"""
Synthetic Equity Option Pricing and Volatility Surface Generation Module

This module provides comprehensive synthetic option pricing capabilities including:
- SABR-based implied volatility surface modeling
- Black-Scholes pricing with put-call parity
- Bid-ask spread calculation with vega and liquidity adjustments
- Arbitrage-free surface generation and validation
- Dynamic surface updates for underlying moves, time decay, and shock scenarios
"""

import numpy as np
import pandas as pd
import math
from scipy.optimize import minimize_scalar, brentq
from scipy.stats import norm
from typing import Dict, List, Tuple, Optional, Union
import logging

logger = logging.getLogger(__name__)


class SABRVolatilitySurface:
    """
    SABR-based implied volatility surface for equity options.
    Implements <PERSON><PERSON>'s approximation for Black implied volatility.
    """
    
    def __init__(self):
        """Initialize SABR volatility surface with default parameters."""
        # Default SABR parameters by expiration (in years)
        self.sabr_params = {
            # Format: expiry_years: {'alpha': ATM_vol, 'beta': elasticity, 'rho': correlation, 'nu': vol_of_vol}
            0.083: {'alpha': 0.20, 'beta': 0.6, 'rho': -0.3, 'nu': 0.4},   # 1 month (~30 days)
            0.25:  {'alpha': 0.22, 'beta': 0.7, 'rho': -0.25, 'nu': 0.35}, # 3 months (~90 days)
            0.5:   {'alpha': 0.24, 'beta': 0.7, 'rho': -0.2, 'nu': 0.3},   # 6 months (~180 days)
            1.0:   {'alpha': 0.26, 'beta': 0.7, 'rho': -0.15, 'nu': 0.25}, # 1 year (~365 days)
            2.0:   {'alpha': 0.28, 'beta': 0.8, 'rho': -0.1, 'nu': 0.2},   # 2 years (~730 days)
            5.0:   {'alpha': 0.25, 'beta': 0.9, 'rho': -0.05, 'nu': 0.15}, # 5 years (~1825 days)
        }
        
        # Configuration
        self.min_vol = 0.01  # Minimum implied volatility (1%)
        self.max_vol = 5.0   # Maximum implied volatility (500%)
        
    def get_sabr_params(self, time_to_expiry: float) -> Dict[str, float]:
        """
        Get SABR parameters for a given time to expiry.
        Interpolates between available expiries if needed.
        
        Args:
            time_to_expiry: Time to expiration in years
            
        Returns:
            Dictionary with SABR parameters (alpha, beta, rho, nu)
        """
        if time_to_expiry <= 0:
            raise ValueError("Time to expiry must be positive")
            
        # Find the closest expiries for interpolation
        expiries = sorted(self.sabr_params.keys())
        
        if time_to_expiry <= expiries[0]:
            # Short extrapolation - use first expiry but adjust for very short terms
            params = self.sabr_params[expiries[0]].copy()
            if time_to_expiry < 0.02:  # Less than 1 week
                # Increase vol-of-vol for very short expiries (gamma risk)
                params['nu'] = min(params['nu'] * 1.2, 1.0)
            return params
        elif time_to_expiry >= expiries[-1]:
            # Long extrapolation - use trend from last two expiries
            if len(expiries) >= 2:
                t1, t2 = expiries[-2], expiries[-1]
                params1, params2 = self.sabr_params[t1], self.sabr_params[t2]

                # Calculate extrapolation factor
                time_beyond = time_to_expiry - t2
                tenor_gap = t2 - t1
                extrap_factor = time_beyond / tenor_gap

                # Dampen extrapolation for very long terms
                damping = math.exp(-extrap_factor * 0.3)  # Exponential damping

                extrapolated = {}
                for key in params1:
                    # Linear trend with damping
                    trend = (params2[key] - params1[key]) * damping
                    extrapolated[key] = params2[key] + trend * extrap_factor

                    # Apply bounds to prevent unrealistic values
                    if key == 'alpha':
                        extrapolated[key] = max(min(extrapolated[key], 1.0), 0.05)
                    elif key == 'beta':
                        extrapolated[key] = max(min(extrapolated[key], 1.0), 0.1)
                    elif key == 'rho':
                        extrapolated[key] = max(min(extrapolated[key], 0.8), -0.8)
                    elif key == 'nu':
                        extrapolated[key] = max(min(extrapolated[key], 1.0), 0.05)

                return extrapolated
            else:
                return self.sabr_params[expiries[-1]].copy()
        else:
            # Linear interpolation between two closest expiries
            for i in range(len(expiries) - 1):
                if expiries[i] <= time_to_expiry <= expiries[i + 1]:
                    t1, t2 = expiries[i], expiries[i + 1]
                    params1, params2 = self.sabr_params[t1], self.sabr_params[t2]
                    
                    # Interpolation weight
                    weight = (time_to_expiry - t1) / (t2 - t1)
                    
                    # Interpolate each parameter
                    interpolated = {}
                    for key in params1:
                        interpolated[key] = params1[key] * (1 - weight) + params2[key] * weight
                    
                    return interpolated
                    
        # Fallback (should not reach here)
        return self.sabr_params[expiries[0]].copy()
    
    def sabr_implied_vol(self, forward: float, strike: float, time_to_expiry: float, 
                        sabr_params: Optional[Dict[str, float]] = None) -> float:
        """
        Calculate SABR implied volatility using Hagan's approximation.
        
        Args:
            forward: Forward price of the underlying
            strike: Strike price
            time_to_expiry: Time to expiration in years
            sabr_params: SABR parameters (if None, uses default for expiry)
            
        Returns:
            Implied volatility
        """
        try:
            if time_to_expiry <= 0:
                return 0.0
                
            if sabr_params is None:
                sabr_params = self.get_sabr_params(time_to_expiry)
                
            alpha = sabr_params['alpha']
            beta = sabr_params['beta']
            rho = sabr_params['rho']
            nu = sabr_params['nu']
            
            # Handle ATM case
            if abs(forward - strike) < 1e-8:
                atm_vol = alpha / (forward ** (1 - beta))
                time_adj = 1 + ((2 - 3 * rho**2) * nu**2 / 24) * time_to_expiry
                return max(atm_vol / time_adj, self.min_vol)
            
            # SABR volatility formula (Hagan's approximation)
            FK = forward * strike
            logFK = math.log(forward / strike)
            
            # Calculate z and x(z)
            z = (nu / alpha) * (FK ** ((1 - beta) / 2)) * logFK
            
            if abs(z) < 1e-8:
                x_z = 1.0
            else:
                sqrt_term = 1 - 2 * rho * z + z * z
                if sqrt_term <= 0:
                    sqrt_term = 1e-8
                x_z = z / math.log((math.sqrt(sqrt_term) - rho + z) / (1 - rho))
            
            # Main volatility components
            numerator = alpha
            denom1 = FK ** ((1 - beta) / 2)
            
            # Volatility smile adjustment terms
            beta_term = (1 - beta)**2 / 24 * (logFK**2)
            rho_term = rho * beta * nu * alpha / (4 * (FK ** ((1 - beta) / 2)))
            nu_term = (2 - 3 * rho**2) * nu**2 / 24
            
            denom2 = 1 + (beta_term + rho_term + nu_term) * time_to_expiry
            
            # Final SABR volatility
            sabr_vol = (numerator / denom1) * x_z / denom2
            
            # Ensure reasonable bounds
            return max(min(sabr_vol, self.max_vol), self.min_vol)
            
        except Exception as e:
            logger.warning(f"Error calculating SABR volatility: {e}")
            return self.get_sabr_params(time_to_expiry)['alpha']  # Fallback to ATM vol

    def sabr_implied_vol_enhanced(self, forward: float, strike: float, time_to_expiry: float) -> float:
        """
        Enhanced SABR implied volatility with robust interpolation and wing damping.

        Args:
            forward: Forward price
            strike: Strike price
            time_to_expiry: Time to expiration in years

        Returns:
            Implied volatility with enhanced interpolation/extrapolation
        """
        if time_to_expiry <= 0:
            return 0.2

        # Calculate moneyness for wing damping
        moneyness = strike / forward

        # Apply wing damping for extreme strikes
        if moneyness < 0.5 or moneyness > 1.8:
            # Get base SABR vol
            base_vol = self.sabr_implied_vol(forward, strike, time_to_expiry)

            # Calculate damping factor
            if moneyness < 0.5:
                damping = min((0.5 - moneyness) / 0.3, 0.8)
            else:
                damping = min((moneyness - 1.8) / 0.5, 0.8)

            # Conservative vol estimate (flatter wings)
            sabr_params = self.get_sabr_params(time_to_expiry)
            conservative_params = sabr_params.copy()
            conservative_params['beta'] = min(conservative_params['beta'] + 0.2, 1.0)
            conservative_params['nu'] = conservative_params['nu'] * 0.7

            conservative_vol = self.sabr_implied_vol(forward, strike, time_to_expiry, conservative_params)

            # Blend the two
            return base_vol * (1 - damping) + conservative_vol * damping
        else:
            # Normal range - use enhanced interpolation
            return self._enhanced_sabr_interpolation(forward, strike, time_to_expiry)

    def _enhanced_sabr_interpolation(self, forward: float, strike: float, time_to_expiry: float) -> float:
        """Enhanced interpolation using variance interpolation."""
        expiries = sorted(self.sabr_params.keys())

        if time_to_expiry <= expiries[0]:
            # Short extrapolation
            vol = self.sabr_implied_vol(forward, strike, time_to_expiry)
            if time_to_expiry < 0.02:  # Very short expiry gamma adjustment
                vol *= (1 + 0.1 * (0.02 - time_to_expiry) / 0.02)
            return vol

        elif time_to_expiry >= expiries[-1]:
            # Long extrapolation with variance dampening
            if len(expiries) >= 2:
                t1, t2 = expiries[-2], expiries[-1]
                vol1 = self.sabr_implied_vol(forward, strike, t1)
                vol2 = self.sabr_implied_vol(forward, strike, t2)

                # Variance extrapolation
                var1, var2 = vol1**2 * t1, vol2**2 * t2
                var_slope = (var2 - var1) / (t2 - t1)

                # Dampen slope for long extrapolation
                damping_factor = math.exp(-(time_to_expiry - expiries[-1]) * 0.5)
                dampened_slope = var_slope * damping_factor

                extrapolated_var = var2 + dampened_slope * (time_to_expiry - t2)
                extrapolated_vol = math.sqrt(max(extrapolated_var / time_to_expiry, 0.01))

                # Converge to long-term vol for very long expiries
                if time_to_expiry > 5.0:
                    long_term_vol = 0.20
                    convergence_weight = min((time_to_expiry - 5.0) / 5.0, 1.0)
                    extrapolated_vol = extrapolated_vol * (1 - convergence_weight) + long_term_vol * convergence_weight

                return extrapolated_vol
            else:
                return self.sabr_implied_vol(forward, strike, time_to_expiry)
        else:
            # Variance interpolation between expiries
            for i in range(len(expiries) - 1):
                if expiries[i] <= time_to_expiry <= expiries[i + 1]:
                    t1, t2 = expiries[i], expiries[i + 1]
                    vol1 = self.sabr_implied_vol(forward, strike, t1)
                    vol2 = self.sabr_implied_vol(forward, strike, t2)

                    # Variance interpolation
                    var1, var2 = vol1**2 * t1, vol2**2 * t2
                    weight = (time_to_expiry - t1) / (t2 - t1)

                    interpolated_var = var1 * (1 - weight) + var2 * weight
                    return math.sqrt(max(interpolated_var / time_to_expiry, 0.01))

        return 0.2  # Fallback

    def set_sabr_params(self, expiry: float, alpha: float, beta: float, rho: float, nu: float):
        """
        Set SABR parameters for a specific expiry.
        
        Args:
            expiry: Time to expiration in years
            alpha: ATM volatility level
            beta: Elasticity parameter (0 <= beta <= 1)
            rho: Correlation parameter (-1 <= rho <= 1)
            nu: Volatility of volatility
        """
        # Validate parameters
        if not (0 < alpha < 2):
            raise ValueError("Alpha must be between 0 and 2")
        if not (0 <= beta <= 1):
            raise ValueError("Beta must be between 0 and 1")
        if not (-1 <= rho <= 1):
            raise ValueError("Rho must be between -1 and 1")
        if not (0 < nu < 2):
            raise ValueError("Nu must be between 0 and 2")
            
        self.sabr_params[expiry] = {
            'alpha': alpha,
            'beta': beta,
            'rho': rho,
            'nu': nu
        }
    
    def calibrate_from_smile(self, expiry: float, forward: float, atm_vol: float,
                           put_25d_vol: float, call_25d_vol: float,
                           additional_points: List[Tuple[float, float]] = None,
                           calibrate_beta: bool = False):
        """
        Enhanced SABR calibration using multiple market points and flexible beta.

        Args:
            expiry: Time to expiration in years
            forward: Forward price
            atm_vol: ATM implied volatility
            put_25d_vol: 25-delta put implied volatility
            call_25d_vol: 25-delta call implied volatility
            additional_points: List of (strike, vol) tuples for additional calibration points
            calibrate_beta: If True, calibrate beta parameter as well
        """
        try:
            from scipy.optimize import minimize, differential_evolution

            # Enhanced delta strike calculation
            def calculate_delta_strike(target_delta: float, vol_guess: float) -> float:
                """Calculate strike for given delta using Black-Scholes formula."""
                for iteration in range(15):  # More iterations for accuracy
                    d1 = (math.log(forward / forward) + 0.5 * vol_guess**2 * expiry) / (vol_guess * math.sqrt(expiry))

                    if abs(target_delta) == 0.25:  # 25-delta
                        if target_delta > 0:  # Call
                            strike = forward * math.exp(-vol_guess * math.sqrt(expiry) * norm.ppf(0.75))
                        else:  # Put
                            strike = forward * math.exp(-vol_guess * math.sqrt(expiry) * norm.ppf(0.25))
                    elif abs(target_delta) == 0.10:  # 10-delta
                        if target_delta > 0:  # Call
                            strike = forward * math.exp(-vol_guess * math.sqrt(expiry) * norm.ppf(0.90))
                        else:  # Put
                            strike = forward * math.exp(-vol_guess * math.sqrt(expiry) * norm.ppf(0.10))
                    else:
                        # General case
                        prob = 0.5 + target_delta if target_delta > 0 else 0.5 + target_delta
                        strike = forward * math.exp(-vol_guess * math.sqrt(expiry) * norm.ppf(prob))

                    # Refine vol guess
                    vol_guess = (vol_guess + atm_vol) / 2

                return strike

            # Build comprehensive target points
            targets = []

            # Core 3-point smile
            atm_strike = forward
            call_25d_strike = calculate_delta_strike(0.25, call_25d_vol)
            put_25d_strike = calculate_delta_strike(-0.25, put_25d_vol)

            targets.extend([
                (put_25d_strike, put_25d_vol),
                (atm_strike, atm_vol),
                (call_25d_strike, call_25d_vol)
            ])

            # Add 10-delta points if we can estimate them
            if expiry > 0.1:  # Only for longer expiries
                # Estimate 10-delta vols from smile curvature
                wing_spread = (put_25d_vol + call_25d_vol) / 2 - atm_vol
                put_10d_vol = put_25d_vol + wing_spread * 0.6  # Steeper for 10D
                call_10d_vol = call_25d_vol + wing_spread * 0.4

                put_10d_strike = calculate_delta_strike(-0.10, put_10d_vol)
                call_10d_strike = calculate_delta_strike(0.10, call_10d_vol)

                targets.extend([
                    (put_10d_strike, put_10d_vol),
                    (call_10d_strike, call_10d_vol)
                ])

            # Add any additional market points
            if additional_points:
                targets.extend(additional_points)

            # Enhanced objective function with regularization
            def objective(params):
                if calibrate_beta:
                    alpha, beta, rho, nu = params
                    # Bounds check for beta
                    if not (0.1 <= beta <= 1.0):
                        return 1e6
                else:
                    alpha, rho, nu = params
                    # Adaptive beta based on expiry
                    if expiry < 0.25:  # Short expiries
                        beta = 0.6
                    elif expiry > 1.0:  # Long expiries
                        beta = 0.8
                    else:
                        beta = 0.7

                # Bounds check
                if not (0.01 <= alpha <= 3.0 and -0.99 <= rho <= 0.99 and 0.01 <= nu <= 2.5):
                    return 1e6

                error = 0.0
                weights = []

                for i, (strike, target_vol) in enumerate(targets):
                    try:
                        model_vol = self._sabr_vol_direct(forward, strike, expiry, alpha, beta, rho, nu)

                        # Weight ATM and 25D points more heavily
                        if i < 3:  # Core points
                            weight = 2.0
                        else:  # Additional points
                            weight = 1.0

                        weights.append(weight)
                        error += weight * (model_vol - target_vol)**2

                    except:
                        return 1e6

                # Add regularization to prevent extreme parameters
                reg_penalty = 0.01 * (nu**2 + rho**2)  # Prefer smaller nu and rho

                return error + reg_penalty

            # Multiple optimization attempts with different starting points
            best_result = None
            best_error = float('inf')

            # Starting points
            if calibrate_beta:
                starting_points = [
                    [atm_vol, 0.7, -0.3, 0.4],
                    [atm_vol * 0.8, 0.6, -0.1, 0.3],
                    [atm_vol * 1.2, 0.8, -0.5, 0.6]
                ]
                bounds = [(0.01, 3.0), (0.1, 1.0), (-0.99, 0.99), (0.01, 2.5)]
            else:
                starting_points = [
                    [atm_vol, -0.3, 0.4],
                    [atm_vol * 0.8, -0.1, 0.3],
                    [atm_vol * 1.2, -0.5, 0.6]
                ]
                bounds = [(0.01, 3.0), (-0.99, 0.99), (0.01, 2.5)]

            # Try multiple starting points
            for x0 in starting_points:
                try:
                    result = minimize(objective, x0, bounds=bounds, method='L-BFGS-B')
                    if result.success and result.fun < best_error:
                        best_result = result
                        best_error = result.fun
                except:
                    continue

            # If standard optimization fails, try differential evolution
            if best_result is None or best_error > 1e-3:
                try:
                    result = differential_evolution(objective, bounds, seed=42, maxiter=100)
                    if result.success and result.fun < best_error:
                        best_result = result
                        best_error = result.fun
                except:
                    pass

            # Apply best result
            if best_result is not None and best_error < 1e-2:
                if calibrate_beta:
                    alpha, beta, rho, nu = best_result.x
                else:
                    alpha, rho, nu = best_result.x
                    beta = 0.7 if expiry <= 1.0 else 0.8

                self.set_sabr_params(expiry, alpha, beta, rho, nu)

                logger.info(f"Enhanced SABR calibration for expiry {expiry}: "
                          f"alpha={alpha:.3f}, beta={beta:.3f}, rho={rho:.3f}, nu={nu:.3f}, "
                          f"error={best_error:.6f}, points={len(targets)}")
            else:
                # Fallback to robust heuristic method
                self._calibrate_robust_heuristic(expiry, forward, atm_vol, put_25d_vol, call_25d_vol)
                logger.warning(f"Enhanced SABR optimization failed for expiry {expiry}, using robust heuristic")

        except ImportError:
            # Scipy not available, use robust heuristic method
            self._calibrate_robust_heuristic(expiry, forward, atm_vol, put_25d_vol, call_25d_vol)
            logger.warning("Scipy not available, using robust heuristic SABR calibration")
        except Exception as e:
            logger.error(f"Error in enhanced SABR calibration: {e}")
            # Fallback to default parameters
            self.set_sabr_params(expiry, atm_vol, 0.7, -0.3, 0.4)

    def _sabr_vol_direct(self, forward: float, strike: float, time_to_expiry: float,
                        alpha: float, beta: float, rho: float, nu: float) -> float:
        """Direct SABR volatility calculation for calibration."""
        if abs(forward - strike) < 1e-8:
            return alpha / (forward ** (1 - beta))

        FK = forward * strike
        logFK = math.log(forward / strike)

        z = (nu / alpha) * (FK ** ((1 - beta) / 2)) * logFK

        if abs(z) < 1e-8:
            x_z = 1.0
        else:
            sqrt_term = max(1 - 2 * rho * z + z * z, 1e-8)
            x_z = z / math.log((math.sqrt(sqrt_term) - rho + z) / (1 - rho))

        numerator = alpha
        denom1 = FK ** ((1 - beta) / 2)

        beta_term = (1 - beta)**2 / 24 * (logFK**2)
        rho_term = rho * beta * nu * alpha / (4 * (FK ** ((1 - beta) / 2)))
        nu_term = (2 - 3 * rho**2) * nu**2 / 24

        denom2 = 1 + (beta_term + rho_term + nu_term) * time_to_expiry

        return (numerator / denom1) * x_z / denom2

    def _calibrate_heuristic(self, expiry: float, forward: float, atm_vol: float,
                           put_25d_vol: float, call_25d_vol: float):
        """Fallback heuristic calibration method."""
        # Calculate 25-delta strikes (approximate)
        d1_25 = norm.ppf(0.75)  # 25-delta call
        d1_25_put = norm.ppf(0.25)  # 25-delta put

        call_25d_strike = forward * math.exp(0.5 * atm_vol**2 * expiry - atm_vol * math.sqrt(expiry) * d1_25)
        put_25d_strike = forward * math.exp(0.5 * atm_vol**2 * expiry - atm_vol * math.sqrt(expiry) * d1_25_put)

        # Simple calibration approach - fit to smile shape
        beta = 0.7

        # Estimate rho from skew
        skew = (call_25d_vol - put_25d_vol) / (call_25d_strike - put_25d_strike) * forward
        rho = max(min(skew * -2.0, 0.8), -0.8)  # Scale and bound

        # Estimate nu from smile curvature
        smile_curvature = (call_25d_vol + put_25d_vol) / 2 - atm_vol
        nu = max(min(smile_curvature * 10.0 + 0.3, 1.5), 0.1)  # Scale and bound

        # Set alpha to match ATM vol
        alpha = atm_vol * (forward ** (1 - beta))

        self.set_sabr_params(expiry, alpha, beta, rho, nu)

    def _calibrate_robust_heuristic(self, expiry: float, forward: float, atm_vol: float,
                                  put_25d_vol: float, call_25d_vol: float):
        """Enhanced heuristic calibration with better parameter estimation."""
        # Adaptive beta based on expiry and market conditions
        if expiry < 0.25:  # Short expiries - more normal behavior
            beta = 0.6
        elif expiry > 1.0:  # Long expiries - more log-normal
            beta = 0.8
        else:
            beta = 0.7

        # More sophisticated rho estimation
        # Use risk reversal (call vol - put vol) and butterfly (wing avg - atm)
        risk_reversal = call_25d_vol - put_25d_vol
        butterfly = (call_25d_vol + put_25d_vol) / 2 - atm_vol

        # Rho correlates with risk reversal, but scale by expiry
        rho_base = -risk_reversal * 5.0  # Base correlation
        rho_expiry_adj = math.sqrt(expiry)  # Longer expiries have less extreme rho
        rho = max(min(rho_base / rho_expiry_adj, 0.8), -0.8)

        # Nu estimation from butterfly and expiry
        # Higher butterfly suggests more vol-of-vol
        nu_base = max(butterfly * 8.0 + 0.2, 0.1)  # Base vol-of-vol
        nu_expiry_adj = 1.0 + expiry * 0.5  # Longer expiries can have higher nu
        nu = min(nu_base * nu_expiry_adj, 2.0)

        # Alpha to match ATM vol with consistency checks
        alpha = atm_vol * (forward ** (1 - beta))

        # Sanity checks and adjustments
        if abs(rho) > 0.9:
            rho = 0.9 * (1 if rho > 0 else -1)
        if nu < 0.1:
            nu = 0.1
        if nu > 2.0:
            nu = 2.0
        if alpha < 0.01:
            alpha = 0.01
        if alpha > 2.0:
            alpha = 2.0

        self.set_sabr_params(expiry, alpha, beta, rho, nu)

        logger.info(f"Robust heuristic SABR for expiry {expiry}: "
                   f"alpha={alpha:.3f}, beta={beta:.3f}, rho={rho:.3f}, nu={nu:.3f}")
        logger.debug(f"Market inputs: RR={risk_reversal:.3f}, BF={butterfly:.3f}")

    def calibrate_to_live_market(self, expiry: float, market_vols: Dict[float, float],
                                forward: float = None) -> Dict[str, float]:
        """
        Calibrate SABR parameters to live market implied volatilities.

        Args:
            expiry: Time to expiry in years
            market_vols: Dictionary of {strike: implied_vol} from live market
            forward: Forward price (calculated if not provided)

        Returns:
            Calibration results with error metrics
        """
        if len(market_vols) < 3:
            return {'error': f'Need at least 3 market points, got {len(market_vols)}'}

        if forward is None:
            forward = 100.0  # Default, should be provided in practice

        # Convert market data to calibration format
        strikes = sorted(market_vols.keys())
        vols = [market_vols[k] for k in strikes]

        # Identify key points for weighted calibration
        atm_strike = min(strikes, key=lambda k: abs(k - forward))
        atm_vol = market_vols[atm_strike]

        # Find 25-delta equivalent points (approximate)
        put_25d_strike = min(strikes, key=lambda k: abs(k - forward * 0.9))
        call_25d_strike = min(strikes, key=lambda k: abs(k - forward * 1.1))

        put_25d_vol = market_vols.get(put_25d_strike, atm_vol * 1.05)
        call_25d_vol = market_vols.get(call_25d_strike, atm_vol * 0.95)

        try:
            # Try enhanced calibration first
            self.calibrate_from_smile(expiry, forward, atm_vol, put_25d_vol, call_25d_vol,
                                    additional_points=[(k, v) for k, v in market_vols.items()],
                                    calibrate_beta=True)

            # Validate calibration quality
            errors = []
            for strike, market_vol in market_vols.items():
                model_vol = self.sabr_implied_vol(forward, strike, expiry)
                error_pct = abs(model_vol - market_vol) / market_vol * 100
                errors.append(error_pct)

            max_error = max(errors)
            avg_error = sum(errors) / len(errors)

            result = {
                'success': True,
                'max_error_pct': max_error,
                'avg_error_pct': avg_error,
                'num_points': len(market_vols),
                'calibrated_params': self.get_sabr_params(expiry)
            }

            logger.info(f"Live SABR calibration for T={expiry:.3f}: "
                       f"avg_error={avg_error:.2f}%, max_error={max_error:.2f}%")

            return result

        except Exception as e:
            logger.error(f"Live SABR calibration failed for expiry {expiry}: {e}")
            return {'error': str(e)}

    def update_from_market_data(self, market_data: Dict[float, Dict[float, float]]) -> Dict[str, any]:
        """
        Update all SABR parameters from comprehensive market data.

        Args:
            market_data: {expiry_years: {strike: implied_vol}}

        Returns:
            Calibration summary across all expiries
        """
        results = {}
        total_errors = []

        for expiry, strike_vols in market_data.items():
            if len(strike_vols) >= 3:
                # Calculate forward for this expiry (simplified)
                forward = 100.0 * math.exp(0.05 * expiry)  # Assume 5% risk-free rate

                calibration_result = self.calibrate_to_live_market(expiry, strike_vols, forward)
                results[f'{expiry:.3f}Y'] = calibration_result

                if calibration_result.get('success'):
                    total_errors.extend([calibration_result['avg_error_pct']])

        # Overall summary
        summary = {
            'calibrated_expiries': len([r for r in results.values() if r.get('success')]),
            'failed_expiries': len([r for r in results.values() if not r.get('success')]),
            'expiry_results': results
        }

        if total_errors:
            summary['overall_avg_error'] = sum(total_errors) / len(total_errors)
            summary['overall_max_error'] = max(total_errors)

        return summary


class BlackScholesEngine:
    """
    Black-Scholes pricing engine for European options.
    Ensures put-call parity and handles edge cases.
    """
    
    @staticmethod
    def black_scholes_call(spot: float, strike: float, time_to_expiry: float, 
                          risk_free_rate: float, dividend_yield: float, volatility: float) -> float:
        """
        Calculate Black-Scholes call option price.
        
        Args:
            spot: Current spot price
            strike: Strike price
            time_to_expiry: Time to expiration in years
            risk_free_rate: Risk-free interest rate
            dividend_yield: Dividend yield
            volatility: Implied volatility
            
        Returns:
            Call option price
        """
        if time_to_expiry <= 0:
            return max(spot - strike, 0)
            
        if volatility <= 0:
            # Handle zero volatility case
            forward = spot * math.exp((risk_free_rate - dividend_yield) * time_to_expiry)
            return max(forward - strike, 0) * math.exp(-risk_free_rate * time_to_expiry)
        
        try:
            # Standard Black-Scholes formula
            d1 = (math.log(spot / strike) + (risk_free_rate - dividend_yield + 0.5 * volatility**2) * time_to_expiry) / (volatility * math.sqrt(time_to_expiry))
            d2 = d1 - volatility * math.sqrt(time_to_expiry)
            
            call_price = (spot * math.exp(-dividend_yield * time_to_expiry) * norm.cdf(d1) - 
                         strike * math.exp(-risk_free_rate * time_to_expiry) * norm.cdf(d2))
            
            return max(call_price, 0)
            
        except Exception as e:
            logger.warning(f"Error in Black-Scholes call calculation: {e}")
            return max(spot - strike, 0)  # Fallback to intrinsic value
    
    @staticmethod
    def black_scholes_put(spot: float, strike: float, time_to_expiry: float, 
                         risk_free_rate: float, dividend_yield: float, volatility: float) -> float:
        """
        Calculate Black-Scholes put option price using put-call parity.
        
        Args:
            spot: Current spot price
            strike: Strike price
            time_to_expiry: Time to expiration in years
            risk_free_rate: Risk-free interest rate
            dividend_yield: Dividend yield
            volatility: Implied volatility
            
        Returns:
            Put option price
        """
        if time_to_expiry <= 0:
            return max(strike - spot, 0)
        
        # Calculate call price first
        call_price = BlackScholesEngine.black_scholes_call(
            spot, strike, time_to_expiry, risk_free_rate, dividend_yield, volatility
        )
        
        # Use put-call parity: Put = Call - Spot*e^(-q*T) + Strike*e^(-r*T)
        forward_spot = spot * math.exp(-dividend_yield * time_to_expiry)
        discounted_strike = strike * math.exp(-risk_free_rate * time_to_expiry)
        
        put_price = call_price - forward_spot + discounted_strike
        
        return max(put_price, 0)
    
    @staticmethod
    def calculate_greeks(spot: float, strike: float, time_to_expiry: float, 
                        risk_free_rate: float, dividend_yield: float, volatility: float) -> Dict[str, float]:
        """
        Calculate option Greeks for risk management.
        
        Returns:
            Dictionary with delta, gamma, theta, vega, rho
        """
        if time_to_expiry <= 0 or volatility <= 0:
            return {'delta': 0, 'gamma': 0, 'theta': 0, 'vega': 0, 'rho': 0}
        
        try:
            sqrt_t = math.sqrt(time_to_expiry)
            d1 = (math.log(spot / strike) + (risk_free_rate - dividend_yield + 0.5 * volatility**2) * time_to_expiry) / (volatility * sqrt_t)
            d2 = d1 - volatility * sqrt_t
            
            # Greeks calculations
            delta = math.exp(-dividend_yield * time_to_expiry) * norm.cdf(d1)
            gamma = math.exp(-dividend_yield * time_to_expiry) * norm.pdf(d1) / (spot * volatility * sqrt_t)
            theta = (-spot * norm.pdf(d1) * volatility * math.exp(-dividend_yield * time_to_expiry) / (2 * sqrt_t) -
                    risk_free_rate * strike * math.exp(-risk_free_rate * time_to_expiry) * norm.cdf(d2) +
                    dividend_yield * spot * math.exp(-dividend_yield * time_to_expiry) * norm.cdf(d1)) / 365
            vega = spot * math.exp(-dividend_yield * time_to_expiry) * norm.pdf(d1) * sqrt_t / 100
            rho = strike * time_to_expiry * math.exp(-risk_free_rate * time_to_expiry) * norm.cdf(d2) / 100
            
            return {
                'delta': delta,
                'gamma': gamma,
                'theta': theta,
                'vega': vega,
                'rho': rho
            }
            
        except Exception as e:
            logger.warning(f"Error calculating Greeks: {e}")
            return {'delta': 0, 'gamma': 0, 'theta': 0, 'vega': 0, 'rho': 0}


class BidAskSpreadCalculator:
    """
    Calculate bid-ask spreads based on vega, liquidity, and market microstructure.
    """

    def __init__(self, base_vol_spread: float = 0.01, min_tick_size: float = 0.01):
        """
        Initialize spread calculator.

        Args:
            base_vol_spread: Base implied volatility spread (e.g., 0.01 = 1.0%)
            min_tick_size: Minimum tick size for option prices
        """
        self.base_vol_spread = base_vol_spread
        self.min_tick_size = min_tick_size

        # Calibrated spread model coefficients (default values)
        self.spread_coefficients = {
            'intercept': 0.005,      # Base spread (0.5%)
            'vega_coeff': 0.02,      # Vega coefficient
            'gamma_coeff': 0.1,      # Gamma coefficient
            'otm_penalty': 0.003,    # OTM penalty
            'time_decay_coeff': 0.5  # Time decay coefficient
        }

        # Market spread observations for calibration
        self.spread_observations = []  # List of (features, observed_spread) tuples

    def get_liquidity_factor(self, spot: float, strike: float, time_to_expiry: float,
                           is_call: bool, current_atm_vol: float = 0.2) -> float:
        """
        Enhanced liquidity factor with ITM/OTM differentiation and vol-level sensitivity.

        Args:
            spot: Current spot price
            strike: Strike price
            time_to_expiry: Time to expiration in years
            is_call: True for call, False for put
            current_atm_vol: Current ATM volatility level

        Returns:
            Liquidity factor (higher = wider spreads)
        """
        moneyness = strike / spot
        days_to_expiry = time_to_expiry * 365

        # Differentiate between ITM and OTM
        if is_call:
            is_itm = strike < spot
        else:
            is_itm = strike > spot

        # Enhanced moneyness factor
        if 0.95 <= moneyness <= 1.05:
            moneyness_factor = 1.0  # ATM - most liquid
        elif 0.9 <= moneyness < 0.95 or 1.05 < moneyness <= 1.1:
            # Near money - differentiate ITM vs OTM
            moneyness_factor = 1.2 if is_itm else 1.4
        elif 0.8 <= moneyness < 0.9 or 1.1 < moneyness <= 1.2:
            moneyness_factor = 1.3 if is_itm else 1.8
        else:
            # Very deep strikes
            moneyness_factor = 1.5 if is_itm else 2.5

        # Enhanced time factor with gamma consideration
        if days_to_expiry < 7:  # Less than 1 week - high gamma risk
            time_factor = 2.0
        elif days_to_expiry < 30:  # Less than 1 month
            time_factor = 1.5
        elif days_to_expiry < 90:  # Less than 3 months
            time_factor = 1.2
        elif days_to_expiry > 365:  # Very long dated
            time_factor = 1.3
        else:
            time_factor = 1.0

        # Volatility level factor - higher vol = wider spreads
        vol_factor = 0.5 + current_atm_vol * 2.5  # Scale with vol level
        vol_factor = max(min(vol_factor, 2.0), 0.8)  # Reasonable bounds

        return moneyness_factor * time_factor * vol_factor

    def calculate_spread(self, mid_price: float, vega: float, spot: float, strike: float,
                        time_to_expiry: float, is_call: bool = True, gamma: float = 0.0,
                        current_atm_vol: float = 0.2) -> Tuple[float, float]:
        """
        Enhanced bid-ask spread calculation with gamma risk and dynamic tick sizing.

        Args:
            mid_price: Mid (theoretical) option price
            vega: Option vega (price sensitivity to 1% vol change)
            spot: Current spot price
            strike: Strike price
            time_to_expiry: Time to expiration in years
            is_call: True for call, False for put
            gamma: Option gamma (delta sensitivity to spot moves)
            current_atm_vol: Current ATM volatility level

        Returns:
            Tuple of (bid_price, ask_price)
        """
        if time_to_expiry <= 0:
            # At expiration, spread is minimal
            intrinsic = max(spot - strike, 0) if is_call else max(strike - spot, 0)
            spread = max(self._get_dynamic_tick_size(mid_price), intrinsic * 0.001)
            return max(intrinsic, mid_price - spread/2), mid_price + spread/2

        # Calculate enhanced liquidity factor
        liquidity_factor = self.get_liquidity_factor(spot, strike, time_to_expiry, is_call, current_atm_vol)

        # Dynamic base vol spread based on market volatility
        dynamic_base_spread = max(self.base_vol_spread, current_atm_vol * 0.02)  # 2% of ATM vol
        vol_spread = dynamic_base_spread * liquidity_factor

        # Convert vol spread to price spread using vega
        vega_spread = vol_spread * abs(vega) * 100  # vega is per 1% vol change

        # Add gamma risk component for high-gamma options
        gamma_spread = 0.0
        if gamma > 0:
            # Gamma risk increases with proximity to ATM and short expiry
            moneyness = abs(math.log(strike / spot))  # Distance from ATM
            gamma_risk_factor = gamma * spot * 0.01  # 1% spot move risk
            gamma_weight = math.exp(-moneyness * 5) * math.exp(-time_to_expiry * 2)  # Weight by ATM-ness and time
            gamma_spread = gamma_risk_factor * gamma_weight * 0.5  # Scale factor

        # Total spread
        total_spread = vega_spread + gamma_spread

        # Enhanced minimum spread logic
        if mid_price < 0.5:
            # Very cheap options - use tick-based minimum
            min_spread = self._get_dynamic_tick_size(mid_price)
        elif mid_price > 10:
            # Expensive options - percentage-based minimum
            min_spread = mid_price * 0.005  # 0.5% minimum
        else:
            # Normal range - hybrid approach
            min_spread = max(self._get_dynamic_tick_size(mid_price), mid_price * 0.01)

        price_spread = max(total_spread, min_spread)

        # Calculate bid and ask
        half_spread = price_spread / 2
        bid_price = mid_price - half_spread
        ask_price = mid_price + half_spread

        # Ensure bid >= intrinsic value
        intrinsic = max(spot - strike, 0) if is_call else max(strike - spot, 0)
        bid_price = max(bid_price, intrinsic)

        # Ensure ask >= bid + min tick
        tick_size = self._get_dynamic_tick_size(mid_price)
        ask_price = max(ask_price, bid_price + tick_size)

        # Round to appropriate tick size
        bid_price = math.floor(bid_price / tick_size) * tick_size
        ask_price = math.ceil(ask_price / tick_size) * tick_size

        return bid_price, ask_price

    def _get_dynamic_tick_size(self, price: float) -> float:
        """
        Get dynamic tick size based on option price level.

        Args:
            price: Option price

        Returns:
            Appropriate tick size
        """
        if price < 3.0:
            return 0.01  # $0.01 for cheap options
        elif price < 10.0:
            return 0.05  # $0.05 for mid-range
        else:
            return 0.10  # $0.10 for expensive options

    def add_spread_observation(self, spot: float, strike: float, time_to_expiry: float,
                              is_call: bool, vega: float, gamma: float, current_atm_vol: float,
                              observed_bid: float, observed_ask: float, theoretical_mid: float):
        """
        Add market spread observation for model calibration.

        Args:
            spot, strike, time_to_expiry, is_call: Option characteristics
            vega, gamma: Greeks
            current_atm_vol: Current ATM volatility
            observed_bid, observed_ask: Market bid/ask prices
            theoretical_mid: Theoretical mid price
        """
        if theoretical_mid <= 0:
            return

        observed_spread = observed_ask - observed_bid
        spread_pct = observed_spread / theoretical_mid

        # Calculate features
        moneyness = strike / spot
        is_otm = (is_call and strike > spot) or (not is_call and strike < spot)
        vega_scaled = vega * current_atm_vol  # Scale vega by vol level
        gamma_risk = gamma * spot * 0.01  # 1% spot move risk
        time_factor = 1.0 / math.sqrt(time_to_expiry) if time_to_expiry > 0 else 10.0

        features = {
            'vega_scaled': vega_scaled,
            'gamma_risk': gamma_risk,
            'otm_indicator': 1.0 if is_otm else 0.0,
            'time_factor': time_factor,
            'vol_level': current_atm_vol
        }

        self.spread_observations.append((features, spread_pct))

    def calibrate_spread_model(self) -> Dict[str, float]:
        """
        Calibrate spread model using regression on market observations.

        Returns:
            Calibration results and new coefficients
        """
        if len(self.spread_observations) < 10:
            return {'error': f'Insufficient observations: {len(self.spread_observations)}'}

        try:
            import numpy as np
            from sklearn.linear_model import LinearRegression

            # Prepare data
            X = []
            y = []

            for features, spread_pct in self.spread_observations:
                X.append([
                    1.0,  # Intercept
                    features['vega_scaled'],
                    features['gamma_risk'],
                    features['otm_indicator'],
                    features['time_factor']
                ])
                y.append(spread_pct)

            X = np.array(X)
            y = np.array(y)

            # Fit regression
            reg = LinearRegression(fit_intercept=False)  # We include intercept manually
            reg.fit(X, y)

            # Update coefficients
            coeffs = reg.coef_
            self.spread_coefficients = {
                'intercept': coeffs[0],
                'vega_coeff': coeffs[1],
                'gamma_coeff': coeffs[2],
                'otm_penalty': coeffs[3],
                'time_decay_coeff': coeffs[4]
            }

            # Calculate R-squared
            y_pred = reg.predict(X)
            ss_res = np.sum((y - y_pred) ** 2)
            ss_tot = np.sum((y - np.mean(y)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

            logger.info(f"Spread model calibrated: R²={r_squared:.3f}, observations={len(self.spread_observations)}")

            return {
                'success': True,
                'r_squared': r_squared,
                'num_observations': len(self.spread_observations),
                'coefficients': self.spread_coefficients.copy()
            }

        except ImportError:
            logger.warning("Sklearn not available for spread calibration")
            return {'error': 'Sklearn not available'}
        except Exception as e:
            logger.error(f"Error calibrating spread model: {e}")
            return {'error': str(e)}

    def calculate_calibrated_spread(self, mid_price: float, vega: float, gamma: float,
                                  spot: float, strike: float, time_to_expiry: float,
                                  is_call: bool, current_atm_vol: float) -> Tuple[float, float]:
        """
        Calculate spread using calibrated model.

        Returns:
            Tuple of (bid_price, ask_price)
        """
        if time_to_expiry <= 0:
            intrinsic = max(spot - strike, 0) if is_call else max(strike - spot, 0)
            spread = max(self._get_dynamic_tick_size(mid_price), intrinsic * 0.001)
            return max(intrinsic, mid_price - spread/2), mid_price + spread/2

        # Calculate features
        moneyness = strike / spot
        is_otm = (is_call and strike > spot) or (not is_call and strike < spot)
        vega_scaled = vega * current_atm_vol
        gamma_risk = gamma * spot * 0.01
        time_factor = 1.0 / math.sqrt(time_to_expiry)

        # Apply calibrated model
        spread_pct = (self.spread_coefficients['intercept'] +
                     self.spread_coefficients['vega_coeff'] * vega_scaled +
                     self.spread_coefficients['gamma_coeff'] * gamma_risk +
                     self.spread_coefficients['otm_penalty'] * (1.0 if is_otm else 0.0) +
                     self.spread_coefficients['time_decay_coeff'] * time_factor)

        # Convert to absolute spread
        spread = max(spread_pct * mid_price, self._get_dynamic_tick_size(mid_price))

        # Calculate bid and ask
        half_spread = spread / 2
        bid_price = mid_price - half_spread
        ask_price = mid_price + half_spread

        # Ensure bid >= intrinsic value
        intrinsic = max(spot - strike, 0) if is_call else max(strike - spot, 0)
        bid_price = max(bid_price, intrinsic)

        # Ensure ask >= bid + min tick
        tick_size = self._get_dynamic_tick_size(mid_price)
        ask_price = max(ask_price, bid_price + tick_size)

        # Round to appropriate tick size
        bid_price = math.floor(bid_price / tick_size) * tick_size
        ask_price = math.ceil(ask_price / tick_size) * tick_size

        return bid_price, ask_price

    def enforce_put_call_parity_spread_level(self, call_mid: float, put_mid: float,
                                           call_half_spread: float, put_half_spread: float,
                                           spot: float, strike: float, time_to_expiry: float,
                                           risk_free_rate: float, dividend_yield: float) -> Tuple[Tuple[float, float], Tuple[float, float]]:
        """
        Enforce put-call parity at the spread level, not price level.
        Calculate clean mid prices independently, then adjust only half-spreads to remove arbitrage.

        Args:
            call_mid, put_mid: Theoretical mid prices (calculated independently)
            call_half_spread, put_half_spread: Half-spreads for each option
            spot: Current spot price
            strike: Strike price
            time_to_expiry: Time to expiration in years
            risk_free_rate: Risk-free rate
            dividend_yield: Dividend yield

        Returns:
            Tuple of ((call_bid, call_ask), (put_bid, put_ask)) with parity-enforced spreads
        """
        if time_to_expiry <= 0:
            call_bid, call_ask = call_mid - call_half_spread, call_mid + call_half_spread
            put_bid, put_ask = put_mid - put_half_spread, put_mid + put_half_spread
            return (call_bid, call_ask), (put_bid, put_ask)

        # Calculate theoretical parity relationship
        forward = spot * math.exp((risk_free_rate - dividend_yield) * time_to_expiry)
        discounted_strike = strike * math.exp(-risk_free_rate * time_to_expiry)
        parity_value = forward - discounted_strike

        # Check if mid prices violate parity (they shouldn't if properly calculated)
        mid_parity_error = (call_mid - put_mid) - parity_value

        if abs(mid_parity_error) > 0.01:  # Significant mid price parity violation
            logger.warning(f"Mid price parity violation: {mid_parity_error:.4f} for strike {strike}")
            # Adjust mids slightly to enforce parity
            call_mid -= mid_parity_error / 2
            put_mid += mid_parity_error / 2

        # Calculate initial bid/ask with original spreads
        call_bid_initial = call_mid - call_half_spread
        call_ask_initial = call_mid + call_half_spread
        put_bid_initial = put_mid - put_half_spread
        put_ask_initial = put_mid + put_half_spread

        # Check parity constraints on bid/ask combinations
        conversion_value = call_bid_initial - put_ask_initial  # Should be <= parity_value
        reversal_value = call_ask_initial - put_bid_initial    # Should be >= parity_value

        # Adjust spreads (not mids) to fix violations
        call_half_spread_adj = call_half_spread
        put_half_spread_adj = put_half_spread

        if conversion_value > parity_value:
            # Conversion arbitrage: need to tighten spreads
            excess = conversion_value - parity_value
            spread_adjustment = excess / 4  # Distribute across both half-spreads
            call_half_spread_adj = max(call_half_spread - spread_adjustment, self.min_tick_size / 2)
            put_half_spread_adj = max(put_half_spread - spread_adjustment, self.min_tick_size / 2)

        if reversal_value < parity_value:
            # Reversal arbitrage: need to widen spreads
            deficit = parity_value - reversal_value
            spread_adjustment = deficit / 4  # Distribute across both half-spreads
            call_half_spread_adj = call_half_spread + spread_adjustment
            put_half_spread_adj = put_half_spread + spread_adjustment

        # Calculate final bid/ask with adjusted spreads
        call_bid = call_mid - call_half_spread_adj
        call_ask = call_mid + call_half_spread_adj
        put_bid = put_mid - put_half_spread_adj
        put_ask = put_mid + put_half_spread_adj

        # Ensure bids are non-negative and maintain minimum spread
        call_bid = max(call_bid, 0.0)
        put_bid = max(put_bid, 0.0)
        call_ask = max(call_ask, call_bid + self.min_tick_size)
        put_ask = max(put_ask, put_bid + self.min_tick_size)

        # Round to tick size
        tick_size = self.min_tick_size
        call_bid = math.floor(call_bid / tick_size) * tick_size
        call_ask = math.ceil(call_ask / tick_size) * tick_size
        put_bid = math.floor(put_bid / tick_size) * tick_size
        put_ask = math.ceil(put_ask / tick_size) * tick_size

        return (call_bid, call_ask), (put_bid, put_ask)

    def enforce_put_call_parity(self, call_bid: float, call_ask: float, put_bid: float, put_ask: float,
                               spot: float, strike: float, time_to_expiry: float,
                               risk_free_rate: float, dividend_yield: float) -> Tuple[Tuple[float, float], Tuple[float, float]]:
        """
        Legacy method - kept for backward compatibility.
        Redirects to spread-level parity enforcement.
        """
        # Extract mids and half-spreads
        call_mid = (call_bid + call_ask) / 2
        put_mid = (put_bid + put_ask) / 2
        call_half_spread = (call_ask - call_bid) / 2
        put_half_spread = (put_ask - put_bid) / 2

        return self.enforce_put_call_parity_spread_level(
            call_mid, put_mid, call_half_spread, put_half_spread,
            spot, strike, time_to_expiry, risk_free_rate, dividend_yield
        )


class ArbitrageChecker:
    """
    Check for and prevent arbitrage opportunities in the volatility surface.
    """

    @staticmethod
    def check_calendar_arbitrage(vol_surface: Dict[float, Dict[float, float]],
                                strikes: List[float]) -> List[str]:
        """
        Check for calendar arbitrage (variance must be non-decreasing with time).

        Args:
            vol_surface: Dictionary {expiry: {strike: vol}}
            strikes: List of strikes to check

        Returns:
            List of arbitrage warnings
        """
        warnings = []
        expiries = sorted(vol_surface.keys())

        for strike in strikes:
            prev_variance = 0
            for i, expiry in enumerate(expiries):
                if strike in vol_surface[expiry]:
                    vol = vol_surface[expiry][strike]
                    variance = vol**2 * expiry

                    if variance < prev_variance:
                        warnings.append(f"Calendar arbitrage at strike {strike}: "
                                      f"variance decreases from {prev_variance:.6f} to {variance:.6f}")

                    prev_variance = variance

        return warnings

    @staticmethod
    def check_butterfly_arbitrage(vols: List[float], strikes: List[float]) -> List[str]:
        """
        Check for butterfly arbitrage (volatility should be convex in strike).

        Args:
            vols: List of volatilities
            strikes: Corresponding strikes

        Returns:
            List of arbitrage warnings
        """
        warnings = []

        if len(vols) < 3:
            return warnings

        for i in range(1, len(vols) - 1):
            # Check convexity condition
            k1, k2, k3 = strikes[i-1], strikes[i], strikes[i+1]
            v1, v2, v3 = vols[i-1], vols[i], vols[i+1]

            # Linear interpolation check
            weight = (k2 - k1) / (k3 - k1)
            interpolated_vol = v1 * (1 - weight) + v3 * weight

            # Volatility should be above the linear interpolation (convex)
            if v2 < interpolated_vol - 0.001:  # Small tolerance
                warnings.append(f"Butterfly arbitrage at strike {k2}: "
                              f"vol {v2:.4f} below interpolated {interpolated_vol:.4f}")

        return warnings

    @staticmethod
    def smooth_volatility_surface(vol_surface: Dict[float, Dict[float, float]],
                                 smoothing_factor: float = 0.1) -> Dict[float, Dict[float, float]]:
        """
        Constraint-based volatility surface smoothing that preserves convexity.

        Args:
            vol_surface: Original volatility surface
            smoothing_factor: Smoothing strength (0 = no smoothing, 1 = heavy smoothing)

        Returns:
            Smoothed and convex volatility surface
        """
        smoothed_surface = {}

        for expiry in vol_surface:
            strikes = sorted(vol_surface[expiry].keys())
            vols = [vol_surface[expiry][k] for k in strikes]

            if len(vols) < 3:
                smoothed_surface[expiry] = vol_surface[expiry].copy()
                continue

            # Apply constraint-based smoothing
            smoothed_vols = ArbitrageChecker._enforce_convexity_constraints(strikes, vols, smoothing_factor)

            smoothed_surface[expiry] = {strikes[i]: smoothed_vols[i] for i in range(len(strikes))}

        return smoothed_surface

    @staticmethod
    def _enforce_convexity_constraints(strikes: List[float], vols: List[float],
                                     smoothing_factor: float) -> List[float]:
        """
        Enforce convexity constraints on volatility smile using iterative adjustment.

        Args:
            strikes: Sorted list of strikes
            vols: Corresponding volatilities
            smoothing_factor: Adjustment strength

        Returns:
            Convexity-adjusted volatilities
        """
        adjusted_vols = vols.copy()
        max_iterations = 20

        for iteration in range(max_iterations):
            violations_fixed = 0

            # Check and fix convexity violations
            for i in range(1, len(strikes) - 1):
                # Calculate second derivative (convexity measure)
                h1 = strikes[i] - strikes[i-1]
                h2 = strikes[i+1] - strikes[i]

                if h1 > 0 and h2 > 0:
                    # Second derivative of volatility w.r.t. strike
                    second_deriv = (adjusted_vols[i+1] - adjusted_vols[i]) / h2 - (adjusted_vols[i] - adjusted_vols[i-1]) / h1

                    # For convexity, second derivative should be >= 0 (smile should be convex)
                    if second_deriv < -1e-6:  # Tolerance for numerical errors
                        # Violation detected - adjust middle point upward
                        min_vol_required = (adjusted_vols[i-1] * h2 + adjusted_vols[i+1] * h1) / (h1 + h2)
                        adjustment = (min_vol_required - adjusted_vols[i]) * smoothing_factor
                        adjusted_vols[i] += max(adjustment, 0.001)  # Minimum 0.1% adjustment
                        violations_fixed += 1

            # Ensure reasonable wing behavior
            # Left wing (puts) - prevent excessive volatility drops
            for i in range(1, len(strikes) // 2):
                if adjusted_vols[i] < adjusted_vols[i-1] - 0.05:  # Max 5% drop per strike
                    adjusted_vols[i] = adjusted_vols[i-1] - 0.02 * smoothing_factor
                    violations_fixed += 1

            # Right wing (calls) - prevent excessive volatility drops
            for i in range(len(strikes) // 2 + 1, len(strikes)):
                if adjusted_vols[i] < adjusted_vols[i-1] - 0.05:
                    adjusted_vols[i] = adjusted_vols[i-1] - 0.02 * smoothing_factor
                    violations_fixed += 1

            if violations_fixed == 0:
                break  # Converged

        return adjusted_vols

    @staticmethod
    def enforce_calendar_arbitrage_free(vol_surface: Dict[float, Dict[float, float]]) -> Dict[float, Dict[float, float]]:
        """
        Enforce that total variance (vol^2 * T) is non-decreasing with time to prevent calendar arbitrage.

        Args:
            vol_surface: Volatility surface {expiry: {strike: vol}}

        Returns:
            Arbitrage-free volatility surface
        """
        corrected_surface = {}
        expiries = sorted(vol_surface.keys())

        if len(expiries) <= 1:
            return vol_surface.copy()

        # Get all strikes across all expiries
        all_strikes = set()
        for expiry_vols in vol_surface.values():
            all_strikes.update(expiry_vols.keys())
        all_strikes = sorted(all_strikes)

        for strike in all_strikes:
            prev_variance = 0

            for expiry in expiries:
                if strike in vol_surface[expiry]:
                    vol = vol_surface[expiry][strike]
                    variance = vol**2 * expiry

                    # Ensure variance is non-decreasing
                    if variance < prev_variance:
                        # Adjust volatility to maintain minimum variance
                        corrected_vol = math.sqrt(prev_variance / expiry) if expiry > 0 else vol
                        variance = prev_variance
                    else:
                        corrected_vol = vol

                    # Store corrected volatility
                    if expiry not in corrected_surface:
                        corrected_surface[expiry] = {}
                    corrected_surface[expiry][strike] = corrected_vol

                    prev_variance = variance

        return corrected_surface

    @staticmethod
    def remove_all_arbitrage(vol_surface: Dict[float, Dict[float, float]],
                           smoothing_factor: float = 0.1) -> Dict[float, Dict[float, float]]:
        """
        Remove both calendar and butterfly arbitrage from the volatility surface.

        Args:
            vol_surface: Original volatility surface
            smoothing_factor: Smoothing strength for butterfly arbitrage removal

        Returns:
            Arbitrage-free volatility surface
        """
        # First remove calendar arbitrage
        calendar_free = ArbitrageChecker.enforce_calendar_arbitrage_free(vol_surface)

        # Then remove butterfly arbitrage
        arbitrage_free = ArbitrageChecker.smooth_volatility_surface(calendar_free, smoothing_factor)

        return arbitrage_free

    @staticmethod
    def fix_negative_densities(vol_surface: Dict[float, Dict[float, float]],
                              spot_price: float, risk_free_rate: float, dividend_yield: float) -> Dict[float, Dict[float, float]]:
        """
        Fix negative risk-neutral densities by adjusting far-wing volatilities.

        Args:
            vol_surface: Volatility surface {expiry: {strike: vol}}
            spot_price: Current spot price
            risk_free_rate: Risk-free rate
            dividend_yield: Dividend yield

        Returns:
            Density-corrected volatility surface
        """
        corrected_surface = {}

        for expiry in vol_surface:
            if expiry <= 0:
                corrected_surface[expiry] = vol_surface[expiry].copy()
                continue

            strikes = sorted(vol_surface[expiry].keys())
            vols = [vol_surface[expiry][k] for k in strikes]

            if len(strikes) < 5:  # Need enough points for density calculation
                corrected_surface[expiry] = vol_surface[expiry].copy()
                continue

            # Calculate forward price
            forward = spot_price * math.exp((risk_free_rate - dividend_yield) * expiry)

            # Check for negative densities and fix them
            corrected_vols = vols.copy()
            max_iterations = 10

            for iteration in range(max_iterations):
                # Calculate call prices for density check
                call_prices = []
                for i, strike in enumerate(strikes):
                    vol = corrected_vols[i]
                    # Simplified Black-Scholes call price
                    if vol > 0 and expiry > 0:
                        d1 = (math.log(spot_price / strike) + (risk_free_rate - dividend_yield + 0.5 * vol**2) * expiry) / (vol * math.sqrt(expiry))
                        d2 = d1 - vol * math.sqrt(expiry)
                        call_price = (spot_price * math.exp(-dividend_yield * expiry) * norm.cdf(d1) -
                                    strike * math.exp(-risk_free_rate * expiry) * norm.cdf(d2))
                    else:
                        call_price = max(spot_price - strike, 0)
                    call_prices.append(max(call_price, 0))

                # Check densities (second derivative of call prices)
                negative_density_found = False

                for i in range(1, len(call_prices) - 1):
                    h = strikes[1] - strikes[0]  # Assume uniform strike spacing
                    second_deriv = (call_prices[i+1] - 2*call_prices[i] + call_prices[i-1]) / (h**2)
                    density = second_deriv * math.exp(risk_free_rate * expiry)

                    if density < -1e-6:  # Negative density detected
                        negative_density_found = True

                        # Fix by increasing volatility at this strike and neighbors
                        vol_adjustment = 0.01  # 1% vol increase

                        # Adjust current and neighboring strikes
                        for j in range(max(0, i-1), min(len(corrected_vols), i+2)):
                            corrected_vols[j] = min(corrected_vols[j] + vol_adjustment, 2.0)  # Cap at 200%

                if not negative_density_found:
                    break  # No more negative densities

            # Store corrected volatilities
            corrected_surface[expiry] = {strikes[i]: corrected_vols[i] for i in range(len(strikes))}

        return corrected_surface

    @staticmethod
    def remove_all_arbitrage_with_density_fix(vol_surface: Dict[float, Dict[float, float]],
                                            spot_price: float, risk_free_rate: float, dividend_yield: float,
                                            smoothing_factor: float = 0.1) -> Dict[float, Dict[float, float]]:
        """
        Remove all arbitrage including negative densities from the volatility surface.

        Args:
            vol_surface: Original volatility surface
            spot_price: Current spot price
            risk_free_rate: Risk-free rate
            dividend_yield: Dividend yield
            smoothing_factor: Smoothing strength for butterfly arbitrage removal

        Returns:
            Completely arbitrage-free volatility surface
        """
        # Step 1: Remove calendar arbitrage
        calendar_free = ArbitrageChecker.enforce_calendar_arbitrage_free(vol_surface)

        # Step 2: Remove butterfly arbitrage
        butterfly_free = ArbitrageChecker.smooth_volatility_surface(calendar_free, smoothing_factor)

        # Step 3: Fix negative densities
        density_free = ArbitrageChecker.fix_negative_densities(
            butterfly_free, spot_price, risk_free_rate, dividend_yield
        )

        return density_free


class SurfaceUpdater:
    """
    Handle dynamic updates to the volatility surface for underlying moves, time decay, and shocks.
    """

    def __init__(self, vol_surface: SABRVolatilitySurface):
        """
        Initialize surface updater.

        Args:
            vol_surface: SABR volatility surface instance
        """
        self.vol_surface = vol_surface
        self.sticky_mode = 'delta'  # 'delta' or 'strike'

    def update_for_underlying_move(self, old_spot: float, new_spot: float,
                                  vol_surface_data: Dict[float, Dict[float, float]]) -> Dict[float, Dict[float, float]]:
        """
        Update volatility surface when underlying price changes.

        Args:
            old_spot: Previous spot price
            new_spot: New spot price
            vol_surface_data: Current volatility surface {expiry: {strike: vol}}

        Returns:
            Updated volatility surface
        """
        if self.sticky_mode == 'strike':
            # Sticky strike: volatilities stay at same absolute strikes
            return vol_surface_data.copy()

        elif self.sticky_mode == 'delta':
            # Sticky delta: volatilities move with relative strikes
            spot_ratio = new_spot / old_spot
            updated_surface = {}

            for expiry in vol_surface_data:
                updated_surface[expiry] = {}
                for old_strike, vol in vol_surface_data[expiry].items():
                    # Scale strike with spot move
                    new_strike = old_strike * spot_ratio
                    updated_surface[expiry][new_strike] = vol

            return updated_surface

        return vol_surface_data.copy()

    def apply_time_decay(self, vol_surface_data: Dict[float, Dict[float, float]],
                        time_elapsed: float) -> Dict[float, Dict[float, float]]:
        """
        Apply time decay to the volatility surface.

        Args:
            vol_surface_data: Current volatility surface
            time_elapsed: Time elapsed in years

        Returns:
            Updated volatility surface with reduced time to expiry
        """
        updated_surface = {}

        for expiry in vol_surface_data:
            new_expiry = max(expiry - time_elapsed, 0.001)  # Minimum 1 day

            if new_expiry > 0:
                # Adjust volatilities for time decay (slight increase for shorter expiries)
                time_factor = math.sqrt(expiry / new_expiry) if new_expiry > 0 else 1.0

                updated_surface[new_expiry] = {}
                for strike, vol in vol_surface_data[expiry].items():
                    # Apply modest vol adjustment for time decay
                    adjusted_vol = vol * (0.95 + 0.05 * time_factor)
                    updated_surface[new_expiry][strike] = adjusted_vol

        return updated_surface

    def apply_shock_scenario(self, vol_surface_data: Dict[float, Dict[float, float]],
                           spot_shock: float, vol_shock: float, current_spot: float, skew_adjustment: float = 0.0) -> Dict[float, Dict[float, float]]:
        """
        Apply shock scenario to the volatility surface.

        Args:
            vol_surface_data: Current volatility surface
            spot_shock: Percentage change in spot price (e.g., -0.2 for -20%)
            vol_shock: Absolute change in ATM volatility (e.g., 0.1 for +10 vol points)
            current_spot: Current spot price for ATM calculation
            skew_adjustment: Additional skew steepening (positive = more negative skew)

        Returns:
            Shocked volatility surface
        """
        shocked_surface = {}

        for expiry in vol_surface_data:
            shocked_surface[expiry] = {}

            # Calculate ATM strike (use current spot price)
            strikes = list(vol_surface_data[expiry].keys())
            atm_strike = min(strikes, key=lambda k: abs(k - current_spot))

            for strike, vol in vol_surface_data[expiry].items():
                # Base vol shock
                shocked_vol = vol + vol_shock

                # Apply skew adjustment based on moneyness
                moneyness = math.log(strike / atm_strike)
                skew_effect = skew_adjustment * moneyness  # More negative for OTM puts

                shocked_vol += skew_effect

                # Ensure reasonable bounds
                shocked_vol = max(min(shocked_vol, 5.0), 0.01)

                shocked_surface[expiry][strike] = shocked_vol

        return shocked_surface


class SyntheticOptionsEngine:
    """
    Main engine for synthetic option pricing and volatility surface management.
    """

    def __init__(self, spot_price: float = 100.0, risk_free_rate: float = 0.05,
                 dividend_yield: float = 0.0, current_date: str = None):
        """
        Initialize synthetic options engine with market data integration.

        Args:
            spot_price: Current spot price of underlying
            risk_free_rate: Risk-free interest rate
            dividend_yield: Dividend yield
            current_date: Current date in 'YYYY-MM-DD' format (defaults to today)
        """
        self.spot_price = spot_price
        self.risk_free_rate = risk_free_rate
        self.dividend_yield = dividend_yield

        # Date management for proper time-to-expiry calculation
        if current_date:
            from datetime import datetime
            self.current_date = datetime.strptime(current_date, '%Y-%m-%d')
        else:
            from datetime import datetime
            self.current_date = datetime.now()

        # Market data integration
        self.market_data_cache = {}  # {(strike, expiry_date): {'bid': float, 'ask': float, 'mid': float}}
        self.dividend_calendar = {}  # {ex_date: dividend_amount}
        self.last_calibration_time = None
        self.calibration_errors = {}  # Track calibration quality

        # Initialize components
        self.vol_surface = SABRVolatilitySurface()
        self.bs_engine = BlackScholesEngine()
        self.spread_calculator = BidAskSpreadCalculator()
        self.arbitrage_checker = ArbitrageChecker()
        self.surface_updater = SurfaceUpdater(self.vol_surface)

        # Cache for performance
        self._option_cache = {}

        # Event and realized vol management
        self.event_calendar = {}  # {date: {'vol_bump': float, 'expiry_threshold': float}}
        self.realized_vol_history = []  # List of (date, realized_vol) tuples
        self.auto_update_enabled = False

        # Calibration settings
        self.calibration_weights = {
            'atm': 3.0,      # Highest weight for ATM
            '25d': 2.0,      # High weight for 25-delta
            '10d': 1.5,      # Medium weight for 10-delta
            'wing': 1.0      # Lower weight for far wings
        }
        self.max_calibration_error = 0.02  # 2% max error threshold
        self.intraday_recalibration_threshold = 0.01  # 1% error triggers recalibration

    def calculate_time_to_expiry(self, expiry_date: str) -> float:
        """
        Calculate proper time to expiry in year fractions.

        Args:
            expiry_date: Expiry date in 'YYYY-MM-DD' format

        Returns:
            Time to expiry in years
        """
        from datetime import datetime
        expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d')
        days_to_expiry = (expiry_dt - self.current_date).days
        return max(days_to_expiry / 365.0, 0.0)

    def add_market_data(self, strike: float, expiry_date: str, bid: float, ask: float, mid: float = None):
        """
        Add real market data for calibration.

        Args:
            strike: Strike price
            expiry_date: Expiry date in 'YYYY-MM-DD' format
            bid: Bid price
            ask: Ask price
            mid: Mid price (calculated if not provided)
        """
        if mid is None:
            mid = (bid + ask) / 2

        key = (strike, expiry_date)
        self.market_data_cache[key] = {
            'bid': bid,
            'ask': ask,
            'mid': mid,
            'timestamp': self.current_date
        }

    def add_dividend(self, ex_date: str, dividend_amount: float):
        """
        Add dividend to calendar for forward price adjustment.

        Args:
            ex_date: Ex-dividend date in 'YYYY-MM-DD' format
            dividend_amount: Dividend amount per share
        """
        self.dividend_calendar[ex_date] = dividend_amount

    def calculate_dividend_adjusted_forward(self, expiry_date: str) -> float:
        """
        Calculate forward price adjusted for discrete dividends.

        Args:
            expiry_date: Expiry date in 'YYYY-MM-DD' format

        Returns:
            Dividend-adjusted forward price
        """
        from datetime import datetime
        expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d')

        # Calculate PV of dividends between now and expiry
        pv_dividends = 0.0
        for ex_date_str, div_amount in self.dividend_calendar.items():
            ex_date = datetime.strptime(ex_date_str, '%Y-%m-%d')
            if self.current_date < ex_date <= expiry_dt:
                days_to_ex = (ex_date - self.current_date).days
                time_to_ex = days_to_ex / 365.0
                pv_dividends += div_amount * math.exp(-self.risk_free_rate * time_to_ex)

        # Forward = (Spot - PV(Dividends)) * exp(r*T)
        time_to_expiry = self.calculate_time_to_expiry(expiry_date)
        adjusted_spot = self.spot_price - pv_dividends
        return adjusted_spot * math.exp(self.risk_free_rate * time_to_expiry)

    def calibrate_to_market_data(self, expiry_date: str, force_recalibration: bool = False) -> Dict[str, float]:
        """
        Calibrate SABR parameters to live market data for a specific expiry.

        Args:
            expiry_date: Expiry date in 'YYYY-MM-DD' format
            force_recalibration: Force recalibration even if recent

        Returns:
            Calibration results and error metrics
        """
        time_to_expiry = self.calculate_time_to_expiry(expiry_date)
        if time_to_expiry <= 0:
            return {'error': 'Expired option'}

        # Get market data for this expiry
        market_points = []
        for (strike, exp_date), data in self.market_data_cache.items():
            if exp_date == expiry_date:
                # Convert market price to implied volatility
                forward = self.calculate_dividend_adjusted_forward(expiry_date)
                try:
                    # Invert Black-Scholes to get observed vol
                    observed_vol = self._invert_black_scholes(
                        data['mid'], self.spot_price, strike, time_to_expiry,
                        self.risk_free_rate, self.dividend_yield, forward
                    )

                    # Determine weight based on moneyness
                    moneyness = strike / forward
                    if 0.95 <= moneyness <= 1.05:
                        weight = self.calibration_weights['atm']
                    elif 0.85 <= moneyness <= 0.95 or 1.05 <= moneyness <= 1.15:
                        weight = self.calibration_weights['25d']
                    elif 0.75 <= moneyness <= 0.85 or 1.15 <= moneyness <= 1.25:
                        weight = self.calibration_weights['10d']
                    else:
                        weight = self.calibration_weights['wing']

                    market_points.append((strike, observed_vol, weight))

                except Exception as e:
                    logger.warning(f"Failed to invert BS for strike {strike}: {e}")

        if len(market_points) < 3:
            return {'error': f'Insufficient market data: {len(market_points)} points'}

        # Perform weighted SABR calibration
        return self._weighted_sabr_calibration(time_to_expiry, forward, market_points)

    def _invert_black_scholes(self, market_price: float, spot: float, strike: float,
                             time_to_expiry: float, risk_free_rate: float, dividend_yield: float,
                             forward: float) -> float:
        """
        Invert Black-Scholes to get implied volatility from market price.

        Args:
            market_price: Observed market price
            spot: Spot price
            strike: Strike price
            time_to_expiry: Time to expiry in years
            risk_free_rate: Risk-free rate
            dividend_yield: Dividend yield
            forward: Forward price

        Returns:
            Implied volatility
        """
        # Determine if call or put based on intrinsic value
        call_intrinsic = max(forward * math.exp(-risk_free_rate * time_to_expiry) -
                           strike * math.exp(-risk_free_rate * time_to_expiry), 0)
        put_intrinsic = max(strike * math.exp(-risk_free_rate * time_to_expiry) -
                          forward * math.exp(-risk_free_rate * time_to_expiry), 0)

        is_call = abs(market_price - call_intrinsic) < abs(market_price - put_intrinsic)

        # Newton-Raphson iteration for implied vol
        vol = 0.2  # Initial guess
        for iteration in range(50):
            if is_call:
                price = self.bs_engine.black_scholes_call(spot, strike, time_to_expiry,
                                                        risk_free_rate, dividend_yield, vol)
            else:
                price = self.bs_engine.black_scholes_put(spot, strike, time_to_expiry,
                                                       risk_free_rate, dividend_yield, vol)

            # Calculate vega for Newton-Raphson
            if vol > 0 and time_to_expiry > 0:
                d1 = (math.log(spot / strike) + (risk_free_rate - dividend_yield + 0.5 * vol**2) * time_to_expiry) / (vol * math.sqrt(time_to_expiry))
                vega = spot * math.exp(-dividend_yield * time_to_expiry) * norm.pdf(d1) * math.sqrt(time_to_expiry)
            else:
                vega = 0.01  # Small value to avoid division by zero

            price_diff = price - market_price
            if abs(price_diff) < 1e-6:
                break

            if vega > 1e-6:
                vol -= price_diff / vega
                vol = max(min(vol, 5.0), 0.001)  # Keep vol in reasonable bounds
            else:
                break

        return max(min(vol, 5.0), 0.001)

    def _weighted_sabr_calibration(self, time_to_expiry: float, forward: float,
                                 market_points: List[Tuple[float, float, float]]) -> Dict[str, float]:
        """
        Perform weighted SABR calibration to multiple market points.

        Args:
            time_to_expiry: Time to expiry in years
            forward: Forward price
            market_points: List of (strike, observed_vol, weight) tuples

        Returns:
            Calibration results with error metrics
        """
        try:
            from scipy.optimize import minimize, differential_evolution

            def weighted_objective(params):
                alpha, beta, rho, nu = params

                # Parameter bounds check
                if not (0.01 <= alpha <= 3.0 and 0.1 <= beta <= 1.0 and
                       -0.99 <= rho <= 0.99 and 0.01 <= nu <= 2.5):
                    return 1e6

                total_error = 0.0
                total_weight = 0.0

                for strike, observed_vol, weight in market_points:
                    try:
                        model_vol = self.vol_surface._sabr_vol_direct(forward, strike, time_to_expiry,
                                                                    alpha, beta, rho, nu)
                        error = (model_vol - observed_vol)**2
                        total_error += weight * error
                        total_weight += weight
                    except:
                        return 1e6

                # Weighted average error
                if total_weight > 0:
                    avg_error = total_error / total_weight
                else:
                    avg_error = 1e6

                # Add regularization
                reg_penalty = 0.01 * (nu**2 + rho**2)

                return avg_error + reg_penalty

            # Multiple starting points for robustness
            best_result = None
            best_error = float('inf')

            starting_points = [
                [0.2, 0.7, -0.3, 0.4],
                [0.15, 0.6, -0.1, 0.3],
                [0.25, 0.8, -0.5, 0.6],
                [0.3, 0.5, 0.1, 0.5]
            ]

            bounds = [(0.01, 3.0), (0.1, 1.0), (-0.99, 0.99), (0.01, 2.5)]

            for x0 in starting_points:
                try:
                    result = minimize(weighted_objective, x0, bounds=bounds, method='L-BFGS-B')
                    if result.success and result.fun < best_error:
                        best_result = result
                        best_error = result.fun
                except:
                    continue

            # Try differential evolution if local optimization fails
            if best_result is None or best_error > 0.01:
                try:
                    result = differential_evolution(weighted_objective, bounds, seed=42, maxiter=100)
                    if result.success and result.fun < best_error:
                        best_result = result
                        best_error = result.fun
                except:
                    pass

            if best_result is not None:
                alpha, beta, rho, nu = best_result.x

                # Update SABR parameters
                self.vol_surface.set_sabr_params(time_to_expiry, alpha, beta, rho, nu)

                # Calculate individual errors for monitoring
                individual_errors = []
                for strike, observed_vol, weight in market_points:
                    model_vol = self.vol_surface._sabr_vol_direct(forward, strike, time_to_expiry,
                                                                alpha, beta, rho, nu)
                    error_pct = abs(model_vol - observed_vol) / observed_vol * 100
                    individual_errors.append(error_pct)

                max_error = max(individual_errors) if individual_errors else 0
                avg_error = sum(individual_errors) / len(individual_errors) if individual_errors else 0

                # Store calibration quality
                self.calibration_errors[time_to_expiry] = {
                    'max_error_pct': max_error,
                    'avg_error_pct': avg_error,
                    'num_points': len(market_points),
                    'timestamp': self.current_date
                }

                logger.info(f"SABR calibration successful for T={time_to_expiry:.3f}: "
                          f"α={alpha:.3f}, β={beta:.3f}, ρ={rho:.3f}, ν={nu:.3f}, "
                          f"avg_error={avg_error:.2f}%, max_error={max_error:.2f}%")

                return {
                    'success': True,
                    'alpha': alpha, 'beta': beta, 'rho': rho, 'nu': nu,
                    'avg_error_pct': avg_error,
                    'max_error_pct': max_error,
                    'num_points': len(market_points)
                }
            else:
                return {'success': False, 'error': 'Optimization failed'}

        except ImportError:
            logger.warning("Scipy not available for weighted calibration")
            return {'success': False, 'error': 'Scipy not available'}
        except Exception as e:
            logger.error(f"Error in weighted SABR calibration: {e}")
            return {'success': False, 'error': str(e)}

    def monitor_pricing_errors(self, expiry_date: str) -> Dict[str, float]:
        """
        Monitor pricing errors against market data and trigger recalibration if needed.

        Args:
            expiry_date: Expiry date to monitor

        Returns:
            Error metrics and recalibration status
        """
        time_to_expiry = self.calculate_time_to_expiry(expiry_date)
        if time_to_expiry <= 0:
            return {'error': 'Expired option'}

        # Get current market data
        errors = []
        forward = self.calculate_dividend_adjusted_forward(expiry_date)

        for (strike, exp_date), data in self.market_data_cache.items():
            if exp_date == expiry_date:
                try:
                    # Get model price
                    model_vol = self.vol_surface.sabr_implied_vol_enhanced(forward, strike, time_to_expiry)
                    model_price_call = self.bs_engine.black_scholes_call(
                        self.spot_price, strike, time_to_expiry,
                        self.risk_free_rate, self.dividend_yield, model_vol
                    )
                    model_price_put = self.bs_engine.black_scholes_put(
                        self.spot_price, strike, time_to_expiry,
                        self.risk_free_rate, self.dividend_yield, model_vol
                    )

                    # Compare with market mid (assume it's for the closer-to-ATM option)
                    market_mid = data['mid']
                    call_error = abs(model_price_call - market_mid) / market_mid
                    put_error = abs(model_price_put - market_mid) / market_mid

                    # Use the smaller error (better fit)
                    error_pct = min(call_error, put_error) * 100
                    errors.append(error_pct)

                except Exception as e:
                    logger.warning(f"Error monitoring strike {strike}: {e}")

        if not errors:
            return {'error': 'No market data available'}

        max_error = max(errors)
        avg_error = sum(errors) / len(errors)

        # Check if recalibration is needed
        needs_recalibration = max_error > self.intraday_recalibration_threshold * 100

        result = {
            'max_error_pct': max_error,
            'avg_error_pct': avg_error,
            'num_points': len(errors),
            'needs_recalibration': needs_recalibration,
            'recalibration_threshold': self.intraday_recalibration_threshold * 100
        }

        # Trigger recalibration if needed
        if needs_recalibration:
            logger.info(f"Triggering intraday recalibration for {expiry_date}: max_error={max_error:.2f}%")
            calibration_result = self.calibrate_to_market_data(expiry_date, force_recalibration=True)
            result['recalibration_result'] = calibration_result

        return result

    def get_calibration_dashboard(self) -> Dict[str, any]:
        """
        Get comprehensive calibration quality dashboard.

        Returns:
            Dashboard with calibration metrics across all expiries
        """
        dashboard = {
            'last_update': self.current_date.strftime('%Y-%m-%d %H:%M:%S'),
            'market_data_points': len(self.market_data_cache),
            'calibrated_expiries': len(self.calibration_errors),
            'expiry_details': {}
        }

        for expiry, error_data in self.calibration_errors.items():
            dashboard['expiry_details'][f'{expiry:.3f}Y'] = {
                'max_error_pct': error_data['max_error_pct'],
                'avg_error_pct': error_data['avg_error_pct'],
                'num_points': error_data['num_points'],
                'last_calibration': error_data['timestamp'].strftime('%Y-%m-%d %H:%M:%S'),
                'quality_grade': self._get_quality_grade(error_data['max_error_pct'])
            }

        # Overall quality assessment
        if self.calibration_errors:
            all_max_errors = [e['max_error_pct'] for e in self.calibration_errors.values()]
            dashboard['overall_max_error'] = max(all_max_errors)
            dashboard['overall_quality'] = self._get_quality_grade(max(all_max_errors))
        else:
            dashboard['overall_max_error'] = None
            dashboard['overall_quality'] = 'No calibration data'

        return dashboard

    def _get_quality_grade(self, max_error_pct: float) -> str:
        """Get quality grade based on maximum error."""
        if max_error_pct <= 1.0:
            return 'Excellent'
        elif max_error_pct <= 2.0:
            return 'Good'
        elif max_error_pct <= 5.0:
            return 'Fair'
        else:
            return 'Poor'

    def get_option_quote(self, strike: float, expiry: float, is_call: bool = True,
                        enforce_parity: bool = True) -> Dict[str, float]:
        """
        Get complete option quote with bid, mid, ask, and Greeks.
        Automatically enforces put-call parity on bid/ask spreads by default.

        Args:
            strike: Strike price
            expiry: Time to expiration in years
            is_call: True for call, False for put
            enforce_parity: If True, enforces put-call parity constraints

        Returns:
            Dictionary with bid, mid, ask, implied_vol, and Greeks
        """
        try:
            if enforce_parity:
                # Get both call and put quotes and enforce parity
                call_quote, put_quote = self.get_parity_enforced_quotes(strike, expiry)
                return call_quote if is_call else put_quote

            # Get implied volatility using enhanced interpolation
            forward = self.spot_price * math.exp((self.risk_free_rate - self.dividend_yield) * expiry)
            implied_vol = self.vol_surface.sabr_implied_vol_enhanced(forward, strike, expiry)

            # Calculate mid price
            if is_call:
                mid_price = self.bs_engine.black_scholes_call(
                    self.spot_price, strike, expiry, self.risk_free_rate, self.dividend_yield, implied_vol
                )
            else:
                mid_price = self.bs_engine.black_scholes_put(
                    self.spot_price, strike, expiry, self.risk_free_rate, self.dividend_yield, implied_vol
                )

            # Calculate Greeks
            greeks = self.bs_engine.calculate_greeks(
                self.spot_price, strike, expiry, self.risk_free_rate, self.dividend_yield, implied_vol
            )

            # Calculate enhanced bid-ask spread with gamma and vol-level sensitivity
            bid_price, ask_price = self.spread_calculator.calculate_spread(
                mid_price, greeks['vega'], self.spot_price, strike, expiry, is_call,
                greeks['gamma'], implied_vol
            )

            return {
                'strike': strike,
                'expiry': expiry,
                'is_call': is_call,
                'bid': bid_price,
                'mid': mid_price,
                'ask': ask_price,
                'implied_vol': implied_vol,
                'delta': greeks['delta'],
                'gamma': greeks['gamma'],
                'theta': greeks['theta'],
                'vega': greeks['vega'],
                'rho': greeks['rho']
            }

        except Exception as e:
            logger.error(f"Error calculating option quote for {strike} strike, {expiry} expiry: {e}")
            return {
                'strike': strike, 'expiry': expiry, 'is_call': is_call,
                'bid': 0.0, 'mid': 0.0, 'ask': 0.0, 'implied_vol': 0.2,
                'delta': 0.0, 'gamma': 0.0, 'theta': 0.0, 'vega': 0.0, 'rho': 0.0
            }

    def get_parity_enforced_quotes(self, strike: float, expiry: float) -> Tuple[Dict[str, float], Dict[str, float]]:
        """
        Get call and put quotes with put-call parity enforced at the spread level.

        Args:
            strike: Strike price
            expiry: Time to expiration in years

        Returns:
            Tuple of (call_quote, put_quote) with parity-enforced bid/ask
        """
        # Get individual quotes first (without parity enforcement to avoid recursion)
        call_quote = self.get_option_quote(strike, expiry, is_call=True, enforce_parity=False)
        put_quote = self.get_option_quote(strike, expiry, is_call=False, enforce_parity=False)

        # Extract mids and calculate half-spreads
        call_mid = call_quote['mid']
        put_mid = put_quote['mid']
        call_half_spread = (call_quote['ask'] - call_quote['bid']) / 2
        put_half_spread = (put_quote['ask'] - put_quote['bid']) / 2

        # Enforce put-call parity at spread level
        (call_bid, call_ask), (put_bid, put_ask) = self.spread_calculator.enforce_put_call_parity_spread_level(
            call_mid, put_mid, call_half_spread, put_half_spread,
            self.spot_price, strike, expiry, self.risk_free_rate, self.dividend_yield
        )

        # Update quotes with parity-enforced prices
        call_quote.update({'bid': call_bid, 'ask': call_ask})
        put_quote.update({'bid': put_bid, 'ask': put_ask})

        return call_quote, put_quote

    def get_option_quote_by_date(self, strike: float, expiry_date: str, is_call: bool = True) -> Dict[str, float]:
        """
        Get option quote using expiry date string (proper time-to-expiry conversion).

        Args:
            strike: Strike price
            expiry_date: Expiry date in 'YYYY-MM-DD' format
            is_call: True for call, False for put

        Returns:
            Option quote dictionary
        """
        # Convert expiry date to proper time-to-expiry in years
        time_to_expiry = self.calculate_time_to_expiry(expiry_date)

        if time_to_expiry <= 0:
            # Expired option
            intrinsic = max(self.spot_price - strike, 0) if is_call else max(strike - self.spot_price, 0)
            return {
                'strike': strike, 'expiry_date': expiry_date, 'time_to_expiry': 0.0, 'is_call': is_call,
                'bid': intrinsic, 'mid': intrinsic, 'ask': intrinsic, 'implied_vol': 0.0,
                'delta': 1.0 if (is_call and strike < self.spot_price) else 0.0,
                'gamma': 0.0, 'theta': 0.0, 'vega': 0.0, 'rho': 0.0
            }

        # Get quote using proper time-to-expiry
        quote = self.get_option_quote(strike, time_to_expiry, is_call)

        # Add date information
        quote['expiry_date'] = expiry_date
        quote['time_to_expiry'] = time_to_expiry
        quote['days_to_expiry'] = int(time_to_expiry * 365)

        return quote

    def demonstrate_time_conversion_fix(self) -> Dict[str, any]:
        """
        Demonstrate the proper time-to-expiry conversion fix.

        Returns:
            Examples showing days -> years conversion hitting SABR buckets
        """
        examples = []

        # Test various expiry dates
        test_dates = [
            ('2024-02-14', '30 days'),   # Should hit 0.083 bucket (1 month)
            ('2024-04-15', '90 days'),   # Should hit 0.25 bucket (3 months)
            ('2024-07-15', '180 days'),  # Should hit 0.5 bucket (6 months)
            ('2025-01-15', '365 days'),  # Should hit 1.0 bucket (1 year)
            ('2026-01-15', '730 days'),  # Should hit 2.0 bucket (2 years)
            ('2029-01-15', '1825 days'), # Should hit 5.0 bucket (5 years)
        ]

        for expiry_date, description in test_dates:
            time_to_expiry = self.calculate_time_to_expiry(expiry_date)

            # Get SABR parameters for this expiry
            sabr_params = self.vol_surface.get_sabr_params(time_to_expiry)

            examples.append({
                'expiry_date': expiry_date,
                'description': description,
                'time_to_expiry_years': round(time_to_expiry, 4),
                'days_calculated': int(time_to_expiry * 365),
                'sabr_alpha': round(sabr_params['alpha'], 3),
                'sabr_beta': round(sabr_params['beta'], 3),
                'sabr_rho': round(sabr_params['rho'], 3),
                'sabr_nu': round(sabr_params['nu'], 3)
            })

        return {
            'current_date': self.current_date.strftime('%Y-%m-%d'),
            'examples': examples,
            'sabr_buckets': list(self.vol_surface.sabr_params.keys()),
            'message': 'Time-to-expiry now properly converts days to years, hitting SABR buckets correctly'
        }

    def get_option_chain(self, strikes: List[float], expiries: List[float]) -> pd.DataFrame:
        """
        Generate complete option chain for given strikes and expiries.
        Uses single parity enforcement per strike-expiry to ensure matched call/put pairs.

        Args:
            strikes: List of strike prices
            expiries: List of expiration times in years

        Returns:
            DataFrame with option quotes where call/put pairs are properly matched
        """
        quotes = []

        for expiry in expiries:
            for strike in strikes:
                # Get matched call/put pair in one parity enforcement pass
                call_quote, put_quote = self.get_parity_enforced_quotes(strike, expiry)

                # Add option type identifiers
                call_quote['option_type'] = 'call'
                put_quote['option_type'] = 'put'

                # Add both to quotes list
                quotes.extend([call_quote, put_quote])

        return pd.DataFrame(quotes)

    def verify_option_chain_parity(self, option_chain_df: pd.DataFrame) -> Dict[str, any]:
        """
        Verify put-call parity consistency across the option chain.

        Args:
            option_chain_df: DataFrame from get_option_chain()

        Returns:
            Parity verification results with error statistics
        """
        parity_errors = []

        # Group by strike and expiry to get matched pairs
        grouped = option_chain_df.groupby(['strike', 'expiry'])

        for (strike, expiry), group in grouped:
            if len(group) == 2:  # Should have exactly one call and one put
                call_row = group[group['option_type'] == 'call'].iloc[0]
                put_row = group[group['option_type'] == 'put'].iloc[0]

                # Calculate theoretical parity value
                forward = self.spot_price * math.exp((self.risk_free_rate - self.dividend_yield) * expiry)
                discounted_strike = strike * math.exp(-self.risk_free_rate * expiry)
                theoretical_parity = forward - discounted_strike

                # Check parity for mid prices
                actual_parity_mid = call_row['mid'] - put_row['mid']
                mid_error = abs(actual_parity_mid - theoretical_parity)

                # Check parity constraints for bid/ask
                conversion_value = call_row['bid'] - put_row['ask']  # Should be <= theoretical_parity
                reversal_value = call_row['ask'] - put_row['bid']    # Should be >= theoretical_parity

                conversion_violation = max(0, conversion_value - theoretical_parity)
                reversal_violation = max(0, theoretical_parity - reversal_value)

                parity_errors.append({
                    'strike': strike,
                    'expiry': expiry,
                    'mid_error': mid_error,
                    'conversion_violation': conversion_violation,
                    'reversal_violation': reversal_violation,
                    'call_spread': call_row['ask'] - call_row['bid'],
                    'put_spread': put_row['ask'] - put_row['bid']
                })

        if not parity_errors:
            return {'error': 'No matched call/put pairs found'}

        # Calculate summary statistics
        mid_errors = [e['mid_error'] for e in parity_errors]
        conversion_violations = [e['conversion_violation'] for e in parity_errors]
        reversal_violations = [e['reversal_violation'] for e in parity_errors]

        summary = {
            'total_pairs': len(parity_errors),
            'mid_parity_errors': {
                'max': max(mid_errors),
                'avg': sum(mid_errors) / len(mid_errors),
                'count_above_1cent': sum(1 for e in mid_errors if e > 0.01)
            },
            'arbitrage_violations': {
                'conversion_violations': sum(1 for v in conversion_violations if v > 0.001),
                'reversal_violations': sum(1 for v in reversal_violations if v > 0.001),
                'max_conversion_violation': max(conversion_violations),
                'max_reversal_violation': max(reversal_violations)
            },
            'spread_consistency': {
                'call_spreads': [e['call_spread'] for e in parity_errors],
                'put_spreads': [e['put_spread'] for e in parity_errors]
            }
        }

        # Overall assessment
        if summary['mid_parity_errors']['max'] < 0.01 and \
           summary['arbitrage_violations']['conversion_violations'] == 0 and \
           summary['arbitrage_violations']['reversal_violations'] == 0:
            summary['overall_quality'] = 'Excellent - No parity violations'
        elif summary['mid_parity_errors']['max'] < 0.05:
            summary['overall_quality'] = 'Good - Minor parity errors'
        else:
            summary['overall_quality'] = 'Poor - Significant parity violations'

        return summary

    def get_bulk_quotes(self, strikes: np.ndarray, expiries: np.ndarray,
                       is_calls: np.ndarray) -> np.ndarray:
        """
        Vectorized bulk option pricing for high-frequency applications.
        Note: For parity-consistent pricing, use get_bulk_quotes_parity_aware() instead.

        Args:
            strikes: Array of strike prices
            expiries: Array of expiration times (same length as strikes)
            is_calls: Array of booleans (True for calls, False for puts)

        Returns:
            Structured numpy array with bid, mid, ask, iv, delta, vega
        """
        n = len(strikes)

        # Pre-allocate result array
        dtype = [('strike', 'f8'), ('expiry', 'f8'), ('is_call', 'bool'),
                ('bid', 'f8'), ('mid', 'f8'), ('ask', 'f8'), ('iv', 'f8'),
                ('delta', 'f8'), ('gamma', 'f8'), ('theta', 'f8'), ('vega', 'f8'), ('rho', 'f8')]

        results = np.zeros(n, dtype=dtype)

        # Vectorized calculations where possible
        forwards = self.spot_price * np.exp((self.risk_free_rate - self.dividend_yield) * expiries)

        for i in range(n):
            try:
                strike, expiry, is_call = strikes[i], expiries[i], is_calls[i]
                forward = forwards[i]

                # Get implied volatility
                implied_vol = self.vol_surface.sabr_implied_vol(forward, strike, expiry)

                # Calculate mid price
                if is_call:
                    mid_price = self.bs_engine.black_scholes_call(
                        self.spot_price, strike, expiry, self.risk_free_rate, self.dividend_yield, implied_vol
                    )
                else:
                    mid_price = self.bs_engine.black_scholes_put(
                        self.spot_price, strike, expiry, self.risk_free_rate, self.dividend_yield, implied_vol
                    )

                # Calculate Greeks
                greeks = self.bs_engine.calculate_greeks(
                    self.spot_price, strike, expiry, self.risk_free_rate, self.dividend_yield, implied_vol
                )

                # Calculate bid-ask spread
                bid_price, ask_price = self.spread_calculator.calculate_spread(
                    mid_price, greeks['vega'], self.spot_price, strike, expiry, is_call
                )

                # Store results
                results[i] = (strike, expiry, is_call, bid_price, mid_price, ask_price, implied_vol,
                            greeks['delta'], greeks['gamma'], greeks['theta'], greeks['vega'], greeks['rho'])

            except Exception as e:
                logger.warning(f"Error in bulk quote {i}: {e}")
                results[i] = (strike, expiry, is_call, 0.0, 0.0, 0.0, 0.2, 0.0, 0.0, 0.0, 0.0, 0.0)

        return results

    def get_bulk_quotes_parity_aware(self, strikes: List[float], expiries: List[float]) -> np.ndarray:
        """
        Bulk option pricing with proper parity enforcement for matched call/put pairs.

        Args:
            strikes: List of strike prices
            expiries: List of expiration times in years

        Returns:
            Structured numpy array with matched call/put pairs (parity-enforced)
        """
        # Calculate total number of quotes (2 per strike-expiry combination)
        n_pairs = len(strikes) * len(expiries)
        n_total = n_pairs * 2  # calls + puts

        # Pre-allocate result array
        dtype = [('strike', 'f8'), ('expiry', 'f8'), ('is_call', 'bool'),
                ('bid', 'f8'), ('mid', 'f8'), ('ask', 'f8'), ('iv', 'f8'),
                ('delta', 'f8'), ('gamma', 'f8'), ('theta', 'f8'), ('vega', 'f8'), ('rho', 'f8')]

        results = np.zeros(n_total, dtype=dtype)

        idx = 0
        for expiry in expiries:
            for strike in strikes:
                try:
                    # Get matched call/put pair with single parity enforcement
                    call_quote, put_quote = self.get_parity_enforced_quotes(strike, expiry)

                    # Store call quote
                    results[idx] = (
                        strike, expiry, True,
                        call_quote['bid'], call_quote['mid'], call_quote['ask'], call_quote['implied_vol'],
                        call_quote['delta'], call_quote['gamma'], call_quote['theta'],
                        call_quote['vega'], call_quote['rho']
                    )
                    idx += 1

                    # Store put quote
                    results[idx] = (
                        strike, expiry, False,
                        put_quote['bid'], put_quote['mid'], put_quote['ask'], put_quote['implied_vol'],
                        put_quote['delta'], put_quote['gamma'], put_quote['theta'],
                        put_quote['vega'], put_quote['rho']
                    )
                    idx += 1

                except Exception as e:
                    logger.warning(f"Error in parity-aware bulk quote for {strike}/{expiry}: {e}")
                    # Fill with default values for both call and put
                    for is_call in [True, False]:
                        results[idx] = (strike, expiry, is_call, 0.0, 0.0, 0.0, 0.2, 0.0, 0.0, 0.0, 0.0, 0.0)
                        idx += 1

        return results

    def get_streaming_quotes(self, strikes: List[float], expiries: List[float],
                           update_callback=None) -> Dict[Tuple[float, float], Dict[str, float]]:
        """
        Streaming API for real-time option quotes with callback support.

        Args:
            strikes: List of strikes to monitor
            expiries: List of expiries to monitor
            update_callback: Function called when quotes update

        Returns:
            Dictionary of {(strike, expiry): {call_quote, put_quote}}
        """
        quotes = {}

        for expiry in expiries:
            for strike in strikes:
                try:
                    # Get parity-enforced quotes
                    call_quote, put_quote = self.get_parity_enforced_quotes(strike, expiry)

                    key = (strike, expiry)
                    quotes[key] = {
                        'call': call_quote,
                        'put': put_quote,
                        'timestamp': pd.Timestamp.now()
                    }

                    # Call update callback if provided
                    if update_callback:
                        update_callback(key, quotes[key])

                except Exception as e:
                    logger.warning(f"Error in streaming quote for {strike}/{expiry}: {e}")

        return quotes

    def update_spot_price(self, new_spot: float, sticky_mode: str = 'delta'):
        """
        Update underlying spot price and adjust volatility surface using SurfaceUpdater.

        Args:
            new_spot: New spot price
            sticky_mode: 'delta' or 'strike' for surface adjustment
        """
        old_spot = self.spot_price
        self.spot_price = new_spot
        self.surface_updater.sticky_mode = sticky_mode

        # Build current volatility surface data
        vol_surface_data = self._build_vol_surface_data()

        # Apply surface update for underlying move
        updated_surface = self.surface_updater.update_for_underlying_move(
            old_spot, new_spot, vol_surface_data
        )

        # Remove any arbitrage introduced by the update (including negative densities)
        arbitrage_free_surface = ArbitrageChecker.remove_all_arbitrage_with_density_fix(
            updated_surface, self.spot_price, self.risk_free_rate, self.dividend_yield
        )

        # Update SABR parameters based on the arbitrage-free surface
        self._update_sabr_from_surface(arbitrage_free_surface)

        # Clear cache
        self._option_cache.clear()

        logger.info(f"Updated spot price from {old_spot:.2f} to {new_spot:.2f} (sticky {sticky_mode})")

    def apply_time_decay(self, time_elapsed: float):
        """
        Apply time decay to the volatility surface.

        Args:
            time_elapsed: Time elapsed in years
        """
        # Build current volatility surface data
        vol_surface_data = self._build_vol_surface_data()

        # Apply time decay
        updated_surface = self.surface_updater.apply_time_decay(vol_surface_data, time_elapsed)

        # Remove any arbitrage (including negative densities)
        arbitrage_free_surface = ArbitrageChecker.remove_all_arbitrage_with_density_fix(
            updated_surface, self.spot_price, self.risk_free_rate, self.dividend_yield
        )

        # Update SABR parameters
        self._update_sabr_from_surface(arbitrage_free_surface)

        # Clear cache
        self._option_cache.clear()

        logger.info(f"Applied time decay of {time_elapsed*365:.1f} days")

    def apply_shock_scenario(self, spot_shock: float, vol_shock: float, skew_adjustment: float = 0.0):
        """
        Apply shock scenario to the volatility surface.

        Args:
            spot_shock: Percentage change in spot price (e.g., -0.2 for -20%)
            vol_shock: Absolute change in ATM volatility (e.g., 0.1 for +10 vol points)
            skew_adjustment: Additional skew steepening
        """
        # Update spot price first
        new_spot = self.spot_price * (1 + spot_shock)

        # Build current volatility surface data
        vol_surface_data = self._build_vol_surface_data()

        # Apply shock scenario
        shocked_surface = self.surface_updater.apply_shock_scenario(
            vol_surface_data, spot_shock, vol_shock, self.spot_price, skew_adjustment
        )

        # Remove any arbitrage (including negative densities)
        arbitrage_free_surface = ArbitrageChecker.remove_all_arbitrage_with_density_fix(
            shocked_surface, new_spot, self.risk_free_rate, self.dividend_yield
        )

        # Update spot and SABR parameters
        self.spot_price = new_spot
        self._update_sabr_from_surface(arbitrage_free_surface)

        # Clear cache
        self._option_cache.clear()

        logger.info(f"Applied shock: spot {spot_shock:.1%}, vol +{vol_shock:.1%}, skew +{skew_adjustment:.3f}")

    def _build_vol_surface_data(self) -> Dict[float, Dict[float, float]]:
        """Build volatility surface data from current SABR parameters."""
        vol_surface_data = {}
        expiries = sorted(self.vol_surface.sabr_params.keys())
        strikes = [self.spot_price * mult for mult in [0.8, 0.9, 0.95, 1.0, 1.05, 1.1, 1.2]]

        for expiry in expiries:
            vol_surface_data[expiry] = {}
            forward = self.spot_price * math.exp((self.risk_free_rate - self.dividend_yield) * expiry)
            for strike in strikes:
                vol = self.vol_surface.sabr_implied_vol(forward, strike, expiry)
                vol_surface_data[expiry][strike] = vol

        return vol_surface_data

    def _update_sabr_from_surface(self, vol_surface_data: Dict[float, Dict[float, float]]):
        """Update SABR parameters to match the given volatility surface using full recalibration."""
        for expiry in vol_surface_data:
            if expiry in self.vol_surface.sabr_params:
                strikes = sorted(vol_surface_data[expiry].keys())
                vols = [vol_surface_data[expiry][k] for k in strikes]

                if len(strikes) >= 3 and len(vols) >= 3:
                    try:
                        # Find ATM, 25D put, and 25D call equivalent points
                        forward = self.spot_price * math.exp((self.risk_free_rate - self.dividend_yield) * expiry)

                        # ATM vol (closest to forward)
                        atm_idx = min(range(len(strikes)), key=lambda i: abs(strikes[i] - forward))
                        atm_vol = vols[atm_idx]

                        # Find OTM put and call vols (approximate 25-delta equivalent)
                        put_idx = min(range(len(strikes)), key=lambda i: abs(strikes[i] - forward * 0.9))
                        call_idx = min(range(len(strikes)), key=lambda i: abs(strikes[i] - forward * 1.1))

                        put_25d_vol = vols[put_idx] if put_idx != atm_idx else atm_vol * 1.05
                        call_25d_vol = vols[call_idx] if call_idx != atm_idx else atm_vol * 0.95

                        # Recalibrate SABR parameters
                        self.vol_surface.calibrate_from_smile(expiry, forward, atm_vol, put_25d_vol, call_25d_vol)

                        logger.debug(f"Recalibrated SABR for expiry {expiry} from arbitrage-free surface")

                    except Exception as e:
                        logger.warning(f"Failed to recalibrate SABR for expiry {expiry}: {e}")
                        # Fallback: just update alpha
                        atm_idx = min(range(len(strikes)), key=lambda i: abs(strikes[i] - self.spot_price))
                        atm_vol = vols[atm_idx]
                        forward = self.spot_price * math.exp((self.risk_free_rate - self.dividend_yield) * expiry)
                        self.vol_surface.sabr_params[expiry]['alpha'] = atm_vol * (forward ** (1 - self.vol_surface.sabr_params[expiry]['beta']))
                else:
                    # Not enough points for full calibration, just update alpha
                    if strikes and vols:
                        atm_idx = min(range(len(strikes)), key=lambda i: abs(strikes[i] - self.spot_price))
                        atm_vol = vols[atm_idx]
                        forward = self.spot_price * math.exp((self.risk_free_rate - self.dividend_yield) * expiry)
                        self.vol_surface.sabr_params[expiry]['alpha'] = atm_vol * (forward ** (1 - self.vol_surface.sabr_params[expiry]['beta']))

    def apply_event_vol_adjustment(self, event_date: float, vol_bump: float, expiry_threshold: float = 0.25):
        """
        Apply volatility adjustment for known events (e.g., earnings).

        Args:
            event_date: Time to event in years
            vol_bump: Volatility increase in absolute terms (e.g., 0.05 for +5%)
            expiry_threshold: Only affect expiries within this time of the event
        """
        for expiry in self.vol_surface.sabr_params:
            if abs(expiry - event_date) <= expiry_threshold:
                # Increase alpha (ATM vol) and nu (vol of vol) for event
                params = self.vol_surface.sabr_params[expiry]
                params['alpha'] += vol_bump
                params['nu'] = min(params['nu'] * 1.2, 1.5)  # Increase vol of vol by 20%, cap at 1.5

                logger.info(f"Applied event vol bump of {vol_bump:.1%} to expiry {expiry:.3f}")

        # Clear cache
        self._option_cache.clear()

    def update_from_realized_vol(self, realized_vol: float, lookback_days: int = 30):
        """
        Update SABR alpha parameters based on realized volatility feedback.

        Args:
            realized_vol: Recent realized volatility (annualized)
            lookback_days: Number of days used to calculate realized vol
        """
        # Calculate adjustment factor based on realized vs implied vol
        current_atm_vol = self.vol_surface.sabr_params[0.25]['alpha']  # Use 3M as reference
        vol_ratio = realized_vol / current_atm_vol

        # Apply gradual adjustment (don't shock the surface)
        adjustment_factor = 0.1 * (vol_ratio - 1.0)  # 10% of the difference

        for expiry in self.vol_surface.sabr_params:
            # Weight adjustment by expiry (shorter expiries get more adjustment)
            expiry_weight = math.exp(-expiry * 2)  # Exponential decay
            params = self.vol_surface.sabr_params[expiry]

            old_alpha = params['alpha']
            params['alpha'] *= (1 + adjustment_factor * expiry_weight)
            params['alpha'] = max(min(params['alpha'], 2.0), 0.05)  # Bounds check

            logger.debug(f"Realized vol adjustment: expiry {expiry:.3f}, alpha {old_alpha:.3f} -> {params['alpha']:.3f}")

        logger.info(f"Updated SABR parameters based on {lookback_days}D realized vol: {realized_vol:.1%}")

        # Clear cache
        self._option_cache.clear()

    def check_density_positivity(self) -> List[str]:
        """
        Check that the risk-neutral density is non-negative across the surface.

        Returns:
            List of warnings for negative density regions
        """
        warnings = []

        for expiry in sorted(self.vol_surface.sabr_params.keys()):
            if expiry <= 0:
                continue

            # Sample strikes around current spot
            strikes = np.linspace(self.spot_price * 0.5, self.spot_price * 1.5, 50)
            forward = self.spot_price * math.exp((self.risk_free_rate - self.dividend_yield) * expiry)

            # Calculate second derivative of call prices w.r.t. strike (risk-neutral density)
            call_prices = []
            for strike in strikes:
                vol = self.vol_surface.sabr_implied_vol(forward, strike, expiry)
                call_price = BlackScholesEngine.black_scholes_call(
                    self.spot_price, strike, expiry, self.risk_free_rate, self.dividend_yield, vol
                )
                call_prices.append(call_price)

            # Numerical second derivative
            densities = []
            for i in range(1, len(call_prices) - 1):
                h = strikes[1] - strikes[0]  # Strike spacing
                second_deriv = (call_prices[i+1] - 2*call_prices[i] + call_prices[i-1]) / (h**2)
                density = second_deriv * math.exp(self.risk_free_rate * expiry)
                densities.append(density)

                if density < -1e-6:  # Small tolerance for numerical errors
                    moneyness = strikes[i] / self.spot_price
                    warnings.append(f"Negative density at expiry {expiry:.3f}, strike {strikes[i]:.2f} "
                                  f"(moneyness {moneyness:.2f}): density={density:.6f}")

        return warnings

    def validate_surface(self, auto_fix: bool = False) -> List[str]:
        """
        Comprehensive validation of the volatility surface for all arbitrage opportunities.

        Args:
            auto_fix: If True, automatically fix detected arbitrage

        Returns:
            List of validation warnings
        """
        warnings = []

        # Build surface data for validation
        expiries = sorted(self.vol_surface.sabr_params.keys())
        strikes = [self.spot_price * mult for mult in [0.8, 0.9, 0.95, 1.0, 1.05, 1.1, 1.2]]

        vol_surface_data = {}
        for expiry in expiries:
            vol_surface_data[expiry] = {}
            forward = self.spot_price * math.exp((self.risk_free_rate - self.dividend_yield) * expiry)
            for strike in strikes:
                vol = self.vol_surface.sabr_implied_vol(forward, strike, expiry)
                vol_surface_data[expiry][strike] = vol

        # Check for calendar arbitrage
        calendar_warnings = self.arbitrage_checker.check_calendar_arbitrage(vol_surface_data, strikes)
        warnings.extend(calendar_warnings)

        # Check for butterfly arbitrage
        for expiry in expiries:
            strikes_sorted = sorted(vol_surface_data[expiry].keys())
            vols = [vol_surface_data[expiry][k] for k in strikes_sorted]
            butterfly_warnings = self.arbitrage_checker.check_butterfly_arbitrage(vols, strikes_sorted)
            warnings.extend([f"Expiry {expiry}: {w}" for w in butterfly_warnings])

        # Check for negative risk-neutral density
        density_warnings = self.check_density_positivity()
        warnings.extend(density_warnings)

        # Check put-call parity violations in bid/ask spreads
        parity_warnings = []
        for expiry in expiries[:2]:  # Check first 2 expiries to avoid too many warnings
            for strike in strikes[::2]:  # Check every other strike
                try:
                    call_quote, put_quote = self.get_parity_enforced_quotes(strike, expiry)

                    # Check if parity enforcement made significant changes
                    original_call = self.get_option_quote(strike, expiry, is_call=True)
                    original_put = self.get_option_quote(strike, expiry, is_call=False)

                    call_bid_change = abs(call_quote['bid'] - original_call['bid'])
                    put_ask_change = abs(put_quote['ask'] - original_put['ask'])

                    if call_bid_change > 0.01 or put_ask_change > 0.01:
                        parity_warnings.append(f"Put-call parity violation at strike {strike:.0f}, expiry {expiry:.3f}")

                except Exception as e:
                    parity_warnings.append(f"Error checking parity at strike {strike:.0f}, expiry {expiry:.3f}: {e}")

        warnings.extend(parity_warnings)

        # Auto-fix if requested
        if auto_fix and warnings:
            logger.info(f"Auto-fixing {len(warnings)} arbitrage issues...")

            # Remove arbitrage from surface (including negative densities)
            arbitrage_free_surface = ArbitrageChecker.remove_all_arbitrage_with_density_fix(
                vol_surface_data, self.spot_price, self.risk_free_rate, self.dividend_yield
            )

            # Update SABR parameters
            self._update_sabr_from_surface(arbitrage_free_surface)

            # Clear cache
            self._option_cache.clear()

            logger.info("Surface arbitrage auto-fix completed")

        return warnings

    def add_event(self, event_date: str, vol_bump: float, expiry_threshold: float = 0.25):
        """
        Add an event to the calendar for automatic volatility adjustments.

        Args:
            event_date: Event date in 'YYYY-MM-DD' format
            vol_bump: Volatility increase in absolute terms
            expiry_threshold: Only affect expiries within this time of the event
        """
        self.event_calendar[event_date] = {
            'vol_bump': vol_bump,
            'expiry_threshold': expiry_threshold
        }
        logger.info(f"Added event on {event_date}: vol_bump={vol_bump:.1%}, threshold={expiry_threshold:.3f}")

    def add_realized_vol(self, date: str, realized_vol: float):
        """
        Add realized volatility observation for automatic calibration updates.

        Args:
            date: Date in 'YYYY-MM-DD' format
            realized_vol: Realized volatility (annualized)
        """
        self.realized_vol_history.append((date, realized_vol))

        # Keep only last 60 days
        if len(self.realized_vol_history) > 60:
            self.realized_vol_history = self.realized_vol_history[-60:]

        logger.debug(f"Added realized vol for {date}: {realized_vol:.1%}")

    def enable_auto_updates(self, enable: bool = True):
        """Enable or disable automatic event and realized vol updates."""
        self.auto_update_enabled = enable
        logger.info(f"Auto-updates {'enabled' if enable else 'disabled'}")

    def process_daily_updates(self, current_date: str):
        """
        Process daily updates for events and realized volatility.
        Should be called once per day in production.

        Args:
            current_date: Current date in 'YYYY-MM-DD' format
        """
        if not self.auto_update_enabled:
            return

        try:
            from datetime import datetime, timedelta
            current_dt = datetime.strptime(current_date, '%Y-%m-%d')

            # Process upcoming events (within next 30 days)
            for event_date_str, event_info in self.event_calendar.items():
                event_dt = datetime.strptime(event_date_str, '%Y-%m-%d')
                days_to_event = (event_dt - current_dt).days

                if 0 <= days_to_event <= 30:  # Event within next 30 days
                    time_to_event = days_to_event / 365.0
                    self.apply_event_vol_adjustment(
                        time_to_event,
                        event_info['vol_bump'],
                        event_info['expiry_threshold']
                    )
                    logger.info(f"Applied event adjustment for {event_date_str} ({days_to_event} days away)")

            # Process realized vol updates (if we have recent data)
            if len(self.realized_vol_history) >= 5:  # Need at least 5 days
                recent_vols = [vol for date, vol in self.realized_vol_history[-30:]]  # Last 30 days
                avg_realized_vol = sum(recent_vols) / len(recent_vols)

                self.update_from_realized_vol(avg_realized_vol, lookback_days=len(recent_vols))
                logger.info(f"Updated from realized vol: {avg_realized_vol:.1%} ({len(recent_vols)} days)")

        except Exception as e:
            logger.error(f"Error in daily updates: {e}")


def run_unit_tests():
    """
    Run unit tests for the synthetic options module.
    """
    print("Running Synthetic Options Unit Tests...")

    # Initialize engine
    engine = SyntheticOptionsEngine(spot_price=100.0, risk_free_rate=0.05, dividend_yield=0.02)

    # Test 1: Put-Call Parity
    print("\n1. Testing Put-Call Parity...")
    strike = 100.0
    expiry = 0.25

    call_quote = engine.get_option_quote(strike, expiry, is_call=True)
    put_quote = engine.get_option_quote(strike, expiry, is_call=False)

    # Put-Call Parity: Call - Put = Spot*e^(-q*T) - Strike*e^(-r*T)
    forward_spot = engine.spot_price * math.exp(-engine.dividend_yield * expiry)
    discounted_strike = strike * math.exp(-engine.risk_free_rate * expiry)
    theoretical_diff = forward_spot - discounted_strike
    actual_diff = call_quote['mid'] - put_quote['mid']

    parity_error = abs(actual_diff - theoretical_diff)
    print(f"   Call Mid: {call_quote['mid']:.4f}")
    print(f"   Put Mid: {put_quote['mid']:.4f}")
    print(f"   Theoretical C-P: {theoretical_diff:.4f}")
    print(f"   Actual C-P: {actual_diff:.4f}")
    print(f"   Parity Error: {parity_error:.6f}")
    assert parity_error < 0.01, f"Put-call parity violation: {parity_error}"
    print("   ✓ Put-Call Parity Test PASSED")

    # Test 2: Bid <= Mid <= Ask
    print("\n2. Testing Bid-Ask Ordering...")
    for quote in [call_quote, put_quote]:
        assert quote['bid'] <= quote['mid'] <= quote['ask'], "Bid-Ask ordering violation"
        assert quote['ask'] - quote['bid'] >= 0.01, "Spread too narrow"
    print("   ✓ Bid-Ask Ordering Test PASSED")

    # Test 3: Intrinsic Value
    print("\n3. Testing Intrinsic Value Bounds...")
    itm_call = engine.get_option_quote(95.0, expiry, is_call=True)
    itm_put = engine.get_option_quote(105.0, expiry, is_call=False)

    call_intrinsic = max(engine.spot_price - 95.0, 0)
    put_intrinsic = max(105.0 - engine.spot_price, 0)

    assert itm_call['bid'] >= call_intrinsic, "Call bid below intrinsic value"
    assert itm_put['bid'] >= put_intrinsic, "Put bid below intrinsic value"
    print(f"   ITM Call Bid: {itm_call['bid']:.4f} >= Intrinsic: {call_intrinsic:.4f}")
    print(f"   ITM Put Bid: {itm_put['bid']:.4f} >= Intrinsic: {put_intrinsic:.4f}")
    print("   ✓ Intrinsic Value Test PASSED")

    # Test 4: Arbitrage Checks
    print("\n4. Testing Arbitrage Validation...")
    warnings = engine.validate_surface()
    print(f"   Arbitrage warnings: {len(warnings)}")
    for warning in warnings[:3]:  # Show first 3 warnings
        print(f"   - {warning}")
    print("   ✓ Arbitrage Validation Test COMPLETED")

    # Test 5: Greeks Reasonableness
    print("\n5. Testing Greeks...")
    atm_call = engine.get_option_quote(100.0, 0.25, is_call=True)
    assert 0.4 <= atm_call['delta'] <= 0.6, f"ATM call delta unreasonable: {atm_call['delta']}"
    assert atm_call['gamma'] > 0, f"Gamma should be positive: {atm_call['gamma']}"
    assert atm_call['vega'] > 0, f"Vega should be positive: {atm_call['vega']}"
    print(f"   ATM Call Delta: {atm_call['delta']:.4f}")
    print(f"   ATM Call Gamma: {atm_call['gamma']:.4f}")
    print(f"   ATM Call Vega: {atm_call['vega']:.4f}")
    print("   ✓ Greeks Test PASSED")

    # Test 6: Put-Call Parity Enforcement
    print("\n6. Testing Put-Call Parity Enforcement...")
    call_quote, put_quote = engine.get_parity_enforced_quotes(100.0, 0.25)

    forward = engine.spot_price * math.exp(-engine.dividend_yield * 0.25)
    discounted_strike = 100.0 * math.exp(-engine.risk_free_rate * 0.25)
    theoretical_diff = forward - discounted_strike

    # Test bid/ask parity constraints
    conversion_value = call_quote['bid'] - put_quote['ask']
    reversal_value = call_quote['ask'] - put_quote['bid']

    assert conversion_value <= theoretical_diff + 0.01, f"Conversion arbitrage: {conversion_value} > {theoretical_diff}"
    assert reversal_value >= theoretical_diff - 0.01, f"Reversal arbitrage: {reversal_value} < {theoretical_diff}"
    print(f"   Conversion constraint: {conversion_value:.4f} <= {theoretical_diff:.4f}")
    print(f"   Reversal constraint: {reversal_value:.4f} >= {theoretical_diff:.4f}")
    print("   ✓ Put-Call Parity Enforcement Test PASSED")

    # Test 7: Surface Updates
    print("\n7. Testing Surface Updates...")
    original_quote = engine.get_option_quote(100.0, 0.25, True)

    # Test spot move
    engine.update_spot_price(105.0, sticky_mode='delta')
    moved_quote = engine.get_option_quote(100.0, 0.25, True)
    assert moved_quote['mid'] != original_quote['mid'], "Spot move should change option price"

    # Test time decay
    engine.apply_time_decay(1/365)  # 1 day

    # Test shock scenario
    engine.apply_shock_scenario(-0.10, 0.05)  # -10% spot, +5% vol
    shocked_quote = engine.get_option_quote(100.0, 0.25, True)

    print(f"   Original: ${original_quote['mid']:.2f}")
    print(f"   After spot move: ${moved_quote['mid']:.2f}")
    print(f"   After shock: ${shocked_quote['mid']:.2f}")
    print("   ✓ Surface Updates Test PASSED")

    # Test 8: Arbitrage Auto-Fix
    print("\n8. Testing Arbitrage Auto-Fix...")
    warnings_before = engine.validate_surface(auto_fix=False)
    warnings_after = engine.validate_surface(auto_fix=True)

    print(f"   Warnings before auto-fix: {len(warnings_before)}")
    print(f"   Warnings after auto-fix: {len(warnings_after)}")
    print("   ✓ Arbitrage Auto-Fix Test COMPLETED")

    print("\n✅ All Unit Tests PASSED!")


def example_usage():
    """
    Example usage of the synthetic options engine.
    """
    print("\n" + "="*60)
    print("SYNTHETIC OPTIONS ENGINE - EXAMPLE USAGE")
    print("="*60)

    # Initialize engine
    engine = SyntheticOptionsEngine(spot_price=100.0, risk_free_rate=0.05, dividend_yield=0.02)

    # Set custom SABR parameters for 3-month expiry
    engine.vol_surface.calibrate_from_smile(
        expiry=0.25,
        forward=100.0,
        atm_vol=0.20,
        put_25d_vol=0.25,
        call_25d_vol=0.18
    )

    print(f"Underlying Price: ${engine.spot_price:.2f}")
    print(f"Risk-Free Rate: {engine.risk_free_rate:.1%}")
    print(f"Dividend Yield: {engine.dividend_yield:.1%}")

    # Generate option chain
    strikes = [90, 95, 100, 105, 110]
    expiries = [0.083, 0.25, 0.5]  # 1 month, 3 months, 6 months

    print(f"\nOption Chain for strikes {strikes} and expiries {[f'{e*12:.0f}M' for e in expiries]}:")
    print("-" * 120)
    print(f"{'Strike':<8} {'Expiry':<8} {'Type':<6} {'Bid':<8} {'Mid':<8} {'Ask':<8} {'IV':<8} {'Delta':<8} {'Vega':<8}")
    print("-" * 120)

    for expiry in expiries:
        for strike in strikes:
            # Call option
            call = engine.get_option_quote(strike, expiry, is_call=True)
            print(f"{strike:<8.0f} {expiry*12:<8.0f}M {'Call':<6} {call['bid']:<8.2f} {call['mid']:<8.2f} "
                  f"{call['ask']:<8.2f} {call['implied_vol']:<8.1%} {call['delta']:<8.3f} {call['vega']:<8.2f}")

            # Put option
            put = engine.get_option_quote(strike, expiry, is_call=False)
            print(f"{strike:<8.0f} {expiry*12:<8.0f}M {'Put':<6} {put['bid']:<8.2f} {put['mid']:<8.2f} "
                  f"{put['ask']:<8.2f} {put['implied_vol']:<8.1%} {put['delta']:<8.3f} {put['vega']:<8.2f}")

    # Demonstrate surface updates
    print(f"\n" + "="*60)
    print("SURFACE UPDATE EXAMPLES")
    print("="*60)

    # Underlying move
    print(f"\nOriginal ATM Call (100 strike, 3M): {engine.get_option_quote(100, 0.25, True)['mid']:.2f}")
    engine.update_spot_price(105.0, sticky_mode='delta')
    print(f"After 5% spot move (sticky delta): {engine.get_option_quote(100, 0.25, True)['mid']:.2f}")

    # Reset and try sticky strike
    engine.update_spot_price(100.0, sticky_mode='strike')
    print(f"Reset to original: {engine.get_option_quote(100, 0.25, True)['mid']:.2f}")

    print(f"\n" + "="*60)
    print("VALIDATION RESULTS")
    print("="*60)

    # Validate surface with auto-fix
    print("Validating surface for arbitrage...")
    warnings = engine.validate_surface(auto_fix=True)
    if warnings:
        print(f"Found and fixed {len(warnings)} potential arbitrage issues:")
        for warning in warnings[:5]:  # Show first 5
            print(f"  - {warning}")
        if len(warnings) > 5:
            print(f"  ... and {len(warnings) - 5} more")
    else:
        print("✅ No arbitrage opportunities detected!")

    # Demonstrate advanced features
    print(f"\n" + "="*60)
    print("ADVANCED FEATURES DEMONSTRATION")
    print("="*60)

    # Event volatility adjustment
    print("\n1. Event Volatility Adjustment (e.g., earnings in 1 week):")
    original_quote = engine.get_option_quote(100, 0.083, True)  # 1 month
    print(f"   Before event: ATM call = ${original_quote['mid']:.2f}, IV = {original_quote['implied_vol']:.1%}")

    engine.apply_event_vol_adjustment(event_date=0.083, vol_bump=0.05)  # +5% vol for 1M expiry
    event_quote = engine.get_option_quote(100, 0.083, True)
    print(f"   After event:  ATM call = ${event_quote['mid']:.2f}, IV = {event_quote['implied_vol']:.1%}")

    # Realized vol feedback
    print("\n2. Realized Volatility Feedback:")
    engine.update_from_realized_vol(realized_vol=0.35, lookback_days=30)  # 35% realized vol
    realized_quote = engine.get_option_quote(100, 0.25, True)
    print(f"   After realized vol update: ATM call IV = {realized_quote['implied_vol']:.1%}")

    # Shock scenario
    print("\n3. Shock Scenario (-20% spot, +10 vol points):")
    original_spot = engine.spot_price
    engine.apply_shock_scenario(spot_shock=-0.20, vol_shock=0.10, skew_adjustment=0.02)
    shock_quote = engine.get_option_quote(100, 0.25, True)  # Same strike, new spot
    print(f"   Spot: ${original_spot:.2f} -> ${engine.spot_price:.2f}")
    print(f"   100 strike call: ${original_quote['mid']:.2f} -> ${shock_quote['mid']:.2f}")
    print(f"   IV: {original_quote['implied_vol']:.1%} -> {shock_quote['implied_vol']:.1%}")

    # Put-call parity enforcement
    print("\n4. Put-Call Parity Enforcement:")
    call_quote, put_quote = engine.get_parity_enforced_quotes(100, 0.25)
    forward = engine.spot_price * math.exp(-0.0 * 0.25)  # No dividend
    discounted_strike = 100 * math.exp(-0.05 * 0.25)
    theoretical_diff = forward - discounted_strike
    actual_diff = call_quote['mid'] - put_quote['mid']
    print(f"   Call-Put parity: theoretical = ${theoretical_diff:.4f}, actual = ${actual_diff:.4f}")
    print(f"   Parity error: ${abs(actual_diff - theoretical_diff):.6f}")

    # Time decay
    print("\n5. Time Decay (1 week forward):")
    engine.apply_time_decay(time_elapsed=7/365)
    decay_quote = engine.get_option_quote(100, 0.25 - 7/365, True)  # Reduced expiry
    print(f"   After 1 week: ATM call = ${decay_quote['mid']:.2f}, IV = {decay_quote['implied_vol']:.1%}")


if __name__ == "__main__":
    # Run tests and examples
    run_unit_tests()
    example_usage()
