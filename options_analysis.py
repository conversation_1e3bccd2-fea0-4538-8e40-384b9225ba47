import sys
import pandas as pd
import yfinance as yf
import logging
from datetime import datetime
from math import log, sqrt, exp
from scipy.stats import norm

# Set up logger
logger = logging.getLogger(__name__)

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QProgressBar,
    QFrame, QGraphicsView, QGraphicsScene, QSizePolicy, QTabWidget
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPainter, QBrush, QPen, QColor

# Chart view for gamma exposure
class BarChartView(QGraphicsView):
    """Custom QGraphicsView for displaying a bar chart for gamma exposure"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.scene = QGraphicsScene(self)
        self.setScene(self.scene)

        # Set background color
        self.setBackgroundBrush(QBrush(QColor("#121212")))

        # Enable antialiasing
        self.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Completely disable any focus or selection indicators
        self.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        self.setFrameStyle(QFrame.Shape.NoFrame)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)
        self.setStyleSheet("background: transparent; border: none;")

        # Set size policy
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Enable mouse tracking for crosshair
        self.setMouseTracking(True)

        # Initialize variables
        self.calls_data = pd.DataFrame()
        self.puts_data = pd.DataFrame()
        self.current_price = 0
        self.min_strike = 0
        self.max_strike = 0
        self.min_gex = 0
        self.max_gex = 0
        self.title = ""
        self.bar_objects = {}
        self.selected_strike = None
        self.mouse_pos = None

        # Chart margins
        self.margin_left = 60
        self.margin_right = 60
        self.margin_top = 60
        self.margin_bottom = 60

    def plot_gamma_exposure(self, calls_df, puts_df, S, title):
        """Plot gamma exposure by strike"""
        # Store data
        self.calls_data = calls_df
        self.puts_data = puts_df
        self.current_price = S
        self.title = title

        # Reset bar objects and selected strike
        self.bar_objects = {}
        self.selected_strike = None

        # Calculate strike range
        strike_range = 50  # Default range, can be made configurable
        self.min_strike = S - strike_range
        self.max_strike = S + strike_range

        # Clear the scene
        self.scene.clear()

        # Calculate chart dimensions
        chart_width = self.width() - self.margin_left - self.margin_right
        chart_height = self.height() - self.margin_top - self.margin_bottom

        # Find min and max GEX values
        calls_max_gex = calls_df['GEX'].max() if not calls_df.empty else 0
        puts_min_gex = puts_df['GEX'].min() if not puts_df.empty else 0
        self.max_gex = max(calls_max_gex, abs(puts_min_gex))
        self.min_gex = -self.max_gex  # Make it symmetric for visual balance

        # Add grid lines and labels
        self.draw_grid(chart_width, chart_height)

        # Add current price line
        self.draw_price_line(chart_width, chart_height)

        # Calculate bar width
        num_bars = len(set(calls_df['strike'].tolist() + puts_df['strike'].tolist()))
        bar_width = min(chart_width / (num_bars + 1), 20)  # Limit max width

        # Draw bars for calls (positive GEX)
        total_positive = 0
        for _, row in calls_df.iterrows():
            strike = row['strike']
            gex = row['GEX']

            if gex > 0 and strike >= self.min_strike and strike <= self.max_strike:
                total_positive += gex

                # Calculate bar position and dimensions
                x = self.margin_left + chart_width * (strike - self.min_strike) / (self.max_strike - self.min_strike)
                y = self.margin_top + chart_height / 2  # Center point
                bar_height = -(chart_height / 2) * (gex / self.max_gex)  # Negative height for upward bars

                # Create bar
                bar_color = QColor("#00FF00")  # Green for positive GEX
                bar_brush = QBrush(bar_color)
                bar_pen = QPen(bar_color)

                # Add bar with slight transparency
                bar = self.scene.addRect(x - bar_width / 2, y, bar_width, bar_height, bar_pen, bar_brush)
                bar.setOpacity(0.7)

                # Store bar object and data for interaction
                self.bar_objects[strike] = {
                    'bar': bar,
                    'data': {'GEX': gex, 'strike': strike}
                }

        # Draw bars for puts (negative GEX)
        total_negative = 0
        for _, row in puts_df.iterrows():
            strike = row['strike']
            gex = row['GEX']

            if gex < 0 and strike >= self.min_strike and strike <= self.max_strike:
                total_negative += abs(gex)

                # Calculate bar position and dimensions
                x = self.margin_left + chart_width * (strike - self.min_strike) / (self.max_strike - self.min_strike)
                y = self.margin_top + chart_height / 2  # Center point
                bar_height = (chart_height / 2) * (abs(gex) / self.max_gex)  # Positive height for downward bars

                # Create bar
                bar_color = QColor("#FF0000")  # Red for negative GEX
                bar_brush = QBrush(bar_color)
                bar_pen = QPen(bar_color)

                # Add bar with slight transparency
                bar = self.scene.addRect(x - bar_width / 2, y, bar_width, bar_height, bar_pen, bar_brush)
                bar.setOpacity(0.7)

                # Store bar object and data for interaction
                self.bar_objects[strike] = {
                    'bar': bar,
                    'data': {'GEX': gex, 'strike': strike}
                }

        return total_positive, total_negative

    def draw_grid(self, chart_width, chart_height):
        """Draw grid lines and labels"""
        # Add horizontal grid lines
        num_h_lines = 8  # Number of horizontal grid lines (including center)
        for i in range(num_h_lines):
            y_pos = self.margin_top + (i * chart_height / (num_h_lines - 1))
            line = self.scene.addLine(
                self.margin_left, y_pos,
                self.margin_left + chart_width, y_pos,
                QPen(QColor("#333333"))
            )
            line.setZValue(-1)  # Put grid lines behind other elements

            # Add GEX labels on y-axis
            if i < num_h_lines / 2:
                # Positive values (top half)
                gex_value = self.max_gex * (1 - (i / (num_h_lines / 2 - 1)))
                label = self.scene.addText(f"{int(gex_value):,}")
                label.setDefaultTextColor(QColor("#AAAAAA"))
                label.setPos(5, y_pos - label.boundingRect().height() / 2)
            elif i > num_h_lines / 2:
                # Negative values (bottom half)
                gex_value = self.max_gex * ((i - num_h_lines / 2) / (num_h_lines / 2 - 1))
                label = self.scene.addText(f"-{int(gex_value):,}")
                label.setDefaultTextColor(QColor("#AAAAAA"))
                label.setPos(5, y_pos - label.boundingRect().height() / 2)

        # Add vertical grid lines (strike prices)
        num_v_lines = 11  # Number of vertical grid lines
        for i in range(num_v_lines):
            strike = self.min_strike + (i * (self.max_strike - self.min_strike) / (num_v_lines - 1))
            x_pos = self.margin_left + (i * chart_width / (num_v_lines - 1))

            # Add vertical line
            line = self.scene.addLine(
                x_pos, self.margin_top,
                x_pos, self.margin_top + chart_height,
                QPen(QColor("#333333"))
            )
            line.setZValue(-1)  # Put grid lines behind other elements

            # Add strike price label
            label = self.scene.addText(f"{strike:.1f}")
            label.setDefaultTextColor(QColor("#AAAAAA"))
            label.setPos(x_pos - label.boundingRect().width() / 2,
                         self.margin_top + chart_height + 5)

    def draw_price_line(self, chart_width, chart_height):
        """Draw current price line"""
        if self.current_price >= self.min_strike and self.current_price <= self.max_strike:
            x_pos = self.margin_left + chart_width * (self.current_price - self.min_strike) / (self.max_strike - self.min_strike)

            # Add vertical line for current price
            price_line = self.scene.addLine(
                x_pos, self.margin_top,
                x_pos, self.margin_top + chart_height,
                QPen(QColor("#FFFFFF"))
            )
            price_line.setZValue(1)  # Put price line in front of grid lines

            # Add current price label
            price_label = self.scene.addText(f"Current: {self.current_price:.2f}")
            price_label.setDefaultTextColor(QColor("#FFFFFF"))
            price_label.setPos(x_pos - price_label.boundingRect().width() / 2,
                              self.margin_top - price_label.boundingRect().height() - 5)

    def resizeEvent(self, event):
        """Handle resize events"""
        super().resizeEvent(event)
        # Redraw chart if we have data
        if not self.calls_data.empty or not self.puts_data.empty:
            self.plot_gamma_exposure(self.calls_data, self.puts_data, self.current_price, self.title)

    def mouseMoveEvent(self, event):
        """Handle mouse move events for crosshair and tooltips"""
        super().mouseMoveEvent(event)
        self.mouse_pos = event.position()

        # Get scene coordinates
        scene_pos = self.mapToScene(int(self.mouse_pos.x()), int(self.mouse_pos.y()))

        # Remove existing crosshair items
        for item in self.scene.items():
            if item.data(0) == 'crosshair':
                self.scene.removeItem(item)

        # Check if mouse is within chart area
        chart_width = self.width() - self.margin_left - self.margin_right
        chart_height = self.height() - self.margin_top - self.margin_bottom

        if (self.margin_left <= scene_pos.x() <= self.margin_left + chart_width and
            self.margin_top <= scene_pos.y() <= self.margin_top + chart_height):

            # Draw horizontal crosshair line
            h_line = self.scene.addLine(
                self.margin_left, scene_pos.y(),
                self.margin_left + chart_width, scene_pos.y(),
                QPen(QColor("#FFFFFF"), 0.5, Qt.PenStyle.DashLine)
            )
            h_line.setData(0, 'crosshair')

            # Draw vertical crosshair line
            v_line = self.scene.addLine(
                scene_pos.x(), self.margin_top,
                scene_pos.x(), self.margin_top + chart_height,
                QPen(QColor("#FFFFFF"), 0.5, Qt.PenStyle.DashLine)
            )
            v_line.setData(0, 'crosshair')

            # Calculate strike price at cursor position
            strike_at_cursor = self.min_strike + (scene_pos.x() - self.margin_left) / chart_width * (self.max_strike - self.min_strike)

            # Add strike price tooltip
            strike_text = self.scene.addText(f"Strike: {strike_at_cursor:.2f}")
            strike_text.setDefaultTextColor(QColor("#FFFFFF"))
            strike_text.setPos(scene_pos.x() + 5, self.margin_top + chart_height - strike_text.boundingRect().height())
            strike_text.setData(0, 'crosshair')

            # Calculate GEX value at cursor position
            gex_value = 0
            if scene_pos.y() < self.margin_top + chart_height / 2:
                # Positive values (top half)
                gex_value = self.max_gex * (1 - (scene_pos.y() - self.margin_top) / (chart_height / 2))
            else:
                # Negative values (bottom half)
                gex_value = -self.max_gex * ((scene_pos.y() - (self.margin_top + chart_height / 2)) / (chart_height / 2))

            # Add GEX tooltip
            if scene_pos.y() <= self.margin_top + chart_height / 2:
                # Positive values (calls)
                gex_text = self.scene.addText(f"GEX: {int(gex_value):,}")
                gex_text.setDefaultTextColor(QColor("#00FF00"))
            else:
                # Negative values (puts)
                gex_text = self.scene.addText(f"GEX: {int(gex_value):,}")
                gex_text.setDefaultTextColor(QColor("#FF0000"))

            # Position the GEX text on the y-axis
            gex_text.setPos(5, scene_pos.y() - gex_text.boundingRect().height() / 2)
            gex_text.setData(0, 'crosshair')

            # Find closest bar to cursor
            closest_bar_key = None
            min_distance = float('inf')

            for strike in self.bar_objects.keys():
                bar_x = self.margin_left + chart_width * (strike - self.min_strike) / (self.max_strike - self.min_strike)
                distance = abs(scene_pos.x() - bar_x)

                if distance < min_distance:
                    min_distance = distance
                    closest_bar_key = strike

            # If we found a close bar and it's within a reasonable distance
            if closest_bar_key is not None and min_distance < 10:  # Adjusted threshold
                self.selected_strike = closest_bar_key
                bar_info = self.bar_objects[closest_bar_key]

                # Add tooltip with bar data
                gex = bar_info['data']['GEX']
                strike = bar_info['data']['strike']

                tooltip_text = f"Strike: {strike}\nGEX: {int(gex):,}"
                tooltip = self.scene.addText(tooltip_text)
                tooltip.setDefaultTextColor(QColor("#FFFFFF"))
                tooltip.setPos(scene_pos.x() + 10, scene_pos.y() - 40)
                tooltip.setData(0, 'crosshair')

# Chart view for open interest
class OpenInterestChartView(QGraphicsView):
    """Custom QGraphicsView for displaying open interest by strike chart"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.scene = QGraphicsScene(self)
        self.setScene(self.scene)

        # Set background color
        self.setBackgroundBrush(QBrush(QColor("#121212")))

        # Enable antialiasing
        self.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Completely disable any focus or selection indicators
        self.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        self.setFrameStyle(QFrame.Shape.NoFrame)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)
        self.setStyleSheet("background: transparent; border: none;")

        # Set size policy
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Enable mouse tracking for crosshair
        self.setMouseTracking(True)

        # Initialize variables
        self.calls_data = pd.DataFrame()
        self.puts_data = pd.DataFrame()
        self.current_price = 0
        self.min_strike = 0
        self.max_strike = 0
        self.min_oi = 0
        self.max_oi = 0
        self.title = ""
        self.bar_objects = {}
        self.selected_strike = None
        self.mouse_pos = None
        self.highest_call_strikes = []
        self.highest_put_strikes = []

        # Chart margins
        self.margin_left = 60
        self.margin_right = 60
        self.margin_top = 60
        self.margin_bottom = 60

    def plot_open_interest(self, calls_df, puts_df, S, title="Open Interest by Strike"):
        """Plot open interest by strike"""
        # Store data
        self.calls_data = calls_df
        self.puts_data = puts_df
        self.current_price = S
        self.title = title

        # Reset bar objects and selected strike
        self.bar_objects = {}
        self.selected_strike = None

        # Calculate strike range
        strike_range = 50  # Default range, can be made configurable
        self.min_strike = S - strike_range
        self.max_strike = S + strike_range

        # Clear the scene
        self.scene.clear()

        # Calculate chart dimensions
        chart_width = self.width() - self.margin_left - self.margin_right
        chart_height = self.height() - self.margin_top - self.margin_bottom

        # Find min and max OI values
        calls_max_oi = calls_df['openInterest'].max() if not calls_df.empty else 0
        puts_max_oi = puts_df['openInterest'].max() if not puts_df.empty else 0
        self.max_oi = max(calls_max_oi, puts_max_oi)
        self.min_oi = -self.max_oi  # Make it symmetric for visual balance

        # Add grid lines and labels
        self.draw_grid(chart_width, chart_height)

        # Add current price line
        self.draw_price_line(chart_width, chart_height)

        # Calculate bar width
        num_bars = len(set(calls_df['strike'].tolist() + puts_df['strike'].tolist()))
        bar_width = min(chart_width / (num_bars + 1), 20)  # Limit max width

        # Find highest OI strikes
        self.find_highest_oi_strikes(calls_df, puts_df)

        # Draw bars for calls (positive OI)
        total_calls = 0
        for _, row in calls_df.iterrows():
            strike = row['strike']
            oi = row['openInterest']

            if oi > 0 and strike >= self.min_strike and strike <= self.max_strike:
                total_calls += oi

                # Calculate bar position and dimensions
                x = self.margin_left + chart_width * (strike - self.min_strike) / (self.max_strike - self.min_strike)
                y = self.margin_top + chart_height / 2  # Center point
                bar_height = -(chart_height / 2) * (oi / self.max_oi)  # Negative height for upward bars

                # Create bar
                bar_color = QColor("#00FF00")  # Green for calls
                bar_brush = QBrush(bar_color)
                bar_pen = QPen(bar_color)

                # Add bar with slight transparency
                bar = self.scene.addRect(x - bar_width / 2, y, bar_width, bar_height, bar_pen, bar_brush)
                bar.setOpacity(0.7)

                # No longer highlighting highest OI strikes

                # Store bar object and data for interaction
                self.bar_objects[strike] = {
                    'bar': bar,
                    'data': {'OI': oi, 'strike': strike, 'type': 'call'}
                }

        # Draw bars for puts (negative OI)
        total_puts = 0
        for _, row in puts_df.iterrows():
            strike = row['strike']
            oi = row['openInterest']

            if oi > 0 and strike >= self.min_strike and strike <= self.max_strike:
                total_puts += oi

                # Calculate bar position and dimensions
                x = self.margin_left + chart_width * (strike - self.min_strike) / (self.max_strike - self.min_strike)
                y = self.margin_top + chart_height / 2  # Center point
                bar_height = (chart_height / 2) * (oi / self.max_oi)  # Positive height for downward bars

                # Create bar
                bar_color = QColor("#FF0000")  # Red for puts
                bar_brush = QBrush(bar_color)
                bar_pen = QPen(bar_color)

                # Add bar with slight transparency
                bar = self.scene.addRect(x - bar_width / 2, y, bar_width, bar_height, bar_pen, bar_brush)
                bar.setOpacity(0.7)

                # No longer highlighting highest OI strikes

                # Store bar object and data for interaction
                self.bar_objects[strike] = {
                    'bar': bar,
                    'data': {'OI': oi, 'strike': strike, 'type': 'put'}
                }

        return total_calls, total_puts

    def find_highest_oi_strikes(self, calls_df, puts_df, top_n=3):
        """Find the strikes with the highest open interest"""
        # We're no longer highlighting highest OI strikes, but we'll still track them
        # for potential future use

        # Find top call strikes
        if not calls_df.empty:
            top_calls = calls_df.nlargest(top_n, 'openInterest')
            self.highest_call_strikes = top_calls['strike'].tolist()
        else:
            self.highest_call_strikes = []

        # Find top put strikes
        if not puts_df.empty:
            top_puts = puts_df.nlargest(top_n, 'openInterest')
            self.highest_put_strikes = top_puts['strike'].tolist()
        else:
            self.highest_put_strikes = []

    def draw_grid(self, chart_width, chart_height):
        """Draw grid lines and labels"""
        # Add horizontal grid lines
        num_h_lines = 8  # Number of horizontal grid lines (including center)
        for i in range(num_h_lines):
            y_pos = self.margin_top + (i * chart_height / (num_h_lines - 1))
            line = self.scene.addLine(
                self.margin_left, y_pos,
                self.margin_left + chart_width, y_pos,
                QPen(QColor("#333333"))
            )
            line.setZValue(-1)  # Put grid lines behind other elements

            # Add OI labels on y-axis
            if i < num_h_lines / 2:
                # Positive values (top half)
                oi_value = self.max_oi * (1 - (i / (num_h_lines / 2 - 1)))
                label = self.scene.addText(f"{int(oi_value):,}")
                label.setDefaultTextColor(QColor("#AAAAAA"))
                label.setPos(5, y_pos - label.boundingRect().height() / 2)
            elif i > num_h_lines / 2:
                # Negative values (bottom half)
                oi_value = self.max_oi * ((i - num_h_lines / 2) / (num_h_lines / 2 - 1))
                label = self.scene.addText(f"-{int(oi_value):,}")
                label.setDefaultTextColor(QColor("#AAAAAA"))
                label.setPos(5, y_pos - label.boundingRect().height() / 2)

        # Add vertical grid lines (strike prices)
        num_v_lines = 11  # Number of vertical grid lines
        for i in range(num_v_lines):
            strike = self.min_strike + (i * (self.max_strike - self.min_strike) / (num_v_lines - 1))
            x_pos = self.margin_left + (i * chart_width / (num_v_lines - 1))

            # Add vertical line
            line = self.scene.addLine(
                x_pos, self.margin_top,
                x_pos, self.margin_top + chart_height,
                QPen(QColor("#333333"))
            )
            line.setZValue(-1)  # Put grid lines behind other elements

            # Add strike price label
            label = self.scene.addText(f"{strike:.1f}")
            label.setDefaultTextColor(QColor("#AAAAAA"))
            label.setPos(x_pos - label.boundingRect().width() / 2,
                         self.margin_top + chart_height + 5)

    def draw_price_line(self, chart_width, chart_height):
        """Draw current price line"""
        if self.current_price >= self.min_strike and self.current_price <= self.max_strike:
            x_pos = self.margin_left + chart_width * (self.current_price - self.min_strike) / (self.max_strike - self.min_strike)

            # Add vertical line for current price
            price_line = self.scene.addLine(
                x_pos, self.margin_top,
                x_pos, self.margin_top + chart_height,
                QPen(QColor("#FFFFFF"))
            )
            price_line.setZValue(1)  # Put price line in front of grid lines

            # Add current price label
            price_label = self.scene.addText(f"Current: {self.current_price:.2f}")
            price_label.setDefaultTextColor(QColor("#FFFFFF"))
            price_label.setPos(x_pos - price_label.boundingRect().width() / 2,
                              self.margin_top - price_label.boundingRect().height() - 5)

    def resizeEvent(self, event):
        """Handle resize events"""
        super().resizeEvent(event)
        # Redraw chart if we have data
        if not self.calls_data.empty or not self.puts_data.empty:
            self.plot_open_interest(self.calls_data, self.puts_data, self.current_price, self.title)

    def mouseMoveEvent(self, event):
        """Handle mouse move events for crosshair and tooltips"""
        super().mouseMoveEvent(event)
        self.mouse_pos = event.position()

        # Get scene coordinates
        scene_pos = self.mapToScene(int(self.mouse_pos.x()), int(self.mouse_pos.y()))

        # Remove existing crosshair items
        for item in self.scene.items():
            if item.data(0) == 'crosshair':
                self.scene.removeItem(item)

        # Check if mouse is within chart area
        chart_width = self.width() - self.margin_left - self.margin_right
        chart_height = self.height() - self.margin_top - self.margin_bottom

        if (self.margin_left <= scene_pos.x() <= self.margin_left + chart_width and
            self.margin_top <= scene_pos.y() <= self.margin_top + chart_height):

            # Draw horizontal crosshair line
            h_line = self.scene.addLine(
                self.margin_left, scene_pos.y(),
                self.margin_left + chart_width, scene_pos.y(),
                QPen(QColor("#FFFFFF"), 0.5, Qt.PenStyle.DashLine)
            )
            h_line.setData(0, 'crosshair')

            # Draw vertical crosshair line
            v_line = self.scene.addLine(
                scene_pos.x(), self.margin_top,
                scene_pos.x(), self.margin_top + chart_height,
                QPen(QColor("#FFFFFF"), 0.5, Qt.PenStyle.DashLine)
            )
            v_line.setData(0, 'crosshair')

            # Calculate strike price at cursor position
            strike_at_cursor = self.min_strike + (scene_pos.x() - self.margin_left) / chart_width * (self.max_strike - self.min_strike)

            # Add strike price tooltip
            strike_text = self.scene.addText(f"Strike: {strike_at_cursor:.2f}")
            strike_text.setDefaultTextColor(QColor("#FFFFFF"))
            strike_text.setPos(scene_pos.x() + 5, self.margin_top + chart_height - strike_text.boundingRect().height())
            strike_text.setData(0, 'crosshair')

            # Calculate OI value at cursor position
            oi_value = 0
            if scene_pos.y() < self.margin_top + chart_height / 2:
                # Positive values (top half)
                oi_value = self.max_oi * (1 - (scene_pos.y() - self.margin_top) / (chart_height / 2))
            else:
                # Negative values (bottom half)
                oi_value = -self.max_oi * ((scene_pos.y() - (self.margin_top + chart_height / 2)) / (chart_height / 2))

            # Add OI tooltip
            if scene_pos.y() <= self.margin_top + chart_height / 2:
                # Positive values (calls)
                oi_text = self.scene.addText(f"OI: {int(oi_value):,}")
                oi_text.setDefaultTextColor(QColor("#00FF00"))
            else:
                # Negative values (puts)
                oi_text = self.scene.addText(f"OI: {int(-oi_value):,}")
                oi_text.setDefaultTextColor(QColor("#FF0000"))

            # Position the OI text on the y-axis
            oi_text.setPos(5, scene_pos.y() - oi_text.boundingRect().height() / 2)
            oi_text.setData(0, 'crosshair')

            # Find closest bar to cursor
            closest_bar_key = None
            min_distance = float('inf')

            for strike in self.bar_objects.keys():
                bar_x = self.margin_left + chart_width * (strike - self.min_strike) / (self.max_strike - self.min_strike)
                distance = abs(scene_pos.x() - bar_x)

                if distance < min_distance:
                    min_distance = distance
                    closest_bar_key = strike

            # If we found a close bar and it's within a reasonable distance
            if closest_bar_key is not None and min_distance < 10:  # Adjusted threshold
                self.selected_strike = closest_bar_key
                bar_info = self.bar_objects[closest_bar_key]

                # Add tooltip with bar data
                oi = bar_info['data']['OI']
                strike = bar_info['data']['strike']
                bar_type = bar_info['data']['type']

                tooltip_text = f"Strike: {strike}\nOI: {int(oi):,}\nType: {bar_type.capitalize()}"
                tooltip = self.scene.addText(tooltip_text)
                tooltip.setDefaultTextColor(QColor("#FFFFFF"))
                tooltip.setPos(scene_pos.x() + 10, scene_pos.y() - 40)
                tooltip.setData(0, 'crosshair')


# Utility functions for options analysis
def format_ticker(ticker):
    """Format ticker symbol for API requests"""
    ticker = ticker.upper().strip()
    # Add .X for indices if needed
    if ticker == "SPX":
        ticker = "^SPX"
    elif ticker == "VIX":
        ticker = "^VIX"
    return ticker

def get_current_price(ticker):
    """Get current price for a ticker using Yahoo Finance"""
    try:

        # Fall back to Yahoo Finance for current price
        stock = yf.Ticker(ticker)
        info = stock.info
        if 'regularMarketPrice' in info and info['regularMarketPrice'] is not None:
            return info['regularMarketPrice']
        elif 'previousClose' in info and info['previousClose'] is not None:
            return info['previousClose']
        else:
            return None
    except Exception as e:
        logger.error(f"Error fetching price for {ticker}: {e}")
        return None

def fetch_options_for_date(ticker, date, S=None):
    """Fetches option chains for the given ticker and date using Yahoo Finance."""
    logger.debug(f"Fetching option chain for {ticker} EXP {date} from Yahoo Finance")
    return _fetch_options_yahoo(ticker, date, S)



def _fetch_options_yahoo(ticker, date, S=None):
    """Fetch options data from Yahoo Finance"""
    stock = yf.Ticker(ticker)
    try:
        if S is None:
            S = get_current_price(ticker)
        chain = stock.option_chain(date)
        calls = chain.calls
        puts = chain.puts

        # Extract expiry date from contract symbol if needed
        if 'contractSymbol' in calls.columns:
            calls['extracted_expiry'] = pd.to_datetime(date)
            puts['extracted_expiry'] = pd.to_datetime(date)

        return calls, puts
    except Exception as e:
        logger.error(f"Error fetching options chain for date {date}: {e}")
        return pd.DataFrame(), pd.DataFrame()



def calculate_greeks(flag, S, K, t, sigma, r=0.05):
    """Calculate option Greeks using Black-Scholes"""
    # Convert inputs to float
    S = float(S)
    K = float(K)
    t = float(t)
    sigma = float(sigma)

    # Calculate d1 and d2
    d1 = (log(S / K) + (r + 0.5 * sigma ** 2) * t) / (sigma * sqrt(t))
    d2 = d1 - sigma * sqrt(t)

    # Calculate delta
    if flag == "c":
        delta = norm.cdf(d1)
    else:
        delta = -norm.cdf(-d1)

    # Calculate gamma (same for calls and puts)
    gamma = norm.pdf(d1) / (S * sigma * sqrt(t))

    # Calculate theta
    if flag == "c":
        theta = -S * norm.pdf(d1) * sigma / (2 * sqrt(t)) - r * K * exp(-r * t) * norm.cdf(d2)
    else:
        theta = -S * norm.pdf(d1) * sigma / (2 * sqrt(t)) + r * K * exp(-r * t) * norm.cdf(-d2)

    # Adjust theta to daily
    theta = theta / 365

    return delta, gamma, theta

def calculate_gamma_exposure(calls, puts, S, expiry_date):
    """Calculate gamma exposure for calls and puts"""
    # Calculate days to expiry
    today = datetime.now().date()
    expiry = datetime.strptime(expiry_date, '%Y-%m-%d').date()
    t_days = max((expiry - today).days, 1)  # Ensure at least 1 day
    t = t_days / 365.0

    # Define function to compute gamma
    def compute_gamma(row, flag):
        sigma = row.get("impliedVolatility", None)
        if sigma is None or sigma <= 0:
            return None
        try:
            _, gamma_val, _ = calculate_greeks(flag, S, row["strike"], t, sigma)
            return gamma_val
        except Exception:
            return None

    # Calculate gamma for each option
    calls = calls.copy()
    puts = puts.copy()
    calls["calc_gamma"] = calls.apply(lambda row: compute_gamma(row, "c"), axis=1)
    puts["calc_gamma"] = puts.apply(lambda row: compute_gamma(row, "p"), axis=1)

    # Calculate GEX (Gamma Exposure)
    # For calls: gamma is positive, so GEX is positive
    # For puts: gamma is positive but exposure is negative, so multiply by -1
    calls["GEX"] = calls["calc_gamma"] * calls["openInterest"] * 100 * S / 10
    puts["GEX"] = -1 * puts["calc_gamma"] * puts["openInterest"] * 100 * S / 10

    return calls, puts

class CombinedOptionsAnalysisApp(QMainWindow):
    """Main application window with tabs for different analysis views"""
    def __init__(self):
        super().__init__()

        # Set window properties
        self.setWindowTitle("Options Analysis Tool")

        # Create central widget and main layout
        central_widget = QWidget()
        central_widget.setContentsMargins(0, 0, 0, 0)
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        main_layout.addWidget(self.tab_widget)

        # Create tabs
        self.gamma_exposure_tab = QWidget()
        self.open_interest_tab = QWidget()

        # Add tabs to the tab widget
        self.tab_widget.addTab(self.gamma_exposure_tab, "Gamma Exposure")
        self.tab_widget.addTab(self.open_interest_tab, "Open Interest")

        # Set up UI for each tab
        self.setup_gamma_exposure_tab()
        self.setup_open_interest_tab()

    def setup_gamma_exposure_tab(self):
        """Set up the gamma exposure tab UI"""
        # Create layout
        layout = QVBoxLayout(self.gamma_exposure_tab)

        # Create input frame
        input_frame = QFrame()
        input_frame.setFrameShape(QFrame.Shape.StyledPanel)
        input_layout = QHBoxLayout(input_frame)

        # Add ticker input
        ticker_label = QLabel("Ticker:")
        self.gamma_ticker_input = QLineEdit()
        self.gamma_ticker_input.setPlaceholderText("Enter ticker symbol (e.g., SPY)")
        input_layout.addWidget(ticker_label)
        input_layout.addWidget(self.gamma_ticker_input)

        # Add expiry date selector
        expiry_label = QLabel("Expiry:")
        self.gamma_expiry_selector = QComboBox()
        input_layout.addWidget(expiry_label)
        input_layout.addWidget(self.gamma_expiry_selector)

        # Add fetch button
        self.gamma_fetch_button = QPushButton("Fetch Data")
        input_layout.addWidget(self.gamma_fetch_button)

        # Create progress bar and status label
        self.gamma_progress_bar = QProgressBar()
        self.gamma_progress_bar.setRange(0, 0)  # Indeterminate progress
        self.gamma_progress_bar.hide()

        self.gamma_status_label = QLabel("Enter a ticker symbol and press Enter to fetch data")

        # Create header frame for totals
        header_frame = QFrame()
        header_layout = QHBoxLayout(header_frame)

        self.gamma_total_positive_label = QLabel("Total Positive GEX: 0")
        self.gamma_total_positive_label.setStyleSheet("color: #00FF00; font-size: 16px; font-weight: bold;")

        self.gamma_total_negative_label = QLabel("Total Negative GEX: 0")
        self.gamma_total_negative_label.setStyleSheet("color: #FF0000; font-size: 16px; font-weight: bold;")

        header_layout.addWidget(self.gamma_total_positive_label)
        header_layout.addSpacing(20)
        header_layout.addWidget(self.gamma_total_negative_label)
        header_layout.addStretch()

        # Create chart view
        self.gamma_chart_view = BarChartView()

        # Add widgets to main layout
        layout.addWidget(input_frame)
        layout.addWidget(self.gamma_progress_bar)
        layout.addWidget(self.gamma_status_label)
        layout.addWidget(header_frame)
        layout.addWidget(self.gamma_chart_view)

        # Connect signals
        self.gamma_ticker_input.returnPressed.connect(self.fetch_gamma_expiry_dates)
        self.gamma_fetch_button.clicked.connect(self.fetch_gamma_exposure)
        self.gamma_expiry_selector.currentIndexChanged.connect(self.fetch_gamma_exposure)

    def setup_open_interest_tab(self):
        """Set up the open interest tab UI"""
        # Create layout
        layout = QVBoxLayout(self.open_interest_tab)

        # Create input frame
        input_frame = QFrame()
        input_frame.setFrameShape(QFrame.Shape.StyledPanel)
        input_layout = QHBoxLayout(input_frame)

        # Add ticker input
        ticker_label = QLabel("Ticker:")
        self.oi_ticker_input = QLineEdit()
        self.oi_ticker_input.setPlaceholderText("Enter ticker symbol (e.g., SPY)")
        input_layout.addWidget(ticker_label)
        input_layout.addWidget(self.oi_ticker_input)

        # Add expiry date selector
        expiry_label = QLabel("Expiry:")
        self.oi_expiry_selector = QComboBox()
        input_layout.addWidget(expiry_label)
        input_layout.addWidget(self.oi_expiry_selector)

        # Add fetch button
        self.oi_fetch_button = QPushButton("Fetch Data")
        input_layout.addWidget(self.oi_fetch_button)

        # Create progress bar and status label
        self.oi_progress_bar = QProgressBar()
        self.oi_progress_bar.setRange(0, 0)  # Indeterminate progress
        self.oi_progress_bar.hide()

        self.oi_status_label = QLabel("Enter a ticker symbol and press Enter to fetch data")

        # Create header frame for totals
        header_frame = QFrame()
        header_layout = QHBoxLayout(header_frame)

        self.oi_total_calls_label = QLabel("Total Call OI: 0")
        self.oi_total_calls_label.setStyleSheet("color: #00FF00; font-size: 16px; font-weight: bold;")

        self.oi_total_puts_label = QLabel("Total Put OI: 0")
        self.oi_total_puts_label.setStyleSheet("color: #FF0000; font-size: 16px; font-weight: bold;")

        header_layout.addWidget(self.oi_total_calls_label)
        header_layout.addSpacing(20)
        header_layout.addWidget(self.oi_total_puts_label)
        header_layout.addStretch()

        # Create chart view
        self.oi_chart_view = OpenInterestChartView()

        # Add widgets to main layout
        layout.addWidget(input_frame)
        layout.addWidget(self.oi_progress_bar)
        layout.addWidget(self.oi_status_label)
        layout.addWidget(header_frame)
        layout.addWidget(self.oi_chart_view)

        # Connect signals
        self.oi_ticker_input.returnPressed.connect(self.fetch_oi_expiry_dates)
        self.oi_fetch_button.clicked.connect(self.fetch_open_interest)
        self.oi_expiry_selector.currentIndexChanged.connect(self.fetch_open_interest)

    # Gamma Exposure methods
    def fetch_gamma_expiry_dates(self):
        """Fetch available expiry dates for the gamma exposure tab"""
        ticker = self.gamma_ticker_input.text().strip()
        if not ticker:
            self.gamma_status_label.setText("Please enter a ticker symbol")
            return

        self.gamma_status_label.setText(f"Fetching expiry dates for {ticker}...")
        self.gamma_progress_bar.show()
        QApplication.processEvents()  # Process events to update UI

        try:
            # Format ticker
            formatted_ticker = format_ticker(ticker)

            # Fetch available expiry dates
            stock = yf.Ticker(formatted_ticker)
            available_dates = stock.options

            if not available_dates:
                self.gamma_status_label.setText("No options data available for this ticker")
                self.gamma_progress_bar.hide()
                return

            # Update expiry selector
            self.gamma_expiry_selector.clear()
            for date in available_dates:
                self.gamma_expiry_selector.addItem(date)

            self.gamma_status_label.setText(f"Found {len(available_dates)} expiry dates")
            self.gamma_progress_bar.hide()

            # Fetch data for the first expiry date
            if available_dates:
                self.fetch_gamma_exposure()

        except Exception as e:
            self.gamma_status_label.setText(f"Error: {str(e)}")
            self.gamma_progress_bar.hide()

    def fetch_gamma_exposure(self):
        """Fetch and display gamma exposure for the selected expiry date"""
        ticker = self.gamma_ticker_input.text().strip()
        expiry_date = self.gamma_expiry_selector.currentText()

        if not ticker or not expiry_date:
            return

        self.gamma_status_label.setText(f"Fetching gamma exposure data for {ticker} ({expiry_date})...")
        self.gamma_progress_bar.show()
        QApplication.processEvents()  # Process events to update UI

        try:
            # Format ticker
            formatted_ticker = format_ticker(ticker)

            # Get current price
            S = get_current_price(formatted_ticker)
            if S is None:
                self.gamma_status_label.setText("Could not fetch current price.")
                self.gamma_progress_bar.hide()
                return

            # Fetch options data
            calls, puts = fetch_options_for_date(formatted_ticker, expiry_date, S)
            if calls.empty or puts.empty:
                self.gamma_status_label.setText("No options data available for the selected date.")
                self.gamma_progress_bar.hide()
                return

            # Calculate Greeks and GEX
            calls, puts = calculate_gamma_exposure(calls, puts, S, expiry_date)

            # Update chart
            title = f"Gamma Exposure by Strike - {ticker} ({expiry_date})"

            # Filter out zero values
            calls_df = calls[['strike', 'GEX']].copy()
            calls_df = calls_df[calls_df['GEX'] != 0]

            puts_df = puts[['strike', 'GEX']].copy()
            puts_df = puts_df[puts_df['GEX'] != 0]

            total_positive, total_negative = self.gamma_chart_view.plot_gamma_exposure(calls_df, puts_df, S, title)

            # Update total labels
            self.gamma_total_positive_label.setText(f"Total Positive GEX: {int(total_positive):,}")
            self.gamma_total_negative_label.setText(f"Total Negative GEX: {int(total_negative):,}")

            self.gamma_status_label.setText(f"Data updated for {ticker} ({expiry_date})")
            self.gamma_progress_bar.hide()

        except Exception as e:
            self.gamma_status_label.setText(f"Error: {str(e)}")
            self.gamma_progress_bar.hide()

    # Open Interest methods
    def fetch_oi_expiry_dates(self):
        """Fetch available expiry dates for the open interest tab"""
        ticker = self.oi_ticker_input.text().strip()
        if not ticker:
            self.oi_status_label.setText("Please enter a ticker symbol")
            return

        self.oi_status_label.setText(f"Fetching expiry dates for {ticker}...")
        self.oi_progress_bar.show()
        QApplication.processEvents()  # Process events to update UI

        try:
            # Format ticker
            formatted_ticker = format_ticker(ticker)

            # Fetch available expiry dates
            stock = yf.Ticker(formatted_ticker)
            available_dates = stock.options

            if not available_dates:
                self.oi_status_label.setText("No options data available for this ticker")
                self.oi_progress_bar.hide()
                return

            # Update expiry selector
            self.oi_expiry_selector.clear()
            for date in available_dates:
                self.oi_expiry_selector.addItem(date)

            self.oi_status_label.setText(f"Found {len(available_dates)} expiry dates")
            self.oi_progress_bar.hide()

            # Fetch data for the first expiry date
            if available_dates:
                self.fetch_open_interest()

        except Exception as e:
            self.oi_status_label.setText(f"Error: {str(e)}")
            self.oi_progress_bar.hide()

    def fetch_open_interest(self):
        """Fetch and display open interest for the selected expiry date"""
        ticker = self.oi_ticker_input.text().strip()
        expiry_date = self.oi_expiry_selector.currentText()

        if not ticker or not expiry_date:
            return

        self.oi_status_label.setText(f"Fetching open interest data for {ticker} ({expiry_date})...")
        self.oi_progress_bar.show()
        QApplication.processEvents()  # Process events to update UI

        try:
            # Format ticker
            formatted_ticker = format_ticker(ticker)

            # Get current price
            S = get_current_price(formatted_ticker)
            if S is None:
                self.oi_status_label.setText("Could not fetch current price.")
                self.oi_progress_bar.hide()
                return

            # Fetch options data
            calls, puts = fetch_options_for_date(formatted_ticker, expiry_date, S)
            if calls.empty or puts.empty:
                self.oi_status_label.setText("No options data available for the selected date.")
                self.oi_progress_bar.hide()
                return

            # Update chart
            title = f"Open Interest by Strike - {ticker} ({expiry_date})"

            # Filter out zero values
            calls_df = calls[['strike', 'openInterest']].copy()
            calls_df = calls_df[calls_df['openInterest'] != 0]

            puts_df = puts[['strike', 'openInterest']].copy()
            puts_df = puts_df[puts_df['openInterest'] != 0]

            total_calls, total_puts = self.oi_chart_view.plot_open_interest(calls_df, puts_df, S, title)

            # Update total labels
            self.oi_total_calls_label.setText(f"Total Call OI: {int(total_calls):,}")
            self.oi_total_puts_label.setText(f"Total Put OI: {int(total_puts):,}")

            self.oi_status_label.setText(f"Data updated for {ticker} ({expiry_date})")
            self.oi_progress_bar.hide()

        except Exception as e:
            self.oi_status_label.setText(f"Error: {str(e)}")
            self.oi_progress_bar.hide()

# Main entry point
if __name__ == "__main__":
    app = QApplication(sys.argv)


    app.setStyleSheet("""
        * {
            outline: 0px;
        }
        QWidget:focus {
            outline: 0px;
        }
        QGraphicsView {
            border: 0px;
            outline: 0px;
            padding: 0px;
            margin: 0px;
            background-color: #121212;
        }
        QLineEdit, QComboBox, QPushButton {
            outline: 0px;
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 4px;
            background-color: #2A2A2A;
            color: #FFFFFF;
        }
        QLineEdit:focus, QComboBox:focus, QPushButton:focus {
            border: 1px solid #3A539B;
            outline: 0px;
        }
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #1E1E1E;
        }
        QTabWidget::tab-bar {
            alignment: center;
        }
        QTabBar {
            outline: 0px;
        }
        QTabBar::tab {
            background-color: #2A2A2A;
            color: #AAAAAA;
            padding: 8px 12px;
            border: 1px solid #555555;
            border-bottom: none;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            outline: 0px;
        }
        QTabBar::tab:selected {
            background-color: #1E1E1E;
            color: #FFFFFF;
            outline: 0px;
        }
        QTabBar::tab:!selected {
            margin-top: 2px;
            outline: 0px;
        }
        QFrame {
            border: 0px;
            outline: 0px;
        }
    """)

    window = CombinedOptionsAnalysisApp()

    # Initialize with default ticker
    window.gamma_ticker_input.setText("SPY")
    window.oi_ticker_input.setText("SPY")

    # Fetch initial data for both tabs
    window.fetch_gamma_expiry_dates()
    window.fetch_oi_expiry_dates()

    window.show()
    sys.exit(app.exec())
