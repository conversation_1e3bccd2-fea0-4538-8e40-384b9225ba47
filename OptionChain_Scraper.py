import asyncio, json, re, sys
from datetime import datetime
from typing import List
from bs4 import BeautifulSoup
import aiohttp, pandas as pd
from dataclasses import dataclass, asdict

@dataclass
class OptionContract:
    strike: float; last_price: float; bid: float; mid: float; ask: float
    volume: int; open_interest: int; implied_volatility: float
    delta: float; gamma: float; theta: float; vega: float; option_type: str
    contract_symbol: str = ""; expiration_date: str = ""; ticker: str = ""
    def to_dict(self): return asdict(self)

class OptionChainScraper:
    def __init__(self, base_url="https://optioncharts.io"):
        self.base_url = base_url; self.session = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'},
            timeout=aiohttp.ClientTimeout(total=30), connector=aiohttp.TCPConnector(ssl=False))
        return self

    async def __aexit__(self, *args):
        if self.session: await self.session.close()

    async def get_data(self, ticker="SPY", expiration_date="2025-07-24", option_type="all") -> List[OptionContract]:
        if not self.session: raise RuntimeError("Use async context manager")

        async with self.session.get(f"{self.base_url}/async/option_chain", params={
            "expiration_dates": f"{expiration_date}:w", "option_type": option_type,
            "strike_range": "all", "ticker": ticker, "view": "list"}) as r:
            return self._parse(await r.text(), ticker, expiration_date) if r.status == 200 else []

    def _parse(self, html: str, ticker: str, exp_date: str) -> List[OptionContract]:
        def safe_num(text, is_int=False):
            try:
                clean = text.replace('$', '').replace(',', '').replace('%', '').strip()
                return (int if is_int else float)(clean) if clean and clean != '-' else 0
            except: return 0

        contracts = []
        for row in BeautifulSoup(html, 'html.parser').find_all('tr', onclick=True):
            if '/option/contract/' not in row.get('onclick', ''): continue
            cells = row.find_all('td')
            if len(cells) < 12: continue

            symbol = re.search(r'/option/contract/([^\']+)', row.get('onclick', '')).group(1) if re.search(r'/option/contract/([^\']+)', row.get('onclick', '')) else ""
            contracts.append(OptionContract(
                strike=safe_num(cells[0].text), last_price=safe_num(cells[1].text),
                bid=safe_num(cells[2].text), mid=safe_num(cells[3].text), ask=safe_num(cells[4].text),
                volume=safe_num(cells[5].text, True), open_interest=safe_num(cells[6].text, True),
                implied_volatility=safe_num(cells[7].text), delta=safe_num(cells[8].text),
                gamma=safe_num(cells[9].text), theta=safe_num(cells[10].text), vega=safe_num(cells[11].text),
                option_type="CALL" if 'C' in symbol[-9:] else "PUT", contract_symbol=symbol,
                expiration_date=exp_date, ticker=ticker))
        return contracts

    def save(self, contracts: List[OptionContract], filename: str = None, fmt="csv"):
        if not contracts: return ""
        base = filename or f"options_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        files = []
        if fmt in ["csv", "both"]:
            file = f"{base}.csv"; pd.DataFrame([c.to_dict() for c in contracts]).to_csv(file, index=False); files.append(file)
        if fmt in ["json", "both"]:
            file = f"{base}.json"; json.dump([c.to_dict() for c in contracts], open(file, 'w'), indent=2); files.append(file)
        return ", ".join(files)

    def summary(self, contracts: List[OptionContract]):
        if not contracts: return print("No contracts")
        df = pd.DataFrame([c.to_dict() for c in contracts])
        calls, puts = len(df[df.option_type == 'CALL']), len(df[df.option_type == 'PUT'])
        print(f"\n=== Option Chain Summary ===")
        print(f"Total Contracts: {len(contracts)}")
        print(f"Calls: {calls} | Puts: {puts}")
        print(f"Strike Range: ${df.strike.min():.0f} - ${df.strike.max():.0f}")
        print(f"Total Volume: {df.volume.sum():,}")
        print(f"Total Open Interest: {df.open_interest.sum():,}")
        if len(contracts) > 0:
            print(f"\nSample Contracts:")
            for contract in contracts[:3]:
                print(f"  {contract.option_type} ${contract.strike} - Last: ${contract.last_price:.2f}, Vol: {contract.volume}, OI: {contract.open_interest}")

async def fetch(ticker="SPY", exp_date="2025-07-24", opt_type="all"):
    async with OptionChainScraper() as scraper:
        contracts = await scraper.get_data(ticker, exp_date, opt_type)
        if contracts:
            scraper.summary(contracts); scraper.save(contracts, f"{ticker}_{exp_date}_{opt_type}", "both")
        return contracts

def get_user_input():
    while True:
        ticker = input("Ticker: ").strip().upper()
        if ticker: break
        print("Invalid ticker symbol.")

    while True:
        expiration = input("Expiration Date (YYYY-MM-DD): ").strip()
        try:
            datetime.strptime(expiration, "%Y-%m-%d"); break
        except ValueError:
            print("Invalid date format.")

    print("1. All\n2. Calls\n3. Puts")
    while True:
        choice = input("Option type (1-3): ").strip()
        if choice in ["", "1"]: return ticker, expiration, "all"
        elif choice == "2": return ticker, expiration, "calls"
        elif choice == "3": return ticker, expiration, "puts"
        else: print("Invalid choice.")

if __name__ == "__main__":
    if len(sys.argv) >= 3:
        asyncio.run(fetch(sys.argv[1].upper(), sys.argv[2], sys.argv[3] if len(sys.argv) > 3 else "all"))
    else:
        print("Option Chain Scraper")
        print("Usage: python OptionChain_Scraper.py TICKER YYYY-MM-DD [all|calls|puts]")
        print("Example: python OptionChain_Scraper.py SPY 2025-07-24 all\n")

        ticker, expiration, option_type = get_user_input()
        asyncio.run(fetch(ticker, expiration, option_type))

        while True:
            another = input("\nContinue? (y/n): ").strip().lower()
            if another in ['y', 'yes']:
                ticker, expiration, option_type = get_user_input()
                asyncio.run(fetch(ticker, expiration, option_type))
            elif another in ['n', 'no']: break
            else: print("Invalid input.")