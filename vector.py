"""
Vector Module - Combined Vector Calculation, Display, and Chart Integration

This module combines functionality from:
- vector.py: Core vector calculation functions with caching
- vector_display.py: Enhanced vector display component with modern UI and animations
- vector_rebase_loading.py: Vector chart integration with loading screens

Features:
- Vector calculation using Donchian channel midpoint
- Enhanced vector display with animations
- Chart integration with loading screen support
- LRU caching for performance optimization
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg
import yfinance as yf

# Import LRUCache if available, otherwise define a simple cache
try:
    from lru_cache import LRUCache
except ImportError:
    # Simple LRU cache implementation if the external one is not available
    class LRUCache:
        def __init__(self, capacity=20):
            self.capacity = capacity
            self._cache = {}
            self._order = []

        def get(self, key):
            if key in self._cache:
                # Move to end (most recently used)
                self._order.remove(key)
                self._order.append(key)
                return self._cache[key]
            return None

        def put(self, key, value):
            if key in self._cache:
                # Update existing item
                self._cache[key] = value
                self._order.remove(key)
                self._order.append(key)
            else:
                # Add new item
                if len(self._cache) >= self.capacity:
                    # Remove least recently used
                    oldest = self._order.pop(0)
                    del self._cache[oldest]
                self._cache[key] = value
                self._order.append(key)

# Global cache for vector calculations
_vector_cache = LRUCache(20)

# ============================================================================
# CORE VECTOR CALCULATION FUNCTIONS
# ============================================================================

def compute_wave_line(data: pd.DataFrame, wave: int = 10, min_periods: int = 1, column: str = 'close') -> pd.Series:
    """
    Compute the midpoint of the Donchian Channel over the close prices.

    Parameters
    ----------
    data : pd.DataFrame
        Must contain a price column (default: 'close').
    wave : int
        Lookback period for the Donchian Channel.
    min_periods : int
        Minimum number of observations required to calculate the statistic.
    column : str
        Column name to use for calculations (default: 'close', but can be 'Close' for different conventions).

    Returns
    -------
    pd.Series
        The stepped-line midpoints.
    """
    # Handle case sensitivity for column names
    price_col = column
    if column.lower() == 'close' and column not in data.columns and 'Close' in data.columns:
        price_col = 'Close'
    elif column.lower() == 'close' and column not in data.columns:
        raise ValueError(f"Column '{column}' not found in data. Available columns: {data.columns.tolist()}")

    # Calculate the Donchian channel
    hi = data[price_col].rolling(window=wave, min_periods=min_periods).max()
    lo = data[price_col].rolling(window=wave, min_periods=min_periods).min()
    wave_price = (hi + lo) / 2

    # Fill NaN values at the beginning with the first valid value
    wave_price = wave_price.bfill()

    return wave_price

def calculate_vector(data: pd.DataFrame, length: int = 20, column: str = 'close') -> pd.Series:
    """
    Calculate the vector using Donchian channel midpoint with caching.

    This is the main entry point for vector calculation. It uses compute_wave_line
    but adds caching for better performance.

    Parameters
    ----------
    data : pd.DataFrame
        The price data with a close/Close column.
    length : int
        The lookback period for the Donchian channel.
    column : str
        Column name to use for calculations (default: 'close', but can be 'Close' for different conventions).

    Returns
    -------
    pd.Series
        The calculated vector.
    """
    if len(data) < length:
        return pd.Series(index=data.index)

    # Create cache key
    cache_key = (id(data), length, column)

    # Check if result is in cache
    cached_result = _vector_cache.get(cache_key)
    if cached_result is not None:
        return cached_result

    # Calculate vector using compute_wave_line
    result = compute_wave_line(data, wave=length, column=column)

    # Cache the result
    _vector_cache.put(cache_key, result)

    return result


# ============================================================================
# ENHANCED VECTOR DISPLAY COMPONENT
# ============================================================================

class EnhancedVectorDisplay:
    """
    Enhanced vector display component with modern UI and animations.

    This class provides a modern, visually appealing display for vector values
    in the imprints widget, with smooth animations and visual feedback.
    """

    def __init__(self, plot_widget, chart_colors=None):
        """
        Initialize the enhanced vector display.

        Args:
            plot_widget: The pyqtgraph PlotWidget to display the vector in
            chart_colors: Dictionary of colors to use for the display
        """
        self.plot_widget = plot_widget
        self.chart_colors = chart_colors or {
            'background': '#121620',  # Midnight Ocean background
            'vector': '#9C27B0',  # Material Design Purple
            'text': '#E0E0E0',    # Light gray text
            'bullish': '#4CAF50', # Material Design Green
            'bearish': '#F44336', # Material Design Red
        }

        # Store items for later removal/update
        self.vector_lines = []
        self.vector_labels = []
        self.animation_timers = []

        # Animation properties
        self.animation_duration = 300  # milliseconds
        self.animation_steps = 10

    def clear(self):
        """Clear all vector display items from the plot widget."""
        # Stop any running animations
        for timer in self.animation_timers:
            if timer.isActive():
                timer.stop()
        self.animation_timers = []

        # Remove vector lines
        for line in self.vector_lines:
            self.plot_widget.removeItem(line)
        self.vector_lines = []

        # Remove vector labels
        for label in self.vector_labels:
            self.plot_widget.removeItem(label)
        self.vector_labels = []

    def draw_vector_line(self, x_range, level, cycle_type=None, is_closed=False, animate=True):
        """
        Draw a vector line with modern styling and optional animation.

        Args:
            x_range: Tuple of (left_edge, right_edge) for the line
            level: Y-coordinate for the line
            cycle_type: 'bullish' or 'bearish' to determine color
            is_closed: Whether this vector level is closed
            animate: Whether to animate the line appearance

        Returns:
            The created line item
        """
        left_edge, right_edge = x_range

        # Determine line color based on cycle type
        if cycle_type == 'bullish':
            color = self.chart_colors['bullish'] if is_closed else '#90EE90'  # Light green if not closed
        elif cycle_type == 'bearish':
            color = self.chart_colors['bearish'] if is_closed else '#FF6666'  # Light red if not closed
        else:
            color = self.chart_colors['vector']

        # Create gradient for the line
        gradient = QtGui.QLinearGradient(left_edge, level, right_edge, level)
        gradient.setColorAt(0, QtGui.QColor(color).darker(120))
        gradient.setColorAt(0.5, QtGui.QColor(color))
        gradient.setColorAt(1, QtGui.QColor(color).darker(120))

        # Create pen with gradient
        pen = pg.mkPen(color=color, width=3)

        if animate:
            # Start with a short line and animate to full width
            mid_x = (left_edge + right_edge) / 2
            start_x = mid_x - (right_edge - left_edge) * 0.1
            end_x = mid_x + (right_edge - left_edge) * 0.1

            line = self.plot_widget.plot(
                [start_x, end_x], [level, level],
                pen=pen
            )

            # Store original and target coordinates for animation
            line.original_coords = ([start_x, end_x], [level, level])
            line.target_coords = ([left_edge, right_edge], [level, level])
            line.current_step = 0

            # Create and start animation timer
            timer = QtCore.QTimer()
            timer.timeout.connect(lambda line=line, timer=timer: self._animate_line(line, timer))
            timer.start(self.animation_duration / self.animation_steps)
            self.animation_timers.append(timer)
        else:
            # Create line without animation
            line = self.plot_widget.plot(
                [left_edge, right_edge], [level, level],
                pen=pen
            )

        self.vector_lines.append(line)
        return line

    def _animate_line(self, line, timer):
        """
        Animate a line from its original to target coordinates.

        Args:
            line: The line item to animate
            timer: The QTimer controlling the animation
        """
        line.current_step += 1
        progress = line.current_step / self.animation_steps

        if progress >= 1.0:
            # Animation complete, set final coordinates
            line.setData(line.target_coords[0], line.target_coords[1])
            timer.stop()
            return

        # Calculate intermediate coordinates
        orig_x, orig_y = line.original_coords
        target_x, target_y = line.target_coords

        # Use easing function for smoother animation
        t = self._ease_out_cubic(progress)

        current_x = [
            orig_x[0] + (target_x[0] - orig_x[0]) * t,
            orig_x[1] + (target_x[1] - orig_x[1]) * t
        ]

        # Update line coordinates
        line.setData(current_x, target_y)

    def _ease_out_cubic(self, t):
        """
        Cubic easing function for smoother animations.

        Args:
            t: Progress value from 0 to 1

        Returns:
            Eased value
        """
        return 1 - (1 - t) ** 3

    def add_vector_label(self, position, price, cycle_type=None, is_closed=False, animate=True):
        """
        Add a modern styled label for the vector price.

        Args:
            position: (x, y) coordinates for the label
            price: The price value to display
            cycle_type: 'bullish' or 'bearish' to determine color
            is_closed: Whether this vector level is closed
            animate: Whether to animate the label appearance

        Returns:
            The created label item
        """
        x, y = position

        # Determine text color based on cycle type
        if cycle_type == 'bullish':
            text_color = "#00FF00" if is_closed else "#90EE90"
        elif cycle_type == 'bearish':
            text_color = "#FF6666" if is_closed else "#FF3333"
        else:
            text_color = self.chart_colors['text']

        # Format price string
        price_str = f"{price:,.2f}"

        # Create modern styled label with rounded corners and shadow
        label = pg.TextItem(
            html=f'''
            <div style="
                background-color: rgba(0,0,0,0.7);
                padding: 3px 6px;
                border-radius: 4px;
                font-size: 9pt;
                color: {text_color};
                border: 2px solid rgba(255,255,255,0.4);
            ">
                {price_str}
            </div>
            ''',
            anchor=(0.5, 0.5)
        )

        if animate:
            # Start with opacity 0 and animate to full opacity
            label.setOpacity(0)
            label.setPos(x, y)
            self.plot_widget.addItem(label)

            # Create and start fade-in animation
            label.current_step = 0
            timer = QtCore.QTimer()
            timer.timeout.connect(lambda label=label, timer=timer: self._animate_label(label, timer))
            timer.start(self.animation_duration / self.animation_steps)
            self.animation_timers.append(timer)
        else:
            # Add label without animation
            label.setPos(x, y)
            self.plot_widget.addItem(label)

        self.vector_labels.append(label)
        return label

    def _animate_label(self, label, timer):
        """
        Animate a label's opacity from 0 to 1.

        Args:
            label: The label item to animate
            timer: The QTimer controlling the animation
        """
        label.current_step += 1
        progress = label.current_step / self.animation_steps

        if progress >= 1.0:
            # Animation complete, set final opacity
            label.setOpacity(1.0)
            timer.stop()
            return

        # Use easing function for smoother animation
        opacity = self._ease_out_cubic(progress)
        label.setOpacity(opacity)

    def draw_vector_cluster(self, x_data, y_data, min_level, max_level, avg_level,
                           cluster_size, min_price=None, max_price=None, color="yellow", opacity=50):
        """
        Draw a vector cluster with modern styling.

        Args:
            x_data: X-coordinates for the cluster polygon
            y_data: Y-coordinates for the cluster polygon
            min_level: Minimum level in the cluster
            max_level: Maximum level in the cluster
            avg_level: Average level in the cluster
            cluster_size: Number of levels in the cluster
            min_price: Minimum price in the cluster
            max_price: Maximum price in the cluster
            color: Color name for the cluster
            opacity: Opacity percentage (0-100)

        Returns:
            Tuple of (cluster_rect, cluster_label)
        """
        # Color mapping
        color_map = {
            "yellow": (255, 255, 0),
            "red": (255, 0, 0),
            "green": (0, 255, 0),
            "blue": (0, 0, 255),
            "cyan": (0, 255, 255),
            "magenta": (255, 0, 255)
        }
        rgb = color_map.get(color, (255, 255, 0))

        # Create cluster rectangle with gradient fill
        cluster_rect = pg.PlotDataItem(
            x=x_data,
            y=y_data,
            pen=pg.mkPen(color=color, width=2, style=QtCore.Qt.PenStyle.DashLine),
            fillLevel=min_level,
            brush=pg.mkBrush(color=(*rgb, opacity))
        )

        # Add cluster to plot
        self.plot_widget.addItem(cluster_rect)
        self.vector_lines.append(cluster_rect)

        # Create cluster label if prices are provided
        if min_price is not None and max_price is not None:
            cluster_text = f"Cluster ({cluster_size}): {min_price:.2f} - {max_price:.2f}"

            # Create modern styled label
            cluster_label = pg.TextItem(
                html=f'''
                <div style="
                    background-color: rgba(0,0,0,0.7);
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 9pt;
                    color: {color};
                    border: 2px solid rgba(255,255,255,0.4);
                ">
                    {cluster_text}
                </div>
                ''',
                anchor=(0, 0.5)
            )

            # Position label at the right edge of the cluster at average level
            cluster_label.setPos(x_data[1] * 0.95, avg_level)
            self.plot_widget.addItem(cluster_label)
            self.vector_labels.append(cluster_label)

            return cluster_rect, cluster_label

        return cluster_rect, None


# ============================================================================
# VECTOR REBASE CHART WITH LOADING INTEGRATION
# ============================================================================

class VectorRebaseChart(QtWidgets.QWidget):
    def __init__(self, use_external_loading=False, use_compact_loading=False):
        """
        Initialize the VectorRebaseChart with loading screen integration.

        Args:
            use_external_loading: Whether to use the external loading screen
            use_compact_loading: Whether to use the compact loading screen with data-driven icon
        """
        super().__init__()
        self.chart_colors = {
            'background': '#1e1e1e',
            'bullish': '#4CAF50',  # Material Design Green
            'bearish': '#F44336',  # Material Design Red
            'vector': '#9C27B0',   # Material Design Purple (changed from blue)
            'pivot': '#FFC107',    # Material Design Amber
            'text': '#E0E0E0',     # Light gray text
            'primary_accent': '#007acc'  # Added for the loading screen
            # ... other colors ...
        }

        # Initialize UI components
        # ... existing code ...

        # Loading screen functionality removed
        self.use_external_loading = False
        self.loading_screen = None

    def fetch_data(self):
        """Fetch data for the chart with loading screen."""
        symbol = getattr(self, 'symbol_input', None)
        if not symbol or not hasattr(symbol, 'text'):
            print("Warning: symbol_input not properly initialized")
            return

        symbol_text = symbol.text().strip().upper()
        if not symbol_text:
            QtWidgets.QMessageBox.warning(self, "Error", "Please enter a symbol")
            if hasattr(self, 'update_timer'):
                self.update_timer.stop()
            if hasattr(self, 'iv_update_timer'):
                self.iv_update_timer.stop()
            return

        try:
            # Loading screen functionality removed

            # Apply any pending preset before fetching data
            if hasattr(self, 'pending_preset') and self.pending_preset:
                preset_text = self.pending_preset
                print(f"Applying pending preset: {preset_text}")

                # Apply the appropriate preset settings
                if preset_text == "W10/15m":
                    self.apply_w10_15m_settings()
                elif preset_text == "W7/60m":
                    self.apply_w7_60m_settings()
                elif preset_text == "W7/Daily":
                    self.apply_w7_daily_settings()
                # ... other presets ...

                # Clear the pending preset
                self.pending_preset = None

            # Set wait cursor
            QtWidgets.QApplication.setOverrideCursor(QtCore.Qt.CursorShape.WaitCursor)

            # Use data dispatcher instead of direct yfinance call
            try:
                from data_dispatcher import DataFetchThread

                days = getattr(self, 'days_spin', None)
                days_value = days.value() if days and hasattr(days, 'value') else 30

                interval_combo = getattr(self, 'timeframe_combo', None)
                interval = interval_combo.currentText() if interval_combo and hasattr(interval_combo, 'currentText') else '1d'

                # Loading screen functionality removed

                # Create a data fetch thread to get Yahoo Finance data
                fetch_thread = DataFetchThread(symbol_text, interval, days_value, 'yfinance')
                self.data = fetch_thread._fetch_yfinance_data()
            except ImportError:
                # Fallback to direct yfinance if data_dispatcher is not available
                print("Warning: data_dispatcher not available, using direct yfinance")
                ticker = yf.Ticker(symbol_text)
                days_value = 30  # default
                self.data = ticker.history(period=f"{days_value}d")

            if self.data.empty:
                try:
                    from dialog_manager import warning
                    warning(self, "Error", "No data returned for symbol")
                except ImportError:
                    QtWidgets.QMessageBox.warning(self, "Error", "No data returned for symbol")

                QtWidgets.QApplication.restoreOverrideCursor()

                # Loading screen functionality removed
                return

            if self.data.index.tz is not None:
                self.data.index = self.data.index.tz_localize(None)

            # Loading screen functionality removed

            # Process data
            # ... existing data processing code ...

            # Loading screen functionality removed

            # Plot data
            if hasattr(self, 'plot_data'):
                self.plot_data()

            # Start update timer
            if hasattr(self, 'update_timer'):
                self.update_timer.start()

            # Restore cursor
            QtWidgets.QApplication.restoreOverrideCursor()

            # Loading screen functionality removed

        except Exception as e:
            # Handle errors
            try:
                from dialog_manager import critical
                critical(self, "Error", f"Error fetching data: {str(e)}")
            except ImportError:
                QtWidgets.QMessageBox.critical(self, "Error", f"Error fetching data: {str(e)}")

            QtWidgets.QApplication.restoreOverrideCursor()

            # Loading screen functionality removed

    def update_data(self):
        """Update data periodically."""
        symbol_input = getattr(self, 'symbol_input', None)
        if not symbol_input or not hasattr(symbol_input, 'text') or not symbol_input.text().strip():
            return

        try:
            # For updates, we don't show the loading screen to avoid flickering
            # during regular updates, but we could show it for longer operations

            symbol = symbol_input.text().strip().upper()

            # Use data dispatcher instead of direct yfinance call
            try:
                from data_dispatcher import DataFetchThread

                interval_combo = getattr(self, 'timeframe_combo', None)
                interval = interval_combo.currentText() if interval_combo and hasattr(interval_combo, 'currentText') else '1d'

                # Create a data fetch thread to get Yahoo Finance data for updates
                fetch_thread = DataFetchThread(symbol, interval, 1, 'yfinance')  # 1 day for updates
                new_data = fetch_thread._fetch_yfinance_data()
            except ImportError:
                # Fallback to direct yfinance if data_dispatcher is not available
                ticker = yf.Ticker(symbol)
                new_data = ticker.history(period="1d")

            if new_data.empty:
                return

            if new_data.index.tz is not None:
                new_data.index = new_data.index.tz_localize(None)

            if len(new_data) > 0 and (self.data is None or new_data.index[-1] > self.data.index[-1]):
                if self.data is not None:
                    combined = pd.concat([self.data, new_data])
                    self.data = combined[~combined.index.duplicated(keep='last')]
                else:
                    self.data = new_data

                if hasattr(self, 'plot_data'):
                    self.plot_data()

        except Exception as e:
            print(f"Error updating data: {str(e)}")
            if hasattr(self, 'update_timer'):
                self.update_timer.stop()

    # Other methods remain unchanged


# ============================================================================
# EXAMPLE USAGE AND TESTING
# ============================================================================

if __name__ == "__main__":
    # Example: load your OHLC data into a DataFrame with a 'close' column
    # For demonstration, we'll simulate some data:
    dates = pd.date_range(start="2025-01-01", periods=100, freq="D")
    close_prices = pd.Series(100 + np.random.randn(100).cumsum(), index=dates)
    df = pd.DataFrame({'close': close_prices})

    # Compute the line
    wave_length = 10
    df['wave_line'] = compute_wave_line(df, wave=wave_length)

    # Plot as a stepped line using matplotlib
    import matplotlib.pyplot as plt
    plt.figure(figsize=(12, 6))
    plt.plot(df.index, df['close'], label='Close', alpha=0.5)
    plt.step(df.index, df['wave_line'], where='post',
             linewidth=2, color='purple', label=f'Wave Line ({wave_length})')
    plt.title("Donchian Channel Midpoint (Stepped Line)")
    plt.xlabel("Date")
    plt.ylabel("Price")
    plt.legend()
    plt.show()

    print("Vector module loaded successfully!")
    print("Available functions:")
    print("- compute_wave_line(): Calculate Donchian channel midpoint")
    print("- calculate_vector(): Calculate vector with caching")
    print("- EnhancedVectorDisplay: Modern vector display component")
    print("- VectorRebaseChart: Chart with loading screen integration")
