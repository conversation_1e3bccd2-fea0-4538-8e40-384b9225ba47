from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg
import pandas as pd
import numpy as np
import logging
import math
from scipy.interpolate import <PERSON>ubicSpline, splrep, splev
from scipy.stats import norm

from crosshair_utility import add_price_only_crosshair
try:
    from sklearn.cluster import DBSCAN
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("scikit-learn not available. DBSCAN clustering will be disabled.")

try:
    from synthetic_options import SyntheticOptionsEngine
    SYNTHETIC_OPTIONS_AVAILABLE = True
except ImportError:
    SYNTHETIC_OPTIONS_AVAILABLE = False
    logging.warning("Synthetic options module not available. Synthetic pricing will be disabled.")

logger = logging.getLogger(__name__)

# SABR Model Implementation
class SABRModel:
    """
    SABR (Stochastic Alpha Beta Rho) volatility model implementation.

    The SABR model is used for modeling the evolution of forward rates and their volatilities.
    It provides a more sophisticated approach than Black-Scholes for options pricing,
    particularly for interest rate derivatives and equity options with volatility smile.

    SABR model parameters:
    - alpha: Initial volatility level
    - beta: Elasticity parameter (0 <= beta <= 1)
    - rho: Correlation between forward rate and volatility (-1 <= rho <= 1)
    - nu: Volatility of volatility (vol-of-vol)
    """

    def __init__(self, alpha=0.2, beta=0.5, rho=-0.3, nu=0.4):
        """
        Initialize SABR model with default parameters.

        Args:
            alpha: Initial volatility level (default: 0.2)
            beta: Elasticity parameter (default: 0.5)
            rho: Correlation parameter (default: -0.3)
            nu: Volatility of volatility (default: 0.4)
        """
        self.alpha = alpha
        self.beta = beta
        self.rho = rho
        self.nu = nu

    def sabr_volatility(self, F, K, T):
        """
        Calculate SABR implied volatility.

        Args:
            F: Forward price
            K: Strike price
            T: Time to expiration (in years)

        Returns:
            float: SABR implied volatility
        """
        try:
            # Handle edge cases
            if T <= 0:
                return 0.0
            if abs(F - K) < 1e-10:  # ATM case
                return self._sabr_atm_volatility(F, T)

            # SABR volatility formula
            alpha, beta, rho, nu = self.alpha, self.beta, self.rho, self.nu

            # Calculate intermediate values
            FK = F * K
            logFK = math.log(F / K)

            # Calculate z and x(z)
            z = (nu / alpha) * (FK ** ((1 - beta) / 2)) * logFK

            if abs(z) < 1e-10:
                x_z = 1.0
            else:
                sqrt_term = 1 - 2 * rho * z + z * z
                if sqrt_term <= 0:
                    sqrt_term = 1e-10
                x_z = z / math.log((math.sqrt(sqrt_term) - rho + z) / (1 - rho))

            # Calculate the main volatility components
            numerator = alpha

            # First denominator term
            denom1 = (FK ** ((1 - beta) / 2))

            # Second denominator term (volatility smile adjustment)
            beta_term = (1 - beta) ** 2 / 24 * (logFK ** 2)
            rho_term = rho * beta * nu * alpha / (4 * (FK ** ((1 - beta) / 2)))
            nu_term = (2 - 3 * rho ** 2) * nu ** 2 / 24

            denom2 = 1 + (beta_term + rho_term + nu_term) * T

            # Final SABR volatility
            sabr_vol = (numerator / denom1) * x_z / denom2

            # Ensure positive volatility
            return max(sabr_vol, 1e-6)

        except Exception as e:
            logger.warning(f"Error calculating SABR volatility: {e}")
            return 0.2  # Fallback to 20% volatility

    def _sabr_atm_volatility(self, F, T):
        """Calculate SABR volatility at-the-money (F = K)."""
        try:
            alpha, beta, nu = self.alpha, self.beta, self.nu

            # ATM volatility formula
            atm_vol = alpha / (F ** (1 - beta))

            # Time-dependent adjustment
            time_adj = 1 + ((2 - 3 * self.rho ** 2) * nu ** 2 / 24) * T

            return atm_vol / time_adj

        except Exception as e:
            logger.warning(f"Error calculating SABR ATM volatility: {e}")
            return 0.2

    def calibrate_to_market_data(self, market_data):
        """
        Calibrate SABR parameters to market implied volatilities.

        Args:
            market_data: List of tuples (strike, market_iv, time_to_expiry, forward_price)

        Returns:
            dict: Calibrated SABR parameters
        """
        try:
            if not market_data:
                return {'alpha': self.alpha, 'beta': self.beta, 'rho': self.rho, 'nu': self.nu}

            def objective_function(params):
                alpha, beta, rho, nu = params
                # Ensure parameter bounds
                if not (0 < alpha < 2 and 0 <= beta <= 1 and -1 <= rho <= 1 and 0 < nu < 2):
                    return 1e6

                # Temporarily set parameters
                old_params = (self.alpha, self.beta, self.rho, self.nu)
                self.alpha, self.beta, self.rho, self.nu = alpha, beta, rho, nu

                total_error = 0
                for strike, market_iv, T, F in market_data:
                    try:
                        sabr_iv = self.sabr_volatility(F, strike, T)
                        error = (sabr_iv - market_iv) ** 2
                        total_error += error
                    except:
                        total_error += 1e6

                # Restore old parameters
                self.alpha, self.beta, self.rho, self.nu = old_params
                return total_error

            # Initial guess
            initial_params = [self.alpha, self.beta, self.rho, self.nu]

            # Simple grid search for robustness
            best_params = initial_params
            best_error = objective_function(initial_params)

            # Try different parameter combinations
            for alpha in [0.1, 0.2, 0.3]:
                for beta in [0.3, 0.5, 0.7]:
                    for rho in [-0.5, -0.3, 0.0, 0.3]:
                        for nu in [0.2, 0.4, 0.6]:
                            params = [alpha, beta, rho, nu]
                            error = objective_function(params)
                            if error < best_error:
                                best_error = error
                                best_params = params

            # Update parameters
            self.alpha, self.beta, self.rho, self.nu = best_params

            logger.info(f"SABR calibration completed. Parameters: alpha={self.alpha:.3f}, beta={self.beta:.3f}, rho={self.rho:.3f}, nu={self.nu:.3f}")

            return {
                'alpha': self.alpha,
                'beta': self.beta,
                'rho': self.rho,
                'nu': self.nu,
                'calibration_error': best_error
            }

        except Exception as e:
            logger.error(f"Error calibrating SABR model: {e}")
            return {'alpha': self.alpha, 'beta': self.beta, 'rho': self.rho, 'nu': self.nu}

def calculate_sabr_option_price(flag, S, K, T, r=0.05, sabr_model=None):
    """
    Calculate option price using SABR model within Black-Scholes framework.

    Args:
        flag: 'c' for call, 'p' for put
        S: Current stock price (used as forward price)
        K: Strike price
        T: Time to expiration (in years)
        r: Risk-free rate (default: 0.05)
        sabr_model: SABRModel instance (if None, creates default)

    Returns:
        tuple: (option_price, sabr_implied_volatility)
    """
    try:
        if sabr_model is None:
            sabr_model = SABRModel()

        # Use current price as forward price approximation
        F = S * math.exp(r * T)

        # Get SABR implied volatility
        sabr_iv = sabr_model.sabr_volatility(F, K, T)

        # Use SABR volatility in Black-Scholes framework
        d1 = (math.log(S / K) + (r + 0.5 * sabr_iv ** 2) * T) / (sabr_iv * math.sqrt(T))
        d2 = d1 - sabr_iv * math.sqrt(T)

        if flag.lower() == 'c':
            # Call option
            price = S * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
        else:
            # Put option
            price = K * math.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)

        return max(price, 0.0), sabr_iv

    except Exception as e:
        logger.error(f"Error calculating SABR option price: {e}")
        # Fallback to simple Black-Scholes with 20% volatility
        sigma = 0.2
        d1 = (math.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * math.sqrt(T))
        d2 = d1 - sigma * math.sqrt(T)

        if flag.lower() == 'c':
            price = S * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
        else:
            price = K * math.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)

        return max(price, 0.0), sigma

# Import theme colors
try:
    import theme
    THEME_COLORS = theme.DEFAULT
except ImportError:
    # Fallback theme colors if theme module is not available
    THEME_COLORS = {
        'background': '#1e1e1e',           # Dark gray background
        'control_panel': '#2d2d2d',        # Lighter gray control panels
        'borders': '#3e3e3e',              # Border color
        'text': '#e0e0e0',                 # Light gray text
        'primary_accent': '#007acc',       # Primary blue accent
        'secondary_accent': '#0098ff',     # Secondary blue accent (lighter)
        'pressed_accent': '#005c99',       # Pressed state blue (darker)
        'highlight': '#FFC107',            # Material Design Amber
        'selection': '#2979FF',            # Selection highlight color
        'button_radius': '4px',            # Button corner radius
        'button_border': '1px solid #4A4A4A',  # Button border for depth effect
        'bullish': '#4CAF50',              # Material Design Green
        'bearish': '#F44336',              # Material Design Red
    }


class EditableZoneArrowButton(QtWidgets.QPushButton):
    """Small arrow button for navigating zones to confluence points."""

    def __init__(self, direction, parent_zone, zone_size):
        super().__init__()
        self.direction = direction  # 'up' or 'down'
        self.parent_zone = parent_zone

        # Calculate adaptive button size based on zone dimensions
        zone_width = zone_size.get('width', 100)
        zone_height = zone_size.get('height', 50)

        # Button size should be a small fraction of zone size
        button_width = max(8, min(zone_width * 0.15, 16))  # 15% of zone width, min 8px, max 16px
        button_height = max(6, min(zone_height * 0.2, 12))  # 20% of zone height, min 6px, max 12px

        self.setFixedSize(int(button_width), int(button_height))

        # Set arrow text based on direction
        arrow_text = "▲" if direction == 'up' else "▼"
        self.setText(arrow_text)

        # Calculate adaptive font size
        font_size = max(6, min(button_height * 0.7, 9))  # 70% of button height, min 6px, max 9px

        # Style the button with adaptive sizing
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: rgba(255, 255, 255, 180);
                border: 1px solid #666;
                border-radius: 2px;
                font-size: {int(font_size)}px;
                font-weight: bold;
                color: #333;
                padding: 0px;
                margin: 0px;
            }}
            QPushButton:hover {{
                background-color: rgba(255, 255, 255, 220);
                border: 1px solid #333;
            }}
            QPushButton:pressed {{
                background-color: rgba(200, 200, 200, 180);
            }}
        """)

        # Connect click to navigation
        self.clicked.connect(self.navigate_to_confluence)

    def navigate_to_confluence(self):
        """Navigate the zone boundary to the nearest confluence point."""
        if self.parent_zone:
            self.parent_zone.navigate_to_nearest_confluence(self.direction)


class EditableDensityZone(pg.QtWidgets.QGraphicsRectItem):
    """
    Editable density zone with y-axis editing capability and navigation buttons.
    """

    def __init__(self, zone_id, zone_bounds, zone_color, zone_type, area_name,
                 primary_element, combining_elements, density_graph_tab):
        """
        Initialize an editable density zone.

        Args:
            zone_id: Unique identifier for the zone
            zone_bounds: Dictionary with zone bounds (min_x, max_x, min_y, max_y)
            zone_color: Color scheme for the zone ('orange', 'red', etc.)
            zone_type: Type of zone (e.g., 'orange_intersect_cluster')
            area_name: Name of the zone area
            primary_element: Primary element data
            combining_elements: List of combining elements
            density_graph_tab: Reference to the parent DensityGraphTab
        """
        # Initialize the rectangle
        left = zone_bounds['min_x']
        bottom = zone_bounds['min_y']
        width = zone_bounds['max_x'] - zone_bounds['min_x']
        height = zone_bounds['max_y'] - zone_bounds['min_y']

        super().__init__(left, bottom, width, height)

        # Store zone properties
        self.zone_id = zone_id
        self.zone_bounds = zone_bounds.copy()
        self.original_zone_bounds = zone_bounds.copy()  # Store original bounds for reset functionality
        self.zone_color = zone_color
        self.zone_type = zone_type
        self.area_name = area_name
        self.primary_element = primary_element
        self.combining_elements = combining_elements
        self.density_graph_tab = density_graph_tab

        # Editing state
        self.is_editing = False
        self.edit_mode_enabled = False  # True when zone is in edit mode (after double-click)
        self.drag_edge = None  # 'top' or 'bottom'
        self.drag_start_y = None

        # Set up zone styling
        self.setup_zone_styling()

        # Enable mouse interaction
        self.setAcceptHoverEvents(True)
        self.setFlag(pg.QtWidgets.QGraphicsItem.GraphicsItemFlag.ItemIsSelectable, True)

        # Create navigation buttons
        self.arrow_buttons = []
        self.create_navigation_buttons()

    def setup_zone_styling(self):
        """Set up the visual styling for the zone."""
        if self.zone_color == 'orange':
            zone_pen = pg.mkPen(color='orange', width=2)
            zone_brush = pg.mkBrush(color=(255, 165, 0, 30))  # Light orange semi-transparent
        elif self.zone_color == 'red':
            zone_pen = pg.mkPen(color='red', width=2)
            zone_brush = pg.mkBrush(color=(255, 0, 0, 30))  # Light red semi-transparent
        elif self.zone_color == 'cyan':
            zone_pen = pg.mkPen(color='cyan', width=2)
            zone_brush = pg.mkBrush(color=(0, 255, 255, 30))  # Light cyan semi-transparent
        elif self.zone_color == '#4caf50':  # Green for support zones
            zone_pen = pg.mkPen(color='#4caf50', width=2)
            zone_brush = pg.mkBrush(color=(76, 175, 80, 30))  # Light green semi-transparent
        elif self.zone_color == '#f44336':  # Red for resistance zones
            zone_pen = pg.mkPen(color='#f44336', width=2)
            zone_brush = pg.mkBrush(color=(244, 67, 54, 30))  # Light red semi-transparent
        elif self.zone_color.startswith('#'):  # Handle other hex colors
            zone_pen = pg.mkPen(color=self.zone_color, width=2)
            # Convert hex to RGB with transparency
            hex_color = self.zone_color.lstrip('#')
            if len(hex_color) == 6:
                r = int(hex_color[0:2], 16)
                g = int(hex_color[2:4], 16)
                b = int(hex_color[4:6], 16)
                zone_brush = pg.mkBrush(color=(r, g, b, 30))
            else:
                zone_brush = pg.mkBrush(color=(128, 128, 128, 30))  # Fallback to gray
        else:
            zone_pen = pg.mkPen(color='gray', width=2)
            zone_brush = pg.mkBrush(color=(128, 128, 128, 30))  # Light gray semi-transparent

        self.setPen(zone_pen)
        self.setBrush(zone_brush)

    def create_navigation_buttons(self):
        """Create arrow buttons for zone navigation."""
        # Clear existing buttons
        for button in self.arrow_buttons:
            if button.scene():
                button.scene().removeItem(button)
        self.arrow_buttons.clear()

        # Calculate zone size for adaptive button sizing
        zone_rect = self.rect()
        zone_size = {
            'width': zone_rect.width(),
            'height': zone_rect.height()
        }

        # Create up arrow button (for top boundary)
        up_button = EditableZoneArrowButton('up', self, zone_size)
        up_proxy = pg.QtWidgets.QGraphicsProxyWidget(self)
        up_proxy.setWidget(up_button)

        # Create down arrow button (for bottom boundary)
        down_button = EditableZoneArrowButton('down', self, zone_size)
        down_proxy = pg.QtWidgets.QGraphicsProxyWidget(self)
        down_proxy.setWidget(down_button)

        # Position buttons inside the zone
        self.position_navigation_buttons(up_proxy, down_proxy)

        # Initially hide buttons (they will be shown when entering edit mode)
        up_proxy.setVisible(False)
        down_proxy.setVisible(False)

        # Store references
        self.arrow_buttons = [up_proxy, down_proxy]
        self.up_button = up_button
        self.down_button = down_button

    def position_navigation_buttons(self, up_proxy, down_proxy):
        """Position the navigation buttons inside the zone with collision detection."""
        zone_rect = self.rect()

        # Calculate adaptive spacing based on zone size
        zone_height = zone_rect.height()
        button_spacing = max(3, zone_height * 0.1)  # 10% of zone height, min 3px
        min_button_spacing = max(2, zone_height * 0.05)  # 5% of zone height, min 2px

        # Get actual button sizes from the widgets
        up_button = up_proxy.widget()
        down_button = down_proxy.widget()

        if not up_button or not down_button:
            return

        # Calculate positions to avoid overlap
        zone_width = zone_rect.width()
        button_height = up_button.height()
        button_width = up_button.width()

        # Ensure buttons are adaptive to zone size and edit mode
        if (zone_height < button_height or zone_width < button_width or
            not self.edit_mode_enabled):
            # Zone too small for buttons or not in edit mode - hide them
            up_proxy.setVisible(False)
            down_proxy.setVisible(False)
            return

        # Make buttons visible only if in edit mode and zone is large enough
        up_proxy.setVisible(True)
        down_proxy.setVisible(True)

        # Calculate safe positions with collision detection
        total_button_space = 2 * button_height + min_button_spacing

        if zone_height >= total_button_space + 2 * button_spacing:
            # Enough space for both buttons with full spacing
            up_y = zone_rect.top() + button_spacing
            down_y = zone_rect.bottom() - button_height - button_spacing
        elif zone_height >= total_button_space:
            # Enough space for both buttons with minimal spacing
            up_y = zone_rect.top() + min_button_spacing
            down_y = zone_rect.bottom() - button_height - min_button_spacing
        else:
            # Very limited space - position buttons at edges with minimal spacing
            center_y = zone_rect.center().y()
            half_spacing = min_button_spacing / 2
            up_y = center_y - button_height - half_spacing
            down_y = center_y + half_spacing

            # Ensure buttons don't go outside zone bounds
            up_y = max(up_y, zone_rect.top())
            down_y = min(down_y, zone_rect.bottom() - button_height)

        # Center horizontally in the zone, but ensure buttons stay within zone bounds
        zone_center_x = zone_rect.center().x()
        button_x = zone_center_x - (button_width / 2)

        # Ensure buttons don't extend outside zone horizontally
        button_x = max(button_x, zone_rect.left())
        button_x = min(button_x, zone_rect.right() - button_width)

        up_proxy.setPos(button_x, up_y)
        down_proxy.setPos(button_x, down_y)

    def mousePressEvent(self, event):
        """Handle mouse press events for zone editing (only when in edit mode)."""
        if event.button() == QtCore.Qt.MouseButton.LeftButton and self.edit_mode_enabled:
            # Check if click is near top or bottom edge for y-axis editing
            click_y = event.pos().y()
            zone_rect = self.rect()

            edge_threshold = 12  # Larger threshold for more reliable clicking

            # In PyQtGraph coordinates:
            # - zone_rect.bottom() = lower Y value (corresponds to zone_bounds['min_y'])
            # - zone_rect.top() = higher Y value (corresponds to zone_bounds['max_y'])
            rect_bottom = zone_rect.bottom()  # Lower Y value (min_y)
            rect_top = zone_rect.top()        # Higher Y value (max_y)

            # Debug logging with clearer coordinate information
            logger.debug(f"Zone {self.zone_id}: Click at y={click_y:.1f}")
            logger.debug(f"Zone {self.zone_id}: Rect bottom (min_y)={rect_bottom:.1f}, top (max_y)={rect_top:.1f}")
            logger.debug(f"Zone {self.zone_id}: Zone bounds min_y={self.zone_bounds['min_y']:.1f}, max_y={self.zone_bounds['max_y']:.1f}")

            # Calculate distances to each edge
            bottom_distance = abs(click_y - rect_bottom)  # Distance to lower edge (min_y)
            top_distance = abs(click_y - rect_top)        # Distance to upper edge (max_y)

            logger.debug(f"Zone {self.zone_id}: Distances - bottom_edge={bottom_distance:.1f}, top_edge={top_distance:.1f}")

            # Determine which edge is closer and within threshold
            if top_distance <= edge_threshold and top_distance <= bottom_distance:
                # Clicked near the top edge (higher Y value, max_y)
                self.is_editing = True
                self.drag_edge = 'top'
                self.drag_start_y = click_y
                self.setCursor(QtCore.Qt.CursorShape.SizeVerCursor)
                logger.info(f"Zone {self.zone_id}: Started editing TOP edge (max_y) - distance={top_distance:.1f}")
                event.accept()  # Accept the event to prevent propagation
                return
            elif bottom_distance <= edge_threshold:
                # Clicked near the bottom edge (lower Y value, min_y)
                self.is_editing = True
                self.drag_edge = 'bottom'
                self.drag_start_y = click_y
                self.setCursor(QtCore.Qt.CursorShape.SizeVerCursor)
                logger.info(f"Zone {self.zone_id}: Started editing BOTTOM edge (min_y) - distance={bottom_distance:.1f}")
                event.accept()  # Accept the event to prevent propagation
                return
            else:
                logger.debug(f"Zone {self.zone_id}: Click not near any edge (threshold={edge_threshold})")

        super().mousePressEvent(event)

    def mouseDoubleClickEvent(self, event):
        """Handle double-click events to enter/exit edit mode."""
        if event.button() == QtCore.Qt.MouseButton.LeftButton:
            self.toggle_edit_mode()
            event.accept()
            return

        super().mouseDoubleClickEvent(event)

    def mouseMoveEvent(self, event):
        """Handle mouse move events for zone editing with snapping to peaks, troughs, and intersections."""
        if self.is_editing and self.drag_edge and self.edit_mode_enabled:
            current_y = event.pos().y()

            # Get all available snap points
            snap_points = self.get_all_snap_points()

            # Store original bounds for debugging
            original_min_y = self.zone_bounds['min_y']
            original_max_y = self.zone_bounds['max_y']

            # Update zone bounds based on drag with snapping
            min_zone_height = 0.1
            snap_distance = 5.0  # Maximum distance to snap to a point

            if self.drag_edge == 'top':
                # Find nearest snap point for top edge
                snapped_y = self.find_nearest_snap_point(current_y, snap_points, snap_distance)

                # Top edge can move down but not past bottom - min_size
                max_allowed_top = self.zone_bounds['max_y'] - min_zone_height

                if snapped_y <= max_allowed_top:
                    self.zone_bounds['min_y'] = snapped_y
                    if abs(snapped_y - current_y) <= snap_distance:
                        logger.debug(f"Zone {self.zone_id}: TOP edge SNAPPED from {original_min_y:.3f} to {snapped_y:.3f}")
                    else:
                        logger.debug(f"Zone {self.zone_id}: TOP edge moved from {original_min_y:.3f} to {snapped_y:.3f}")
                else:
                    self.zone_bounds['min_y'] = max_allowed_top
                    logger.debug(f"Zone {self.zone_id}: TOP edge constrained to {max_allowed_top:.3f}")

            elif self.drag_edge == 'bottom':
                # Find nearest snap point for bottom edge
                snapped_y = self.find_nearest_snap_point(current_y, snap_points, snap_distance)

                # Bottom edge can move up but not past top + min_size
                min_allowed_bottom = self.zone_bounds['min_y'] + min_zone_height

                if snapped_y >= min_allowed_bottom:
                    self.zone_bounds['max_y'] = snapped_y
                    if abs(snapped_y - current_y) <= snap_distance:
                        logger.debug(f"Zone {self.zone_id}: BOTTOM edge SNAPPED from {original_max_y:.3f} to {snapped_y:.3f}")
                    else:
                        logger.debug(f"Zone {self.zone_id}: BOTTOM edge moved from {original_max_y:.3f} to {snapped_y:.3f}")
                else:
                    self.zone_bounds['max_y'] = min_allowed_bottom
                    logger.debug(f"Zone {self.zone_id}: BOTTOM edge constrained to {min_allowed_bottom:.3f}")

            # Update the visual rectangle
            self.update_zone_rectangle()

            # Accept the event to prevent propagation
            event.accept()
            return

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """Handle mouse release events."""
        if self.is_editing:
            logger.info(f"Zone {self.zone_id}: Finished editing {self.drag_edge} edge")

            # Clean up dragging state
            self.is_editing = False
            self.drag_edge = None
            self.drag_start_y = None

            # Set cursor based on edit mode
            if self.edit_mode_enabled:
                self.setCursor(QtCore.Qt.CursorShape.ArrowCursor)
            else:
                self.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)

            # Reposition navigation buttons after resize
            if len(self.arrow_buttons) >= 2:
                self.position_navigation_buttons(self.arrow_buttons[0], self.arrow_buttons[1])

            # Force a final visual update
            self.update_zone_rectangle()

            # Accept the event to prevent propagation
            event.accept()
            return

        super().mouseReleaseEvent(event)

    def hoverMoveEvent(self, event):
        """Handle hover events to show resize cursor (only when in edit mode)."""
        if not self.is_editing:
            if self.edit_mode_enabled:
                # Show resize cursor when in edit mode
                hover_y = event.pos().y()
                zone_rect = self.rect()
                edge_threshold = 12  # Match the click threshold

                # Use consistent coordinate system with mousePressEvent
                rect_bottom = zone_rect.bottom()  # Lower Y value (min_y)
                rect_top = zone_rect.top()        # Higher Y value (max_y)

                bottom_distance = abs(hover_y - rect_bottom)  # Distance to lower edge (min_y)
                top_distance = abs(hover_y - rect_top)        # Distance to upper edge (max_y)

                if (top_distance <= edge_threshold or bottom_distance <= edge_threshold):
                    self.setCursor(QtCore.Qt.CursorShape.SizeVerCursor)
                else:
                    self.setCursor(QtCore.Qt.CursorShape.ArrowCursor)
            else:
                # Show pointer cursor when not in edit mode to indicate double-click availability
                self.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)

        super().hoverMoveEvent(event)

    def toggle_edit_mode(self):
        """Toggle edit mode for the zone."""
        self.edit_mode_enabled = not self.edit_mode_enabled

        if self.edit_mode_enabled:
            logger.info(f"Zone {self.zone_id}: Entered EDIT MODE (double-clicked)")
            # Update styling to show edit mode
            self.update_edit_mode_styling()
            # Show navigation buttons when entering edit mode
            for button in self.arrow_buttons:
                button.setVisible(True)
        else:
            logger.info(f"Zone {self.zone_id}: Exited EDIT MODE")
            # Reset to normal styling
            self.setup_zone_styling()
            # Hide navigation buttons when exiting edit mode
            for button in self.arrow_buttons:
                button.setVisible(False)
            # Reset cursor
            self.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)
            # Stop any ongoing editing
            if self.is_editing:
                self.is_editing = False
                self.drag_edge = None
                self.drag_start_y = None

    def update_edit_mode_styling(self):
        """Update zone styling to indicate edit mode."""
        if self.zone_color == 'orange':
            # Brighter orange border and more opaque fill for edit mode
            zone_pen = pg.mkPen(color='orange', width=3)
            zone_brush = pg.mkBrush(color=(255, 165, 0, 60))  # More opaque
        elif self.zone_color == 'red':
            # Brighter red border and more opaque fill for edit mode
            zone_pen = pg.mkPen(color='red', width=3)
            zone_brush = pg.mkBrush(color=(255, 0, 0, 60))  # More opaque
        elif self.zone_color == 'cyan':
            # Brighter cyan border and more opaque fill for edit mode
            zone_pen = pg.mkPen(color='cyan', width=3)
            zone_brush = pg.mkBrush(color=(0, 255, 255, 60))  # More opaque
        elif self.zone_color == '#4caf50':  # Green for support zones
            zone_pen = pg.mkPen(color='#4caf50', width=3)
            zone_brush = pg.mkBrush(color=(76, 175, 80, 60))  # More opaque green
        elif self.zone_color == '#f44336':  # Red for resistance zones
            zone_pen = pg.mkPen(color='#f44336', width=3)
            zone_brush = pg.mkBrush(color=(244, 67, 54, 60))  # More opaque red
        elif self.zone_color.startswith('#'):  # Handle other hex colors
            zone_pen = pg.mkPen(color=self.zone_color, width=3)
            # Convert hex to RGB with more opacity for edit mode
            hex_color = self.zone_color.lstrip('#')
            if len(hex_color) == 6:
                r = int(hex_color[0:2], 16)
                g = int(hex_color[2:4], 16)
                b = int(hex_color[4:6], 16)
                zone_brush = pg.mkBrush(color=(r, g, b, 60))  # More opaque
            else:
                zone_brush = pg.mkBrush(color=(128, 128, 128, 60))  # Fallback to gray
        else:
            # Brighter gray border and more opaque fill for edit mode
            zone_pen = pg.mkPen(color='gray', width=3)
            zone_brush = pg.mkBrush(color=(128, 128, 128, 60))  # More opaque

        self.setPen(zone_pen)
        self.setBrush(zone_brush)

    def get_all_snap_points(self):
        """
        Get all available snap points (peaks, troughs, intersections) for zone edge snapping.

        Returns:
            list: List of y-coordinates that edges can snap to
        """
        try:
            snap_points = []

            # Get all peaks and troughs from the density graph tab
            if hasattr(self.density_graph_tab, 'all_valid_peaks') and self.density_graph_tab.all_valid_peaks:
                for _, peak_y in self.density_graph_tab.all_valid_peaks:
                    snap_points.append(peak_y)

            if hasattr(self.density_graph_tab, 'all_valid_troughs') and self.density_graph_tab.all_valid_troughs:
                for _, trough_y in self.density_graph_tab.all_valid_troughs:
                    snap_points.append(trough_y)

            # Get all intersection points (orange and red)
            if hasattr(self.density_graph_tab, 'valid_intersections') and self.density_graph_tab.valid_intersections:
                for _, intersection_y, _, _ in self.density_graph_tab.valid_intersections:
                    snap_points.append(intersection_y)

            if hasattr(self.density_graph_tab, 'red_intersections') and self.density_graph_tab.red_intersections:
                for _, intersection_y, _, _ in self.density_graph_tab.red_intersections:
                    snap_points.append(intersection_y)

            # Get points from clusters (both intersection and peak/trough clusters)
            cluster_sources = [
                'orange_intersect_clusters', 'orange_intersect_non_clusters',
                'red_intersect_clusters', 'red_intersect_non_clusters',
                'peak_trough_clusters', 'peak_trough_non_clusters'
            ]

            for cluster_source in cluster_sources:
                if hasattr(self.density_graph_tab, cluster_source):
                    clusters = getattr(self.density_graph_tab, cluster_source)
                    if clusters:
                        for cluster in clusters:
                            if isinstance(cluster, dict):
                                # Handle cluster dictionaries
                                if 'points' in cluster:
                                    for point in cluster['points']:
                                        if isinstance(point, (list, tuple)) and len(point) >= 2:
                                            snap_points.append(point[1])  # y-coordinate
                                        elif isinstance(point, dict) and 'y' in point:
                                            snap_points.append(point['y'])
                                elif 'y' in cluster:
                                    snap_points.append(cluster['y'])
                            elif isinstance(cluster, (list, tuple)) and len(cluster) >= 2:
                                snap_points.append(cluster[1])  # y-coordinate

            # Remove duplicates and sort
            snap_points = sorted(list(set(snap_points)))

            logger.debug(f"Zone {self.zone_id}: Found {len(snap_points)} snap points")
            return snap_points

        except Exception as e:
            logger.error(f"Error getting snap points: {str(e)}")
            return []

    def find_nearest_snap_point(self, target_y, snap_points, max_distance=None):
        """
        Find the nearest snap point to a target y-coordinate.

        Args:
            target_y: Target y-coordinate
            snap_points: List of available snap points
            max_distance: Maximum distance to consider for snapping (None = no limit)

        Returns:
            float: Nearest snap point y-coordinate, or target_y if no suitable snap point found
        """
        try:
            if not snap_points:
                return target_y

            # Find the closest snap point
            distances = [abs(target_y - snap_y) for snap_y in snap_points]
            min_distance = min(distances)
            closest_index = distances.index(min_distance)
            closest_snap_point = snap_points[closest_index]

            # Check if within max distance (if specified)
            if max_distance is not None and min_distance > max_distance:
                return target_y

            logger.debug(f"Zone {self.zone_id}: Snapped from {target_y:.3f} to {closest_snap_point:.3f} (distance: {min_distance:.3f})")
            return closest_snap_point

        except Exception as e:
            logger.error(f"Error finding nearest snap point: {str(e)}")
            return target_y

    def update_zone_rectangle(self):
        """Update the visual rectangle based on current zone bounds."""
        left = self.zone_bounds['min_x']
        bottom = self.zone_bounds['min_y']
        width = self.zone_bounds['max_x'] - self.zone_bounds['min_x']
        height = self.zone_bounds['max_y'] - self.zone_bounds['min_y']

        # Update the rectangle geometry without triggering re-addition to plot
        self.setRect(left, bottom, width, height)

        # Force a visual update
        if self.scene():
            self.scene().update()

    def navigate_to_nearest_confluence(self, direction):
        """Navigate zone boundary to nearest confluence point."""
        if not self.density_graph_tab:
            return

        confluence_points = self.density_graph_tab.get_confluence_points()
        if not confluence_points:
            return

        current_y = (self.zone_bounds['max_y'] if direction == 'up'
                    else self.zone_bounds['min_y'])

        # Find nearest confluence point in the specified direction
        nearest_confluence = None
        min_distance = float('inf')

        for point in confluence_points:
            point_y = point['y']

            if direction == 'up' and point_y > current_y:
                distance = point_y - current_y
                if distance < min_distance:
                    min_distance = distance
                    nearest_confluence = point
            elif direction == 'down' and point_y < current_y:
                distance = current_y - point_y
                if distance < min_distance:
                    min_distance = distance
                    nearest_confluence = point

        # Move zone boundary to nearest confluence
        if nearest_confluence:
            if direction == 'up':
                self.zone_bounds['max_y'] = nearest_confluence['y']
            else:
                self.zone_bounds['min_y'] = nearest_confluence['y']

            self.update_zone_rectangle()

            # Reposition navigation buttons
            if len(self.arrow_buttons) >= 2:
                self.position_navigation_buttons(self.arrow_buttons[0], self.arrow_buttons[1])

            logger.info(f"Moved zone {self.zone_id} {direction} boundary to confluence at y={nearest_confluence['y']:.3f}")

    def reset_to_original_bounds(self):
        """Reset the zone to its original bounds."""
        try:
            # Restore original bounds
            self.zone_bounds = self.original_zone_bounds.copy()

            # Update the visual rectangle
            self.update_zone_rectangle()

            # Reposition navigation buttons if they exist
            if len(self.arrow_buttons) >= 2:
                self.position_navigation_buttons(self.arrow_buttons[0], self.arrow_buttons[1])

            logger.info(f"Reset zone {self.zone_id} to original bounds: "
                       f"({self.zone_bounds['min_y']:.2f} to {self.zone_bounds['max_y']:.2f})")

        except Exception as e:
            logger.error(f"Error resetting zone {self.zone_id} to original bounds: {str(e)}")

    def has_been_edited(self):
        """Check if the zone has been edited from its original bounds."""
        try:
            return (abs(self.zone_bounds['min_y'] - self.original_zone_bounds['min_y']) > 0.001 or
                    abs(self.zone_bounds['max_y'] - self.original_zone_bounds['max_y']) > 0.001)
        except Exception as e:
            logger.error(f"Error checking if zone {self.zone_id} has been edited: {str(e)}")
            return False


class DensityGraphTab(QtWidgets.QWidget):
    """
    Tab for visualizing price density distributions.

    IV Zone Pricing Logic:
    - IV Peak lines are ALWAYS derived from BID prices and BID-based breakthrough points
    - Max Fear, IV Walls, IV Inner Walls, and IV Overflow are ALWAYS derived from ASK prices and ASK-based breakthrough points
    - Each price type (bid/ask) uses its own breakthrough reference to determine which zones to draw
    - Ask-based zones only appear where they don't touch the ask-based breakthrough point
    - Bid-based zones only appear where they don't touch the bid-based breakthrough point
    - The price type toggle (Use Bid/Ask Prices) only affects DISPLAYED curves, NOT the IV zone calculations
    - IV zones maintain their fixed pricing logic regardless of the display toggle setting
    """
    def __init__(self, parent=None, data_tab=None):
        """
        Initialize the density graph tab.

        Args:
            parent: Parent widget
            data_tab: Reference to the Data tab
        """
        super().__init__(parent)
        self.parent = parent
        self.data_tab = data_tab

        # Initialize SABR model for options pricing
        self.sabr_model = SABRModel()

        # Initialize synthetic options engine
        self.synthetic_engine = None
        if SYNTHETIC_OPTIONS_AVAILABLE:
            try:
                self.synthetic_engine = SyntheticOptionsEngine(
                    spot_price=100.0,  # Default, will be updated from data
                    risk_free_rate=0.05,
                    dividend_yield=0.0
                )
                logger.info("Synthetic options engine initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize synthetic options engine: {e}")
                self.synthetic_engine = None

        self.chart_colors = {
            'background': THEME_COLORS['control_panel'],  # Use theme control panel color
            'bullish': THEME_COLORS['bullish'],           # Material Design Green
            'bearish': THEME_COLORS['bearish'],           # Material Design Red
            'text': THEME_COLORS['text'],                 # Light gray text
            'line': '#FFFFFF',                            # White lines
            'histogram': '#3A539B',                       # Blue for histogram
            'density': '#FFC107',                         # Amber for density curve
            'high': '#FFFFFF',                            # White for high prices
            'low': '#FFFFFF',                             # White for low prices
            'long': '#00FF00',                            # Green for long theoretical prices
            'short': '#FF00FF'                            # Magenta for short theoretical prices
        }

        # Initialize theoretical prices as empty list for data subtab
        self.theoretical_prices = []

        # DBSCAN clustering parameters (configurable via buttons)
        self.peaks_troughs_eps = 0.5  # Default for SPY/QQQ
        self.peaks_troughs_min_samples = 2
        self.intersection_eps_primary = 0.04  # For normalized intersection clustering
        self.intersection_eps_fallback = 0.06  # Fallback for normalized intersection clustering
        self.intersection_min_samples_primary = 3
        self.intersection_min_samples_fallback = 2

        # Track UI state
        self.density_zones_active = False  # Track if Density Zones is active
        self.peak_trough_clusters = []  # Store peak/trough cluster data for priority ranking
        self.peak_trough_non_clusters = []  # Store non-clustered peaks/troughs for priority ranking
        self.orange_intersect_clusters = []  # Store orange intersection cluster data
        self.orange_intersect_non_clusters = []  # Store non-clustered orange intersections
        self.red_intersect_clusters = []  # Store red intersection cluster data
        self.red_intersect_non_clusters = []  # Store non-clustered red intersections

        # Density Zone storage and management
        self.density_zones = []  # List of editable density zone objects
        self.zone_id_counter = 0  # Counter for unique zone IDs
        self.confluence_points = []  # List of all confluence points for navigation

        # Initialize UI
        self.init_ui()

        # Connect to data tab's calculation mode change signal if available
        if self.data_tab is not None:
            # Check if the data tab has the necessary signals
            if hasattr(self.data_tab, 'percentage_based_btn'):
                self.data_tab.percentage_based_btn.toggled.connect(self.on_calculation_mode_changed)
                self.data_tab.current_price_btn.toggled.connect(self.on_calculation_mode_changed)
                self.data_tab.pivot_price_btn.toggled.connect(self.on_calculation_mode_changed)
                if hasattr(self.data_tab, 'cycle_pivot_btn'):
                    self.data_tab.cycle_pivot_btn.toggled.connect(self.on_calculation_mode_changed)
                logger.info("Connected to data tab calculation mode change signals")

    def init_ui(self):
        """Initialize the user interface."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Status label
        self.status_label = QtWidgets.QLabel("Density Graph - Ready")
        self.status_label.setStyleSheet(f"color: {THEME_COLORS['text']};")
        main_layout.addWidget(self.status_label)

        # Create plot widget
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setBackground(self.chart_colors['background'])
        # Empty axis labels to remove confusion
        self.plot_widget.setLabel('left', '', color=self.chart_colors['text'])
        self.plot_widget.setLabel('bottom', '', color=self.chart_colors['text'])

        # Set initial axis ranges
        self.plot_widget.setXRange(-1.5, 6)  # X-axis range from -1.5 to 6
        self.plot_widget.setYRange(-10, 10)  # Initial y-axis range, will be updated later

        # Store the initial view state for reset functionality
        self.initial_view_state = None

        # Enable mouse interaction for zooming
        self.plot_widget.setMouseEnabled(x=True, y=True)
        self.plot_widget.setMenuEnabled(False)  # Still disable context menu

        # Enable mouse wheel zooming but disable rectangular region selection
        view_box = self.plot_widget.getPlotItem().getViewBox()
        view_box.enableAutoRange(enable=False)  # Disable auto-range to maintain manual zoom control
        view_box.setMouseMode(pg.ViewBox.PanMode)  # Use pan mode instead of rect mode to disable left-click zoom

        # Install custom event filter for double-click zone creation (non-interfering)
        self.plot_widget.installEventFilter(self)

        # Add control buttons (zoom and density toggle)
        controls_layout = QtWidgets.QHBoxLayout()

        # Zoom in button
        self.zoom_in_btn = QtWidgets.QPushButton("Zoom In")
        self.zoom_in_btn.setStyleSheet(f"""
            background-color: {THEME_COLORS['control_panel']};
            color: {THEME_COLORS['text']};
            border: 1px solid {THEME_COLORS['borders']};
            border-radius: {THEME_COLORS['button_radius']};
            padding: 5px;
            font-size: 10px;
            font-weight: bold;
        """)
        self.zoom_in_btn.clicked.connect(self.zoom_in)
        controls_layout.addWidget(self.zoom_in_btn)

        # Zoom out button
        self.zoom_out_btn = QtWidgets.QPushButton("Zoom Out")
        self.zoom_out_btn.setStyleSheet(f"""
            background-color: {THEME_COLORS['control_panel']};
            color: {THEME_COLORS['text']};
            border: 1px solid {THEME_COLORS['borders']};
            border-radius: {THEME_COLORS['button_radius']};
            padding: 5px;
            font-size: 10px;
            font-weight: bold;
        """)
        self.zoom_out_btn.clicked.connect(self.zoom_out)
        controls_layout.addWidget(self.zoom_out_btn)

        # Reset zoom button
        self.reset_zoom_btn = QtWidgets.QPushButton("Reset Zoom")
        self.reset_zoom_btn.setStyleSheet(f"""
            background-color: {THEME_COLORS['control_panel']};
            color: {THEME_COLORS['text']};
            border: 1px solid {THEME_COLORS['borders']};
            border-radius: {THEME_COLORS['button_radius']};
            padding: 5px;
            font-size: 10px;
            font-weight: bold;
        """)
        self.reset_zoom_btn.clicked.connect(self.reset_zoom)
        controls_layout.addWidget(self.reset_zoom_btn)

        # Add a smaller spacer between zoom and density controls
        controls_layout.addSpacing(10)

        # Create custom style for checkbox controls
        checkbox_style = f"""
            QCheckBox {{
                color: {THEME_COLORS['text']};
                spacing: 8px;
                padding: 2px;
                font-size: 10px;
                font-weight: bold;
                background: transparent;
                border: none;
            }}
            QCheckBox:hover {{
                background: transparent;
            }}
            QCheckBox:checked {{
                background: transparent;
                color: {THEME_COLORS['text']};
            }}
            QCheckBox::indicator {{
                width: 14px;
                height: 14px;
                border-radius: 3px;
            }}
            QCheckBox::indicator:hover {{
                border: 2px solid white;
            }}
            QCheckBox::indicator:checked {{
                background-color: white;
                border: 2px solid black;
            }}
            QCheckBox::indicator:unchecked {{
                background-color: {THEME_COLORS['control_panel']};
                border: 2px solid black;
            }}
        """

        # Create vertical layout for density buttons (stacked like bid/ask buttons)
        density_layout = QtWidgets.QVBoxLayout()
        density_layout.setSpacing(2)  # Small spacing between stacked buttons

        # Toggle density gradient button (top)
        self.show_density_btn = QtWidgets.QCheckBox("Show Density")
        self.show_density_btn.setChecked(True)  # Enabled by default
        self.show_density_btn.setStyleSheet(checkbox_style)
        self.show_density_btn.setMinimumWidth(100)  # Reduced minimum width
        self.show_density_btn.toggled.connect(self.on_density_toggle)
        density_layout.addWidget(self.show_density_btn)

        # Toggle volume profile button (bottom)
        self.show_volume_profile_btn = QtWidgets.QCheckBox("Show Density Profile")
        self.show_volume_profile_btn.setChecked(False)  # Disabled by default
        self.show_volume_profile_btn.setStyleSheet(checkbox_style)
        self.show_volume_profile_btn.setMinimumWidth(130)  # Reduced minimum width
        self.show_volume_profile_btn.toggled.connect(self.on_volume_profile_toggle)
        density_layout.addWidget(self.show_volume_profile_btn)

        # Add the vertical density layout to the main horizontal controls layout
        controls_layout.addLayout(density_layout)

        # Add a smaller spacer between density buttons and option data
        controls_layout.addSpacing(10)

        # Toggle option data (strike, put IV, call IV) button
        self.show_option_data_btn = QtWidgets.QCheckBox("Show Option Data")
        self.show_option_data_btn.setChecked(True)  # Enabled by default
        self.show_option_data_btn.setStyleSheet(checkbox_style)
        self.show_option_data_btn.setMinimumWidth(110)  # Reduced minimum width
        self.show_option_data_btn.toggled.connect(self.on_option_data_toggle)
        controls_layout.addWidget(self.show_option_data_btn)



        # Add a smaller spacer between toggles
        controls_layout.addSpacing(10)

        # Toggle Option Zones button (IV Peak, IV Wall, IV Inner Wall, Max Fear, IV Overflow)
        self.show_option_zones_btn = QtWidgets.QCheckBox("Option Zones")
        self.show_option_zones_btn.setChecked(True)  # Enabled by default
        self.show_option_zones_btn.setStyleSheet(checkbox_style)
        self.show_option_zones_btn.setMinimumWidth(100)  # Reduced minimum width
        self.show_option_zones_btn.toggled.connect(self.on_option_zones_toggle)
        controls_layout.addWidget(self.show_option_zones_btn)

        # Add a smaller spacer between toggles
        controls_layout.addSpacing(10)

        # Quick zone creation buttons (only visible when Density Zones is active)
        quick_zones_layout = QtWidgets.QHBoxLayout()

        # Quick support zone button
        self.quick_support_btn = QtWidgets.QPushButton("Quick Support")
        self.quick_support_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: {THEME_COLORS['button_radius']};
                padding: 4px 8px;
                font-weight: bold;
                font-size: 10px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['primary_accent']};
                color: white;
                border: 1px solid {THEME_COLORS['secondary_accent']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
                color: white;
            }}
        """)
        self.quick_support_btn.setVisible(False)
        self.quick_support_btn.clicked.connect(self.create_quick_support_zone)
        quick_zones_layout.addWidget(self.quick_support_btn)

        # Quick resistance zone button
        self.quick_resistance_btn = QtWidgets.QPushButton("Quick Resistance")
        self.quick_resistance_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: {THEME_COLORS['button_radius']};
                padding: 4px 8px;
                font-weight: bold;
                font-size: 10px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['primary_accent']};
                color: white;
                border: 1px solid {THEME_COLORS['secondary_accent']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
                color: white;
            }}
        """)
        self.quick_resistance_btn.setVisible(False)
        self.quick_resistance_btn.clicked.connect(self.create_quick_resistance_zone)
        quick_zones_layout.addWidget(self.quick_resistance_btn)

        # Clear zones button
        self.clear_zones_btn = QtWidgets.QPushButton("Clear Zones")
        self.clear_zones_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: {THEME_COLORS['button_radius']};
                padding: 4px 8px;
                font-weight: bold;
                font-size: 10px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['highlight']};
                color: {THEME_COLORS['background']};
                border: 1px solid {THEME_COLORS['highlight']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
                color: white;
            }}
        """)
        self.clear_zones_btn.setVisible(False)
        self.clear_zones_btn.clicked.connect(self.clear_manual_zones)
        quick_zones_layout.addWidget(self.clear_zones_btn)

        # Reset edits button
        self.reset_edits_btn = QtWidgets.QPushButton("Reset Edits")
        self.reset_edits_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: {THEME_COLORS['button_radius']};
                padding: 4px 8px;
                font-weight: bold;
                font-size: 10px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['secondary_accent']};
                color: white;
                border: 1px solid {THEME_COLORS['secondary_accent']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['pressed_accent']};
                color: white;
            }}
        """)
        self.reset_edits_btn.setVisible(False)
        self.reset_edits_btn.clicked.connect(self.reset_zone_edits)
        quick_zones_layout.addWidget(self.reset_edits_btn)

        controls_layout.addLayout(quick_zones_layout)

        # Add a smaller spacer between manual zone button and expiry
        controls_layout.addSpacing(10)

        # Expiration date selector
        expiry_label = QtWidgets.QLabel("Expiry:")
        expiry_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-weight: bold; font-size: 12px;")
        controls_layout.addWidget(expiry_label)

        self.expiry_selector = QtWidgets.QComboBox()
        self.expiry_selector.setMinimumWidth(90)  # Reduced minimum width
        self.expiry_selector.setStyleSheet(f"""
            QComboBox {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 11px;
            }}
            QComboBox:hover {{
                border: 1px solid {THEME_COLORS['primary_accent']};
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid {THEME_COLORS['borders']};
            }}
            QComboBox QAbstractItemView {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                selection-background-color: {THEME_COLORS['primary_accent']};
            }}
        """)
        self.expiry_selector.currentIndexChanged.connect(self.on_expiry_changed)
        controls_layout.addWidget(self.expiry_selector)

        # Add a smaller spacer between expiry and price type controls
        controls_layout.addSpacing(10)

        # Price type selection radio buttons (stacked vertically)
        # Create a vertical layout for the radio buttons
        price_type_layout = QtWidgets.QVBoxLayout()
        price_type_layout.setSpacing(2)  # Small spacing between stacked buttons
        price_type_layout.setContentsMargins(0, 0, 0, 0)  # No margins

        # Create a button group to ensure only one can be selected
        self.price_type_group = QtWidgets.QButtonGroup()

        # Create custom style for price type radio buttons
        price_type_radio_style = f"""
            QRadioButton {{
                color: {THEME_COLORS['text']};
                spacing: 8px;
                padding: 2px;
                font-size: 10px;
                font-weight: bold;
                background: transparent;
                border: none;
            }}
            QRadioButton:hover {{
                background: transparent;
            }}
            QRadioButton:checked {{
                background: transparent;
                color: {THEME_COLORS['text']};
            }}
            QRadioButton::indicator {{
                width: 14px;
                height: 14px;
                border-radius: 7px;
            }}
            QRadioButton::indicator:hover {{
                border: 2px solid white;
            }}
            QRadioButton::indicator:checked {{
                background-color: white;
                border: 2px solid black;
            }}
            QRadioButton::indicator:unchecked {{
                background-color: {THEME_COLORS['control_panel']};
                border: 2px solid black;
            }}
        """

        # Use Bid Prices radio button (default, on top)
        self.use_bid_prices_btn = QtWidgets.QRadioButton("Use Bid Prices")
        self.use_bid_prices_btn.setChecked(True)  # Default to bid prices
        self.use_bid_prices_btn.setStyleSheet(price_type_radio_style)
        self.use_bid_prices_btn.toggled.connect(self.on_price_type_toggle)
        self.price_type_group.addButton(self.use_bid_prices_btn)
        price_type_layout.addWidget(self.use_bid_prices_btn)

        # Use Ask Prices radio button (below bid prices)
        self.use_ask_prices_btn = QtWidgets.QRadioButton("Use Ask Prices")
        self.use_ask_prices_btn.setChecked(False)  # Not default
        self.use_ask_prices_btn.setStyleSheet(price_type_radio_style)
        self.use_ask_prices_btn.toggled.connect(self.on_price_type_toggle)
        self.price_type_group.addButton(self.use_ask_prices_btn)
        price_type_layout.addWidget(self.use_ask_prices_btn)

        # Add the vertical layout to the main horizontal controls layout
        controls_layout.addLayout(price_type_layout)

        # Add a small spacer between price type and option pricing controls
        controls_layout.addSpacing(10)

        # Option pricing selection radio buttons (stacked vertically)
        # Create a vertical layout for the option pricing radio buttons
        option_pricing_layout = QtWidgets.QVBoxLayout()
        option_pricing_layout.setSpacing(2)  # Small spacing between stacked buttons
        option_pricing_layout.setContentsMargins(0, 0, 0, 0)  # No margins

        # Create a button group to ensure only one can be selected
        self.option_pricing_group = QtWidgets.QButtonGroup()

        # Outright Option Pricing radio button (default, on top)
        self.real_option_pricing_btn = QtWidgets.QRadioButton("Outright Option Pricing")
        self.real_option_pricing_btn.setChecked(True)  # Default to outright option pricing
        self.real_option_pricing_btn.setStyleSheet(price_type_radio_style)
        self.real_option_pricing_btn.toggled.connect(self.on_option_pricing_toggle)
        self.option_pricing_group.addButton(self.real_option_pricing_btn)
        option_pricing_layout.addWidget(self.real_option_pricing_btn)

        # Synthetic Option Pricing radio button (below real option pricing)
        self.synthetic_option_pricing_btn = QtWidgets.QRadioButton("Synthetic Option Pricing")
        self.synthetic_option_pricing_btn.setChecked(False)  # Not default
        self.synthetic_option_pricing_btn.setStyleSheet(price_type_radio_style)
        self.synthetic_option_pricing_btn.toggled.connect(self.on_option_pricing_toggle)
        self.option_pricing_group.addButton(self.synthetic_option_pricing_btn)
        option_pricing_layout.addWidget(self.synthetic_option_pricing_btn)

        # Add the vertical option pricing layout to the main horizontal controls layout
        controls_layout.addLayout(option_pricing_layout)

        # Add spacer to push buttons to the left
        controls_layout.addStretch()

        # Add controls to main layout
        main_layout.addLayout(controls_layout)

        # Add plot widget to main layout
        main_layout.addWidget(self.plot_widget)

        # Initialize crosshair (will be properly set up in generate_density_graph)
        self.crosshair = None

        # Initialize visualization states (enabled by default)
        self.show_density = True
        self.show_option_data = True
        self.show_volume_profile = False
        self.show_option_zones = True  # Option Zones enabled by default
        self.use_ask_prices = False  # Use bid prices by default
        self.use_synthetic_pricing = False  # Use outright option pricing by default

        # Density zones state (disabled by default)
        self.density_zones_active = False

        # Zone creation functionality (quick zones only)

        # Initialize expiration date selector state
        self.available_expiry_dates = []
        self.selected_expiry_date = None

        # Storage for snap points (peaks, troughs, intersections)
        self.all_valid_peaks = []
        self.all_valid_troughs = []
        self.valid_intersections = []
        self.red_intersections = []



        # Set background color
        self.setStyleSheet(f"background-color: {THEME_COLORS['background']};")

    def get_calculation_mode(self):
        """
        Get the current calculation mode from the data tab.

        Returns:
            str: The current calculation mode ("percentage_based", "current_price", "pivot_price", or "cycle_pivot")
        """
        if self.data_tab is None:
            logger.warning("No data tab reference available")
            return "percentage_based"  # Default

        if hasattr(self.data_tab, 'calculation_mode'):
            logger.info(f"Using calculation mode from data tab: {self.data_tab.calculation_mode}")
            return self.data_tab.calculation_mode

        # Fallback: determine mode from radio buttons
        if hasattr(self.data_tab, 'percentage_based_btn') and self.data_tab.percentage_based_btn.isChecked():
            return "percentage_based"
        elif hasattr(self.data_tab, 'current_price_btn') and self.data_tab.current_price_btn.isChecked():
            return "current_price"
        elif hasattr(self.data_tab, 'pivot_price_btn') and self.data_tab.pivot_price_btn.isChecked():
            return "pivot_price"
        elif hasattr(self.data_tab, 'cycle_pivot_btn') and self.data_tab.cycle_pivot_btn.isChecked():
            return "cycle_pivot"

        logger.warning("Could not determine calculation mode, using default")
        return "percentage_based"  # Default

    def get_reference_price(self):
        """
        Get the appropriate reference price based on the calculation mode.

        Returns:
            tuple: (price, label, color) where:
                - price is the reference price value
                - label is the description of the price
                - color is the color to use for the infinity line
        """
        try:
            # Get the calculation mode
            calculation_mode = self.get_calculation_mode()

            # If percentage based, use 0 as the reference
            if calculation_mode == "percentage_based":
                logger.info("Using 0 as reference price for percentage based mode")
                return (0, "Percentage Base (0%)", "white")

            # Try to get the market odds tab from the parent
            market_odds_tab = None

            # First check if data_tab has a reference to market_odds_tab
            if self.data_tab is not None and hasattr(self.data_tab, 'market_odds_tab'):
                market_odds_tab = self.data_tab.market_odds_tab
                logger.info("Retrieved market odds tab from data tab")

            # If not found, try to get it from the parent hierarchy
            if market_odds_tab is None and hasattr(self, 'parent') and self.parent is not None:
                parent = self.parent
                while parent is not None:
                    if hasattr(parent, 'market_odds_tab'):
                        market_odds_tab = parent.market_odds_tab
                        logger.info("Retrieved market odds tab from parent hierarchy")
                        break
                    if hasattr(parent, 'parent'):
                        parent = parent.parent()
                    else:
                        break

            # If still not found, try to get it from the data tab's get_market_odds_tab method
            if market_odds_tab is None and self.data_tab is not None and hasattr(self.data_tab, 'get_market_odds_tab'):
                market_odds_tab = self.data_tab.get_market_odds_tab()
                logger.info("Retrieved market odds tab from data tab's get_market_odds_tab method")

            # If we found the market odds tab, get the appropriate price
            if market_odds_tab is not None:
                # For pivot price mode, use the pivot price
                if calculation_mode == "pivot_price":
                    if hasattr(market_odds_tab, 'current_pivot') and market_odds_tab.current_pivot is not None:
                        pivot_price = market_odds_tab.current_pivot
                        logger.info(f"Using pivot price as reference: {pivot_price}")
                        return (pivot_price, f"Extrema Price: {pivot_price:.2f}", "green")

                # For cycle pivot mode, use the latest cycle's pivot price
                elif calculation_mode == "cycle_pivot":
                    if hasattr(market_odds_tab, 'current_pivot') and market_odds_tab.current_pivot is not None:
                        pivot_price = market_odds_tab.current_pivot
                        logger.info(f"Using latest cycle pivot price as reference: {pivot_price}")
                        return (pivot_price, f"Cycle Extrema: {pivot_price:.2f}", "green")

                # For current price mode or default, use the current price
                if hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
                    # Check if we're viewing historical data and should use historical cutoff
                    if hasattr(market_odds_tab, 'historical_cutoff_index'):
                        historical_cutoff_index = market_odds_tab.historical_cutoff_index
                        current_price = market_odds_tab.data['Close'].iloc[historical_cutoff_index]
                        logger.info(f"Using historical reference price (index {historical_cutoff_index}): {current_price}")
                        return (current_price, f"Historical Price: {current_price:.2f}", "orange")
                    else:
                        current_price = market_odds_tab.data['Close'].iloc[-1]
                        logger.info(f"Using current price as reference: {current_price}")
                        return (current_price, f"Current Price: {current_price:.2f}", "yellow")

            logger.warning("Could not determine reference price, using 0")
            return (0, "Reference (0)", "white")  # Default fallback

        except Exception as e:
            logger.error(f"Error getting reference price: {str(e)}", exc_info=True)
            return (0, "Error", "white")  # Default fallback on error

    def get_data_from_data_tab(self):
        """
        Get data from the Data tab.

        Returns:
            pandas.DataFrame: The data from the Data tab, or None if not available
        """
        if self.data_tab is None:
            logger.warning("No data tab reference available")
            return None

        # Check if the data tab has a table model with data
        if hasattr(self.data_tab, 'table_model') and hasattr(self.data_tab.table_model, '_data'):
            data = self.data_tab.table_model._data
            if data is not None and not data.empty:
                logger.info(f"Retrieved data from data tab: {len(data)} rows")
                return data

        logger.warning("No data available from data tab")
        return None

    def get_latest_category(self, data):
        """
        Get the category from the latest row in the data.

        Args:
            data: DataFrame with the data

        Returns:
            str: The category from the latest row, or None if not available
        """
        if data is None or data.empty or 'Category' not in data.columns:
            logger.warning("No category column in data")
            return None

        # Get the category from the latest row
        latest_category = data['Category'].iloc[-1]
        logger.info(f"Latest category: {latest_category}")
        return latest_category

    def get_rows_with_same_weekday(self, data):
        """
        Get all rows with the same weekday as the latest row.

        Args:
            data: DataFrame with the data

        Returns:
            DataFrame: Subset of data with matching weekday, or empty DataFrame if no match
        """
        if data is None or data.empty:
            logger.warning("Invalid data for weekday matching")
            return pd.DataFrame()

        try:
            # Log all columns for debugging
            logger.info(f"Available columns in data: {data.columns.tolist()}")

            # Create a copy of the data to avoid modifying the original
            data_copy = data.copy()

            # Try to extract date information from the data
            # Method 1: Look for date-related columns
            date_column = None
            for col in data.columns:
                if 'date' in col.lower() or 'time' in col.lower() or 'day' in col.lower():
                    date_column = col
                    logger.info(f"Found potential date column: {col}")
                    break

            # Method 2: Check if the index is a DatetimeIndex
            if date_column is None and isinstance(data.index, pd.DatetimeIndex):
                logger.info("Using DatetimeIndex for weekday matching")
                data_copy['_temp_date'] = data.index
                date_column = '_temp_date'

            # Method 3: Try to parse the first column as a date
            if date_column is None:
                logger.info(f"Trying to parse first column as date: {data.columns[0]}")
                try:
                    data_copy['_temp_date'] = pd.to_datetime(data.iloc[:, 0])
                    date_column = '_temp_date'
                    logger.info("Successfully parsed first column as date")
                except:
                    logger.warning("Could not parse first column as date")

            # Method 4: If all else fails, create a date column from the row index
            if date_column is None:
                logger.info("Creating date column from row index")
                # Get the current date
                import datetime
                today = datetime.datetime.now()

                # Create dates going back from today based on row index
                # This assumes the data is in chronological order with the most recent at the end
                dates = []
                for i in range(len(data)):
                    # Go back i days from today
                    date = today - datetime.timedelta(days=i)
                    dates.append(date)

                # Reverse the list so the oldest date is first
                dates.reverse()

                # Add the dates as a new column
                data_copy['_temp_date'] = dates
                date_column = '_temp_date'
                logger.info("Created date column from row index")

            # Convert date column to datetime if it's not already
            if not pd.api.types.is_datetime64_any_dtype(data_copy[date_column]):
                try:
                    data_copy[date_column] = pd.to_datetime(data_copy[date_column])
                    logger.info(f"Converted {date_column} to datetime")
                except Exception as e:
                    logger.error(f"Error converting date column to datetime: {str(e)}")
                    return pd.DataFrame()

            # Get the weekday of the latest row
            latest_date = data_copy[date_column].iloc[-1]
            latest_weekday = latest_date.weekday()  # 0=Monday, 1=Tuesday, ..., 6=Sunday
            weekday_name = latest_date.strftime('%A')  # Full weekday name

            logger.info(f"Latest date: {latest_date}, weekday: {weekday_name} ({latest_weekday})")

            # Filter rows with the same weekday
            matching_rows = data_copy[data_copy[date_column].dt.weekday == latest_weekday]

            if matching_rows.empty:
                logger.warning(f"No rows found with weekday '{weekday_name}'")
            else:
                logger.info(f"Found {len(matching_rows)} rows with weekday '{weekday_name}'")

            # Exclude the latest row to avoid using the latest high/low prices
            if len(matching_rows) > 1:
                # Find the index of the latest row in the matching_rows DataFrame
                latest_row_index = matching_rows.index.max()
                # Drop the latest row
                matching_rows = matching_rows.drop(latest_row_index)
                logger.info(f"Excluded latest row (index {latest_row_index}) from display")

            # Check if we need to limit the number of occurrences
            occurrence_count = 0
            if hasattr(self.parent, 'get_occurrence_count'):
                occurrence_count = self.parent.get_occurrence_count()

            if occurrence_count > 0 and len(matching_rows) > occurrence_count:
                # Sort by date in descending order to get the most recent occurrences
                matching_rows = matching_rows.sort_values(by=date_column, ascending=False)
                # Take only the specified number of occurrences
                matching_rows = matching_rows.head(occurrence_count)
                logger.info(f"Limited to {occurrence_count} most recent occurrences")

            # Drop the temporary date column if we added it
            if date_column == '_temp_date':
                matching_rows = matching_rows.drop('_temp_date', axis=1)

            return matching_rows

        except Exception as e:
            logger.error(f"Error in get_rows_with_same_weekday: {str(e)}", exc_info=True)
            return pd.DataFrame()

    def get_rows_with_same_category(self, data, category):
        """
        Get rows with the same category as the specified category.

        Args:
            data: DataFrame with the data
            category: The category to match

        Returns:
            pandas.DataFrame: Rows with the same category, or an empty DataFrame if none found
        """
        if data is None or data.empty or 'Category' not in data.columns:
            logger.warning("No category column in data")
            return pd.DataFrame()

        # Get rows with the same category
        matching_rows = data[data['Category'] == category]
        logger.info(f"Found {len(matching_rows)} rows with category '{category}'")

        # Exclude the latest row to avoid using the latest high/low prices
        if len(matching_rows) > 1:
            # Find the index of the latest row in the matching_rows DataFrame
            latest_row_index = matching_rows.index.max()
            # Drop the latest row
            matching_rows = matching_rows.drop(latest_row_index)
            logger.info(f"Excluded latest row (index {latest_row_index}) from display")

        # Check if we need to limit the number of occurrences
        occurrence_count = 0
        if hasattr(self.parent, 'get_occurrence_count'):
            occurrence_count = self.parent.get_occurrence_count()

        if occurrence_count > 0 and len(matching_rows) > occurrence_count:
            # Try to find a date column for sorting
            date_column = None
            for col in matching_rows.columns:
                if 'date' in col.lower() or 'time' in col.lower() or 'day' in col.lower():
                    date_column = col
                    break

            if date_column is not None:
                # Convert to datetime if needed
                if not pd.api.types.is_datetime64_any_dtype(matching_rows[date_column]):
                    try:
                        matching_rows[date_column] = pd.to_datetime(matching_rows[date_column])
                    except:
                        date_column = None

            if date_column is not None:
                # Sort by date in descending order to get the most recent occurrences
                matching_rows = matching_rows.sort_values(by=date_column, ascending=False)
            else:
                # If no date column, sort by index in descending order (assuming higher index = more recent)
                matching_rows = matching_rows.sort_index(ascending=False)

            # Take only the specified number of occurrences
            matching_rows = matching_rows.head(occurrence_count)
            logger.info(f"Limited to {occurrence_count} most recent occurrences")

        return matching_rows

    def generate_density_graph(self):
        """Generate density graph with current price line and category dots."""
        try:
            # Clear the plot and reset crosshair
            self.plot_widget.clear()
            self.crosshair = None

            # Clear density zones storage (plot.clear() removes visual elements)
            self.density_zones.clear()
            self.zone_id_counter = 0

            # Update manual zone button visibility based on zones state
            self.update_manual_zone_button_visibility()

            # Notify parent about zone clearing (will be updated again when zones are created)
            self.notify_parent_zones_updated()

            # Get data from the Data tab
            data = self.get_data_from_data_tab()
            if data is None:
                logger.warning("No data available from data tab")
                self.status_label.setText("Density Graph - No data available")
                return

            # Get the matching mode from the parent (Volatility_Statistics_tab)
            matching_mode = 'hl'  # Default to H/L matching
            if hasattr(self.parent, 'get_matching_mode'):
                matching_mode = self.parent.get_matching_mode()
                logger.info(f"Using matching mode: {matching_mode}")

            # Get matching rows based on the selected mode
            if matching_mode == 'weekday':
                # Use weekday matching
                matching_rows = self.get_rows_with_same_weekday(data)
                if matching_rows.empty:
                    logger.warning("No rows found with the same weekday")
                    self.status_label.setText("Density Graph - No rows found with the same weekday")
                    return

                # Get the weekday name for display
                # Find the date column or use the first column as a fallback
                date_column = None
                for col in data.columns:
                    if 'date' in col.lower() or 'time' in col.lower() or 'day' in col.lower():
                        date_column = col
                        break

                if date_column is None:
                    # Use the index if it's a DatetimeIndex
                    if isinstance(data.index, pd.DatetimeIndex):
                        latest_date = data.index[-1]
                    else:
                        # Use the first column as a last resort
                        try:
                            latest_date = pd.to_datetime(data.iloc[-1, 0])
                        except:
                            # If all else fails, use current date
                            from datetime import datetime
                            latest_date = datetime.now()
                else:
                    # Use the identified date column
                    try:
                        latest_date = pd.to_datetime(data[date_column].iloc[-1])
                    except:
                        # If conversion fails, use current date
                        from datetime import datetime
                        latest_date = datetime.now()

                latest_category = latest_date.strftime('%A')  # Use weekday name as the category for display

                logger.info(f"Using weekday matching for {weekday_name}")
            else:
                # Use H/L category matching (default)
                # Get the latest category
                latest_category = self.get_latest_category(data)
                if latest_category is None:
                    logger.warning("No category found in latest row")
                    self.status_label.setText("Density Graph - No category found in latest row")
                    return

                logger.info(f"Latest category: {latest_category}")

                # Get rows with the same category
                matching_rows = self.get_rows_with_same_category(data, latest_category)
                if matching_rows.empty:
                    logger.warning(f"No rows found with category '{latest_category}'")
                    self.status_label.setText(f"Density Graph - No rows found with category '{latest_category}'")
                    return

            # Get the reference price (current price or pivot price)
            reference_price_info = self.get_reference_price()
            if reference_price_info is None:
                logger.warning("Could not determine reference price")
                self.status_label.setText("Density Graph - Could not determine reference price")
                return

            reference_price, _, _ = reference_price_info
            logger.info(f"Using reference price: {reference_price}")

            # Extract high and low values from matching rows
            # Also get the dates to match highs and lows from the same date
            high_values = []
            low_values = []
            dates = []

            # Check if we're in current price mode and should use theoretical values
            is_current_price_mode = False
            if self.data_tab and hasattr(self.data_tab, 'calculation_mode'):
                is_current_price_mode = self.data_tab.calculation_mode == "current_price"
                logger.info(f"Current calculation mode: {self.data_tab.calculation_mode}")

            # Determine which columns to use for high and low values
            high_column = 'Projected High' if is_current_price_mode and 'Projected High' in matching_rows.columns else 'High'
            low_column = 'Projected Low' if is_current_price_mode and 'Projected Low' in matching_rows.columns else 'Low'

            logger.info(f"Using {high_column}/{low_column} values for density graph")

            # Check if the required columns exist
            if high_column not in matching_rows.columns or low_column not in matching_rows.columns:
                logger.warning(f"Required columns {high_column}/{low_column} not found in matching rows")
                self.status_label.setText(f"Density Graph - Required columns {high_column}/{low_column} not found")
                return

            # Log the column types for debugging
            logger.info(f"Column types: {matching_rows.dtypes.to_dict()}")

            # Extract high and low values and convert to numeric
            try:
                # If 'Time' column exists for date matching
                if 'Time' in matching_rows.columns:
                    for _, row in matching_rows.iterrows():
                        try:
                            high_val = pd.to_numeric(row[high_column])
                            low_val = pd.to_numeric(row[low_column])
                            high_values.append(high_val)
                            low_values.append(low_val)
                            dates.append(row['Time'])
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Error converting values to numeric: {e}")
                else:
                    # If no Time column, just use the values
                    high_series = pd.to_numeric(matching_rows[high_column], errors='coerce')
                    low_series = pd.to_numeric(matching_rows[low_column], errors='coerce')

                    # Drop NaN values
                    high_series = high_series.dropna()
                    low_series = low_series.dropna()

                    high_values = high_series.tolist()
                    low_values = low_series.tolist()

                    # Create dummy dates
                    dates = [f"Date_{i}" for i in range(len(high_values))]

                # Make sure we have valid data
                if not high_values or not low_values:
                    logger.warning("No valid high/low values found after conversion")
                    self.status_label.setText("Density Graph - No valid high/low values found")
                    return

                logger.info(f"Extracted {len(high_values)} high values and {len(low_values)} low values")
                logger.info(f"Sample high values: {high_values[:5]}")
                logger.info(f"Sample low values: {low_values[:5]}")

            except Exception as e:
                logger.error(f"Error extracting high/low values: {str(e)}", exc_info=True)
                self.status_label.setText(f"Density Graph - Error extracting values: {str(e)}")
                return

            # Calculate min and max values for y-axis early for padding calculation
            all_values = list(high_values) + list(low_values) + [reference_price]
            min_val = min(all_values)
            max_val = max(all_values)

            # Calculate padding - fixed at 0.5% above highest high and 0.5% below lowest low
            y_range = max_val - min_val
            padding_percentage = 0.005  # 0.5%
            padding_high = max_val * padding_percentage
            padding_low = min_val * padding_percentage
            # For display purposes, we'll still use a padding variable for label positioning
            padding = y_range * 0.005 if y_range > 0 else 0.5

            # Add vertical lines at x=2 and x=3 (keeping only the ones you didn't ask to remove)
            # Vertical line at x=2 for lows
            low_line = pg.InfiniteLine(pos=2, angle=90, pen=pg.mkPen(color='white', width=2))
            self.plot_widget.addItem(low_line)

            # Vertical line at x=3 for highs
            high_line = pg.InfiniteLine(pos=3, angle=90, pen=pg.mkPen(color='white', width=2))
            self.plot_widget.addItem(high_line)

            # We're not adding dots for current price, low values, or high values
            # This creates a cleaner visualization with only smooth curves
            # The data points are still used to generate the curves, but not displayed as dots

            # Log that we're skipping the dots for a cleaner visualization
            logger.info("Skipping data point dots for cleaner visualization with only smooth curves")

            # Create smooth spline connections between points
            # Use B-spline interpolation for smoother, more natural curves

            # Create professional-grade smooth spline connections between points
            # Use advanced B-spline interpolation with micro-step simulation
            logger.info(f"Creating professional-grade curves with micro-step IV simulation")

            # Use the continuous ribbon approach with enhanced rendering
            # This creates a fluid, ribbon-like effect similar to professional trading platforms
            try:
                # Group data points by their characteristics to create natural clusters
                # This simulates how professional systems group similar market behaviors

                # First, calculate the price range to help with normalization
                price_min = min(min(low_values), reference_price)
                price_max = max(max(high_values), reference_price)
                price_range = price_max - price_min

                # Create a list to store all curves for rendering
                all_curves = []

                # For each pair of low-high values, create a continuous path
                for i in range(len(low_values)):
                    try:
                        # Get the low and high values for this date
                        low_val = low_values[i]
                        high_val = high_values[i]

                        # Apply advanced normalization for deep OTM data
                        # This prevents flattening of wings while maintaining natural curve shapes

                        # Calculate relative distances as percentages of the full price range
                        # This is more robust than simple percentage differences
                        rel_distance_low = abs(low_val - reference_price) / price_range if price_range != 0 else 0
                        rel_distance_high = abs(high_val - reference_price) / price_range if price_range != 0 else 0

                        # Check for extreme outliers but preserve important market data points
                        # We'll keep the lowest low and highest high regardless of distance
                        is_lowest_low = low_val == min(low_values)
                        is_highest_high = high_val == max(high_values)

                        # Only skip if it's not an important market point and is an extreme outlier
                        if (rel_distance_low > 0.6 or rel_distance_high > 0.6) and not (is_lowest_low or is_highest_high):
                            logger.info(f"Skipping outlier point with relative distances: low={rel_distance_low:.2f}, high={rel_distance_high:.2f}")
                            continue

                        # Log when we're keeping important market points despite being outliers
                        if (rel_distance_low > 0.4 or rel_distance_high > 0.4) and (is_lowest_low or is_highest_high):
                            logger.info(f"Keeping {'lowest low' if is_lowest_low else 'highest high'} despite being an outlier: low={rel_distance_low:.2f}, high={rel_distance_high:.2f}")

                        # Apply sophisticated weighting factor based on market behavior patterns
                        # Points closer to ATM should have more natural curves
                        # Points far OTM should have more controlled curves to prevent distortion

                        # Base weight starts at 1.0 (full effect)
                        weight_factor = 1.0

                        # Calculate combined distance metric
                        combined_distance = (rel_distance_low + rel_distance_high) / 2

                        # Apply non-linear scaling for more natural falloff
                        # This creates a more gradual transition for mid-range points

                        # Special handling for lowest low and highest high to ensure they're properly displayed
                        if is_lowest_low or is_highest_high:
                            # Use a higher minimum weight for important market points
                            # This ensures they're rendered with sufficient curvature
                            base_weight = 0.5  # Higher base weight for important points

                            # Still apply some distance-based adjustment but less aggressively
                            if combined_distance > 0.05:
                                # Gentler falloff for important market points
                                weight_factor = base_weight + (1.0 - base_weight) / (1.0 + (combined_distance - 0.05))
                                weight_factor = max(0.5, min(0.9, weight_factor))  # Higher minimum for important points
                        else:
                            # Standard handling for regular points
                            if combined_distance > 0.05:  # More than 5% away from reference
                                # Use sigmoid-like function for smooth transition
                                # This creates more natural weighting than linear scaling
                                weight_factor = 1.0 / (1.0 + 2.0 * (combined_distance - 0.05))
                                weight_factor = max(0.3, min(1.0, weight_factor))  # Clamp between 0.3 and 1.0

                        # Create enhanced path with additional control points
                        # Professional systems use more control points for better curve shaping

                        # Basic path: reference → low → high → reference
                        x_path = [1.5, 2, 3, 3.5]
                        y_path = [reference_price, low_val, high_val, reference_price]

                        # Create a continuous spline through all points with micro-step simulation
                        # This creates the smooth, flowing curves seen in professional platforms
                        x_smooth, y_smooth = self.create_continuous_spline(
                            x_path, y_path,
                            num_output_points=500,  # Increased for sub-pixel resolution
                            smoothness=0.4 * weight_factor  # Apply sophisticated weight factor
                        )

                        if len(x_smooth) > 0 and len(y_smooth) > 0:
                            # Store curve data for rendering
                            # Include flags for lowest low and highest high to control rendering order
                            all_curves.append((x_smooth, y_smooth, combined_distance, is_lowest_low, is_highest_high))
                    except Exception as e:
                        logger.warning(f"Failed to create professional curve for data point {i}: {str(e)}")

                # First, separate important market points (lowest low, highest high) from regular points
                important_curves = [curve for curve in all_curves if curve[3] or curve[4]]  # is_lowest_low or is_highest_high
                regular_curves = [curve for curve in all_curves if not (curve[3] or curve[4])]

                # Sort regular curves by distance so closer curves render on top
                # This creates better visual layering like in professional platforms
                regular_curves.sort(key=lambda x: x[2], reverse=True)

                # Calculate density data regardless of whether we'll display it
                # This ensures we have the data ready if the user toggles the visualization on
                logger.info("Calculating price density data")

                # Use the theoretical price range for the density visualization
                # First check if we have theoretical prices available
                theoretical_min = None
                theoretical_max = None

                # Check if we're using theoretical values and they're available in the data
                is_using_theoretical = False
                if self.data_tab and hasattr(self.data_tab, 'calculation_mode'):
                    is_using_theoretical = (self.data_tab.calculation_mode == "current_price" and
                                           'Projected High' in matching_rows.columns and
                                           'Projected Low' in matching_rows.columns)

                if is_using_theoretical:
                    # Extract theoretical highs and lows
                    try:
                        theoretical_highs = pd.to_numeric(matching_rows['Projected High'], errors='coerce').dropna()
                        theoretical_lows = pd.to_numeric(matching_rows['Projected Low'], errors='coerce').dropna()

                        if not theoretical_highs.empty and not theoretical_lows.empty:
                            theoretical_min = theoretical_lows.min()
                            theoretical_max = theoretical_highs.max()
                            logger.info(f"Using theoretical price range: {theoretical_min:.2f} to {theoretical_max:.2f}")
                    except Exception as e:
                        logger.warning(f"Error extracting theoretical prices: {str(e)}")

                # If theoretical prices aren't available, fall back to lowest low and highest high
                if theoretical_min is None or theoretical_max is None:
                    if 'lowest_low' in locals() and 'highest_high' in locals():
                        density_y_min = lowest_low
                        density_y_max = highest_high
                        logger.info(f"Falling back to lowest low ({lowest_low:.2f}) and highest high ({highest_high:.2f}) for density range")
                    else:
                        # Final fallback to min/max values
                        density_y_min = min_val - padding_low
                        density_y_max = max_val + padding_high
                        logger.info(f"Using fallback range for density: {density_y_min:.2f} to {density_y_max:.2f}")
                else:
                    # Use the theoretical range
                    density_y_min = theoretical_min
                    density_y_max = theoretical_max

                # Create a histogram to track price density using the same method as vertical boxes
                # This ensures consistency between the main density visualization and the profile
                num_bins = 100
                bin_edges = np.linspace(density_y_min, density_y_max, num_bins + 1)
                bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
                density_counts = np.zeros(num_bins)

                # Count overlapping wave segments in each price bin (same as vertical box method)
                for curves_list in [regular_curves, important_curves]:
                    for curve_data in curves_list:
                        x_smooth, y_smooth = curve_data[0], curve_data[1]

                        # Extract segment between x=2 and x=3 for density calculation (same restriction as vertical boxes)
                        segment_x, segment_y = self.extract_curve_segment(x_smooth, y_smooth, 2.0, 3.0)
                        if len(segment_x) == 0:
                            continue

                        # Get the y-range of this wave segment (same as vertical box method)
                        y_min = min(segment_y)
                        y_max = max(segment_y)

                        # Add density to all bins that this wave segment covers (vertical box approach)
                        for i, bin_center in enumerate(bin_centers):
                            if y_min <= bin_center <= y_max:
                                density_counts[i] += 1

                # Normalize the counts to get relative density
                if density_counts.max() > 0:
                    density_counts = density_counts / density_counts.max()

                # Only render density visualization if enabled
                if hasattr(self, 'show_density') and self.show_density:
                    logger.info("Rendering density visualization using individual wave segments (enabled)")
                    logger.info(f"Number of curves to process: {len(all_curves)}")

                    # Create 25% transparency boxes for each density wave segment (data from x=2 to x=3, visual spans x=1.5 to x=3.5)
                    self.create_wave_segment_gradient(all_curves)

                    # Add volume profile if enabled
                    if hasattr(self, 'show_volume_profile') and self.show_volume_profile:
                        # Now add the volume profile-like visualization on the left side
                        # This creates a variable-width bar chart based on density
                        volume_x_start = -3.0  # Position further to the left as requested (changed from -0.2)
                        volume_max_width = 2.5  # Increased maximum width for better visibility at the new position

                        # Create volume profile bars with width based on density
                        for i, (bin_center, density) in enumerate(zip(bin_centers, density_counts)):
                            # Skip bins with very low density for cleaner visualization
                            if density < 0.02:  # Lower threshold for volume profile
                                continue

                            # Calculate bar width based on density
                            volume_width = density * volume_max_width

                            # Create a white rectangle with width based on density
                            volume_rect = QtWidgets.QGraphicsRectItem(
                                volume_x_start,
                                bin_center - (bin_edges[i+1] - bin_edges[i]) / 2,  # Center on bin
                                volume_width,
                                bin_edges[i+1] - bin_edges[i]  # Height is bin size
                            )

                            # Set the brush to white with opacity based on density
                            # Higher density = more opaque
                            volume_opacity = int(min(200, 80 + density * 120))  # Scale from 80-200 (out of 255)
                            volume_rect.setBrush(QtGui.QBrush(QtGui.QColor(255, 255, 255, volume_opacity)))

                            # Light gray border for definition
                            volume_rect.setPen(pg.mkPen(QtGui.QColor(200, 200, 200, 50), width=0.5))

                            # Add the volume profile rectangle to the plot
                            self.plot_widget.addItem(volume_rect)

                        # Add density scores over the volume profile bars
                        # Pass band values for filtering purple rays
                        band_values = {}
                        if hasattr(self, 'high_median') and self.high_median is not None:
                            band_values['high_median'] = self.high_median
                        if hasattr(self, 'low_median') and self.low_median is not None:
                            band_values['low_median'] = self.low_median

                        self.add_volume_profile_density_scores(bin_centers, density_counts, volume_x_start, volume_max_width, band_values, bin_edges)

                    logger.info(f"Added horizontal price density visualization with {num_bins} bins")
                else:
                    logger.info("Density visualization is disabled - skipping rendering")

                # Render regular curves on top of the density gradient
                for x_smooth, y_smooth, _, _, _ in regular_curves:
                    # Create a continuous ribbon-like curve with solid white lines
                    # Increase line width to ensure visibility over the gradient
                    ribbon = pg.PlotDataItem(
                        x=x_smooth,
                        y=y_smooth,
                        pen=pg.mkPen(color='white', width=2.0, cosmetic=True),  # Back to original width for better visibility
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None,
                        antialias=True  # Enable anti-aliasing for smoother curves
                    )
                    self.plot_widget.addItem(ribbon)

                # Render important curves last (they'll be on top) with slightly thicker lines
                for x_smooth, y_smooth, _, is_lowest_low, is_highest_high in important_curves:
                    # Use slightly thicker lines for important market points to ensure visibility
                    line_width = 3.0  # Increased from 2.5 to 3.0 for better visibility

                    # Create a continuous ribbon-like curve with solid white lines
                    ribbon = pg.PlotDataItem(
                        x=x_smooth,
                        y=y_smooth,
                        pen=pg.mkPen(color='white', width=line_width, cosmetic=True),  # Thicker for better visibility
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None,
                        antialias=True  # Enable anti-aliasing for smoother curves
                    )
                    self.plot_widget.addItem(ribbon)

                    # Log which important curves we're rendering
                    if is_lowest_low:
                        logger.info("Rendered lowest low curve with enhanced visibility")
                    if is_highest_high:
                        logger.info("Rendered highest high curve with enhanced visibility")

                # Add intersection dots to mark where density curves cross each other
                # Use the stored instance variables for the 50% band values
                band_values = {}
                if hasattr(self, 'high_median') and self.high_median is not None:
                    band_values['high_median'] = self.high_median
                    logger.info(f"Passing high_median to intersection detection: {self.high_median:.2f}")
                if hasattr(self, 'low_median') and self.low_median is not None:
                    band_values['low_median'] = self.low_median
                    logger.info(f"Passing low_median to intersection detection: {self.low_median:.2f}")

                if not band_values:
                    logger.warning("No 50% band values available for intersection filtering")

                self.add_density_intersection_dots(all_curves, band_values)

                logger.info(f"Successfully rendered {len(all_curves)} professional-grade curves with density gradient")

            except Exception as e:
                logger.warning(f"Failed to create professional-grade curves: {str(e)}")

            # Define colors for the percentile lines
            # Very light colors for 1% levels (highest high and lowest low)
            very_light_green = '#CCFFCC'  # Very light green
            very_light_red = '#FFCCCC'    # Very light red
            # Light colors for 25% levels
            light_green = '#90EE90'  # Light green
            light_red = '#FFA07A'    # Light red/salmon
            # Dark colors for 50% levels
            dark_green = '#006400'   # Dark green
            dark_red = '#8B0000'     # Dark red

            # Calculate percentiles for high values
            if len(high_values) > 0:
                # Find the highest high (1% level)
                highest_high = max(high_values)

                # Store highest_high as instance variable for intersection detection
                self.highest_high = highest_high

                # Add highest high line (1% level) - very light green
                highest_high_line = pg.InfiniteLine(
                    pos=highest_high,
                    angle=0,
                    pen=pg.mkPen(color=very_light_green, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(highest_high_line)
                logger.info(f"Added highest high line at {highest_high:.2f}")

                # Add label for the highest high line at x=5.35
                highest_high_label = pg.TextItem(
                    text=f"{highest_high:.2f} 1%",
                    color=very_light_green,
                    anchor=(0, 1)  # Anchor to bottom-left
                )
                highest_high_label.setPos(5.35, highest_high - (padding * 0.5))  # Position at x=5.35
                self.plot_widget.addItem(highest_high_label)

                # Get MaxAvg High from volatility graph for 25% level
                maxavg_high = None
                if hasattr(self.parent, 'volatility_graph_tab'):
                    volatility_levels = self.parent.volatility_graph_tab.get_volatility_levels()
                    maxavg_high = volatility_levels.get('maxavg_high')
                    logger.info(f"Using MaxAvg High from volatility graph: {maxavg_high}")

                # Fallback to local calculation if volatility graph value not available
                if maxavg_high is None:
                    sorted_high_values = np.sort(high_values)[::-1]  # Sort in descending order
                    top_half_count = max(1, len(sorted_high_values) // 2)
                    top_half_highs = sorted_high_values[:top_half_count]
                    maxavg_high = np.mean(top_half_highs)
                    logger.info(f"Using fallback MaxAvg High calculation: {maxavg_high}")

                # Store maxavg_high as instance variable for intersection detection
                self.maxavg_high = maxavg_high

                # Add MaxAvg High line for high values (25% level) - light green
                maxavg_high_line = pg.InfiniteLine(
                    pos=maxavg_high,
                    angle=0,
                    pen=pg.mkPen(color=light_green, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(maxavg_high_line)
                logger.info(f"Added MaxAvg High line for high values at {maxavg_high:.2f}")

                # Add label for the MaxAvg High line at x=5.35
                maxavg_high_label = pg.TextItem(
                    text=f"25% {maxavg_high:.2f}",
                    color=light_green,
                    anchor=(0, 1)  # Anchor to bottom-left
                )
                maxavg_high_label.setPos(5.35, maxavg_high - (padding * 0.5))  # Position at x=5.35
                self.plot_widget.addItem(maxavg_high_label)

                # Get High Median from volatility graph for 50% level
                high_median = None
                if hasattr(self.parent, 'volatility_graph_tab'):
                    volatility_levels = self.parent.volatility_graph_tab.get_volatility_levels()
                    high_median = volatility_levels.get('avg_high_lowest_high')
                    logger.info(f"Using High Median from volatility graph: {high_median}")

                # Fallback to local calculation if volatility graph value not available
                if high_median is None:
                    high_series = pd.Series(high_values)
                    high_series = pd.to_numeric(high_series, errors='coerce').dropna()
                    if len(high_series) > 0:
                        sorted_high_values_median = high_series.sort_values().values
                        high_median = sorted_high_values_median[(len(sorted_high_values_median) - 1) // 2]
                    else:
                        high_median = np.median(high_values)  # Fallback
                    logger.info(f"Using fallback High Median calculation: {high_median}")

                # Store high_median as instance variable for intersection detection
                self.high_median = high_median

                # Add High Median line for high values (50% level) - dark green
                high_median_line = pg.InfiniteLine(
                    pos=high_median,
                    angle=0,
                    pen=pg.mkPen(color=dark_green, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(high_median_line)
                logger.info(f"Added High Median line for high values at {high_median:.2f}")

                # Add label for the High Median line at x=5.35
                high_median_label = pg.TextItem(
                    text=f"50% {high_median:.2f}",
                    color=dark_green,
                    anchor=(0, 1)  # Anchor to bottom-left
                )
                high_median_label.setPos(5.35, high_median - (padding * 0.5))  # Position at x=5.35
                self.plot_widget.addItem(high_median_label)

            # Calculate percentiles for low values
            if len(low_values) > 0:
                # Find the lowest low (1% level)
                lowest_low = min(low_values)

                # Store lowest_low as instance variable for intersection detection
                self.lowest_low = lowest_low

                # Add lowest low line (1% level) - very light red
                lowest_low_line = pg.InfiniteLine(
                    pos=lowest_low,
                    angle=0,
                    pen=pg.mkPen(color=very_light_red, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(lowest_low_line)
                logger.info(f"Added lowest low line at {lowest_low:.2f}")

                # Add label for the lowest low line at x=5.35
                lowest_low_label = pg.TextItem(
                    text=f"{lowest_low:.2f} 1%",
                    color=very_light_red,
                    anchor=(0, 0)  # Anchor to top-left
                )
                lowest_low_label.setPos(5.35, lowest_low + (padding * 0.5))  # Position at x=5.35
                self.plot_widget.addItem(lowest_low_label)

                # Get MaxAvg Low from volatility graph for 25% level
                maxavg_low = None
                if hasattr(self.parent, 'volatility_graph_tab'):
                    volatility_levels = self.parent.volatility_graph_tab.get_volatility_levels()
                    maxavg_low = volatility_levels.get('maxavg_low')
                    logger.info(f"Using MaxAvg Low from volatility graph: {maxavg_low}")

                # Fallback to local calculation if volatility graph value not available
                if maxavg_low is None:
                    sorted_low_values = np.sort(low_values)  # Sort in ascending order (lowest first)
                    bottom_half_count = max(1, len(sorted_low_values) // 2)
                    bottom_half_lows = sorted_low_values[:bottom_half_count]
                    maxavg_low = np.mean(bottom_half_lows)
                    logger.info(f"Using fallback MaxAvg Low calculation: {maxavg_low}")

                # Store maxavg_low as instance variable for intersection detection
                self.maxavg_low = maxavg_low

                # Add MaxAvg Low line for low values (25% level) - light red
                maxavg_low_line = pg.InfiniteLine(
                    pos=maxavg_low,
                    angle=0,
                    pen=pg.mkPen(color=light_red, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(maxavg_low_line)
                logger.info(f"Added MaxAvg Low line for low values at {maxavg_low:.2f}")

                # Add label for the MaxAvg Low line at x=5.35
                maxavg_low_label = pg.TextItem(
                    text=f"25% {maxavg_low:.2f}",
                    color=light_red,
                    anchor=(0, 0)  # Anchor to top-left
                )
                maxavg_low_label.setPos(5.35, maxavg_low + (padding * 0.5))  # Position at x=5.35
                self.plot_widget.addItem(maxavg_low_label)

                # Get Low Median from volatility graph for 50% level
                low_median = None
                if hasattr(self.parent, 'volatility_graph_tab'):
                    volatility_levels = self.parent.volatility_graph_tab.get_volatility_levels()
                    low_median = volatility_levels.get('avg_low_highest_low')
                    logger.info(f"Using Low Median from volatility graph: {low_median}")

                # Fallback to local calculation if volatility graph value not available
                if low_median is None:
                    low_series = pd.Series(low_values)
                    low_series = pd.to_numeric(low_series, errors='coerce').dropna()
                    if len(low_series) > 0:
                        sorted_low_values_median = low_series.sort_values().values
                        low_median = sorted_low_values_median[(len(sorted_low_values_median) - 1) // 2]
                    else:
                        low_median = np.median(low_values)  # Fallback
                    logger.info(f"Using fallback Low Median calculation: {low_median}")

                # Store low_median as instance variable for intersection detection
                self.low_median = low_median

                # Add Low Median line for low values (50% level) - dark red
                low_median_line = pg.InfiniteLine(
                    pos=low_median,
                    angle=0,
                    pen=pg.mkPen(color=dark_red, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(low_median_line)
                logger.info(f"Added Low Median line for low values at {low_median:.2f}")

                # Add label for the Low Median line at x=5.35
                low_median_label = pg.TextItem(
                    text=f"50% {low_median:.2f}",
                    color=dark_red,
                    anchor=(0, 0)  # Anchor to top-left
                )
                low_median_label.setPos(5.35, low_median + (padding * 0.5))  # Position at x=5.35
                self.plot_widget.addItem(low_median_label)

            # Add real current price line (finite line) - white, stops before label area
            # Create a line that goes from x=-0.5 to the right edge, leaving space for the label at x=-1.5
            current_price_line_x = [-0.5, 5.5]  # Start after label area, extend to right edge
            current_price_line_y = [reference_price, reference_price]  # Horizontal line at current price
            current_price_line = pg.PlotDataItem(
                x=current_price_line_x,
                y=current_price_line_y,
                pen=pg.mkPen(color='white', width=2),
                symbolPen=None,
                symbolBrush=None,
                symbol=None
            )
            self.plot_widget.addItem(current_price_line)



            # Calculate position for headers - 0.25% above highest high
            header_y_position = highest_high * 1.0025 if 'highest_high' in locals() else reference_price * 1.0025

            # Only add option data if enabled
            if hasattr(self, 'show_option_data') and self.show_option_data:
                # Add column headers for call and put bids and IVs
                # Call price header at x=0 (dynamic label based on price type)
                call_price_header_text = "Call Ask" if self.use_ask_prices else "Call Bid"
                call_price_header = pg.TextItem(
                    text=call_price_header_text,
                    color='white',
                    anchor=(0, 1)  # Bottom-left aligned
                )
                call_price_header.setPos(0, header_y_position)
                self.plot_widget.addItem(call_price_header)

                # Call IV header at x=0.25
                call_iv_header = pg.TextItem(
                    text="Call IV",
                    color='white',
                    anchor=(0, 1)  # Bottom-left aligned
                )
                call_iv_header.setPos(0.25, header_y_position)
                self.plot_widget.addItem(call_iv_header)

                # Strike header at x=-0.5
                strike_header = pg.TextItem(
                    text="Strike",
                    color='white',
                    anchor=(0, 1)  # Bottom-left aligned
                )
                strike_header.setPos(-0.5, header_y_position)
                self.plot_widget.addItem(strike_header)

                # Put price header at x=4.75 (dynamic label based on price type)
                put_price_header_text = "Put Ask" if self.use_ask_prices else "Put Bid"
                put_price_header = pg.TextItem(
                    text=put_price_header_text,
                    color='white',
                    anchor=(0, 1)  # Bottom-left aligned
                )
                put_price_header.setPos(4.75, header_y_position)
                self.plot_widget.addItem(put_price_header)

                # Put IV header at x=5
                put_iv_header = pg.TextItem(
                    text="Put IV",
                    color='white',
                    anchor=(0, 1)  # Bottom-left aligned
                )
                put_iv_header.setPos(5, header_y_position)
                self.plot_widget.addItem(put_iv_header)

            # Get real options data from the option_greeks tab
            try:
                calls_df, puts_df, _ = self.get_options_data()  # Ignore the current price as we already have reference_price
                price_type = "ask" if self.use_ask_prices else "bid"
                logger.info(f"Successfully retrieved options data: {len(calls_df) if calls_df is not None else 0} calls, {len(puts_df) if puts_df is not None else 0} puts (using {price_type} prices)")

                # Log data structure for debugging
                if calls_df is not None and not calls_df.empty:
                    logger.info(f"Calls DataFrame columns: {list(calls_df.columns)}")
                    if len(calls_df) > 0:
                        first_call = calls_df.iloc[0]
                        logger.info(f"First call sample - Strike: {first_call.get('strike', 'N/A')}, Bid: {first_call.get('bid', 'N/A')}, Ask: {first_call.get('ask', 'N/A')}")

                if puts_df is not None and not puts_df.empty:
                    logger.info(f"Puts DataFrame columns: {list(puts_df.columns)}")
                    if len(puts_df) > 0:
                        first_put = puts_df.iloc[0]
                        logger.info(f"First put sample - Strike: {first_put.get('strike', 'N/A')}, Bid: {first_put.get('bid', 'N/A')}, Ask: {first_put.get('ask', 'N/A')}")

            except Exception as e:
                logger.warning(f"Error retrieving options data: {str(e)}. Using mock data instead.")
                calls_df, puts_df = None, None

            # Add strike prices at 0x axis from lowest low to highest high
            # First, determine the range and step size
            if 'lowest_low' in locals() and 'highest_high' in locals():
                price_range = highest_high - lowest_low

                # Always use step size of 1.0 as requested
                step_size = 1.0

                # Round the lowest_low down and highest_high up to the nearest step
                start_price = math.floor(lowest_low / step_size) * step_size
                end_price = math.ceil(highest_high / step_size) * step_size

                # Limit the number of strike prices to display if the range is very large
                # to avoid performance issues
                max_strikes = 100  # Maximum number of strikes to display
                num_strikes = int((end_price - start_price) / step_size) + 1

                if num_strikes > max_strikes:
                    # Calculate a new step size to limit the number of strikes
                    # while still using increments of 1.0 or multiples of it
                    required_step = math.ceil((end_price - start_price) / max_strikes)
                    # Round up to the nearest multiple of 1.0
                    step_size = math.ceil(required_step / 1.0) * 1.0
                    logger.info(f"Adjusted step size to {step_size} to limit the number of strikes")

                    # Recalculate start and end prices with the new step size
                    start_price = math.floor(lowest_low / step_size) * step_size
                    end_price = math.ceil(highest_high / step_size) * step_size

                # Create dictionaries for quick lookup of option data by strike
                call_data_by_strike = {}
                put_data_by_strike = {}

                # If we have real options data, populate the dictionaries and calibrate SABR model
                market_data_for_calibration = []

                if calls_df is not None and not calls_df.empty:
                    for _, row in calls_df.iterrows():
                        if 'strike' in row and 'bid' in row and 'impliedVolatility' in row:
                            strike = row['strike']
                            # Properly handle ask prices - only use fallback if ask is truly missing or invalid
                            # Also handle NaN bid prices
                            bid_price = row['bid'] if pd.notna(row['bid']) and row['bid'] >= 0 else 0.01
                            ask_price = row['ask'] if 'ask' in row and pd.notna(row['ask']) and row['ask'] >= 0 else max(bid_price, 0.01)

                            if 'ask' not in row:
                                logger.warning(f"Missing 'ask' column for call strike {strike}, using fallback")
                            elif pd.isna(row['ask']):
                                logger.debug(f"NaN ask price for call strike {strike}, using fallback")
                            elif row['ask'] < 0:
                                logger.warning(f"Negative ask price for call strike {strike}, using fallback")

                            if pd.isna(row['bid']):
                                logger.debug(f"NaN bid price for call strike {strike}, using 0.01")

                            call_data_by_strike[strike] = {
                                'bid': bid_price,
                                'ask': ask_price,
                                'iv': row['impliedVolatility']
                            }

                            # Log when bid and ask are the same for debugging
                            if bid_price == ask_price:
                                logger.debug(f"Call strike {strike}: bid and ask are the same ({bid_price})")
                            else:
                                logger.debug(f"Call strike {strike}: bid={bid_price}, ask={ask_price}")

                            # Collect data for SABR calibration
                            if row['impliedVolatility'] > 0:
                                # Calculate time to expiration
                                time_to_expiry = 30.0 / 365.0  # Default
                                if self.selected_expiry_date:
                                    try:
                                        from datetime import datetime
                                        expiry_date = datetime.strptime(self.selected_expiry_date, '%Y-%m-%d')
                                        today = datetime.now()
                                        days_to_expiry = max((expiry_date - today).days, 1)
                                        time_to_expiry = days_to_expiry / 365.0
                                    except:
                                        pass

                                market_data_for_calibration.append((strike, row['impliedVolatility'], time_to_expiry, reference_price))

                if puts_df is not None and not puts_df.empty:
                    for _, row in puts_df.iterrows():
                        if 'strike' in row and 'bid' in row and 'impliedVolatility' in row:
                            strike = row['strike']
                            # Properly handle ask prices - only use fallback if ask is truly missing or invalid
                            # Also handle NaN bid prices
                            bid_price = row['bid'] if pd.notna(row['bid']) and row['bid'] >= 0 else 0.01
                            ask_price = row['ask'] if 'ask' in row and pd.notna(row['ask']) and row['ask'] >= 0 else max(bid_price, 0.01)

                            if 'ask' not in row:
                                logger.warning(f"Missing 'ask' column for put strike {strike}, using fallback")
                            elif pd.isna(row['ask']):
                                logger.debug(f"NaN ask price for put strike {strike}, using fallback")
                            elif row['ask'] < 0:
                                logger.warning(f"Negative ask price for put strike {strike}, using fallback")

                            if pd.isna(row['bid']):
                                logger.debug(f"NaN bid price for put strike {strike}, using 0.01")

                            put_data_by_strike[strike] = {
                                'bid': bid_price,
                                'ask': ask_price,
                                'iv': row['impliedVolatility']
                            }

                            # Log when bid and ask are the same for debugging
                            if bid_price == ask_price:
                                logger.debug(f"Put strike {strike}: bid and ask are the same ({bid_price})")
                            else:
                                logger.debug(f"Put strike {strike}: bid={bid_price}, ask={ask_price}")

                            # Collect data for SABR calibration (avoid duplicates from same strike)
                            if row['impliedVolatility'] > 0 and not any(data[0] == strike for data in market_data_for_calibration):
                                # Calculate time to expiration
                                time_to_expiry = 30.0 / 365.0  # Default
                                if self.selected_expiry_date:
                                    try:
                                        from datetime import datetime
                                        expiry_date = datetime.strptime(self.selected_expiry_date, '%Y-%m-%d')
                                        today = datetime.now()
                                        days_to_expiry = max((expiry_date - today).days, 1)
                                        time_to_expiry = days_to_expiry / 365.0
                                    except:
                                        pass

                                market_data_for_calibration.append((strike, row['impliedVolatility'], time_to_expiry, reference_price))

                # Log summary of bid/ask data for debugging
                call_same_count = sum(1 for data in call_data_by_strike.values() if data['bid'] == data['ask'])
                put_same_count = sum(1 for data in put_data_by_strike.values() if data['bid'] == data['ask'])
                logger.info(f"Bid/Ask Summary - Calls: {call_same_count}/{len(call_data_by_strike)} have same bid/ask, Puts: {put_same_count}/{len(put_data_by_strike)} have same bid/ask")

                # Calibrate SABR model to market data if we have sufficient data points
                if len(market_data_for_calibration) >= 3:
                    try:
                        logger.info(f"Calibrating SABR model with {len(market_data_for_calibration)} market data points")
                        calibration_result = self.sabr_model.calibrate_to_market_data(market_data_for_calibration)
                        logger.info(f"SABR calibration completed: {calibration_result}")
                    except Exception as e:
                        logger.warning(f"SABR calibration failed: {e}, using default parameters")

                # Validate bid/ask ordering and create sets of valid strikes for sine wave rendering
                # CRITICAL FIX: Generate separate validation sets for bid and ask prices
                # The display toggle should only affect which curves are shown, not which IV zones are calculated
                valid_call_strikes_display = self.validate_call_bid_ask_ordering(call_data_by_strike, self.use_ask_prices)
                valid_put_strikes_display = self.validate_put_bid_ask_ordering(put_data_by_strike, self.use_ask_prices)

                # Always validate both bid and ask prices for IV zone calculations (independent of display toggle)
                valid_call_strikes_bid = self.validate_call_bid_ask_ordering(call_data_by_strike, False)  # Always use bid for validation
                valid_put_strikes_bid = self.validate_put_bid_ask_ordering(put_data_by_strike, False)    # Always use bid for validation
                valid_call_strikes_ask = self.validate_call_bid_ask_ordering(call_data_by_strike, True)   # Always use ask for validation
                valid_put_strikes_ask = self.validate_put_bid_ask_ordering(put_data_by_strike, True)     # Always use ask for validation

                # Combine all valid strikes for comprehensive IV zone calculation
                all_valid_call_strikes = valid_call_strikes_bid.union(valid_call_strikes_ask)
                all_valid_put_strikes = valid_put_strikes_bid.union(valid_put_strikes_ask)

                logger.info(f"Valid call strikes for display: {len(valid_call_strikes_display)} out of {len(call_data_by_strike)}")
                logger.info(f"Valid put strikes for display: {len(valid_put_strikes_display)} out of {len(put_data_by_strike)}")
                logger.info(f"Valid call strikes for bid IV zones: {len(valid_call_strikes_bid)} out of {len(call_data_by_strike)}")
                logger.info(f"Valid put strikes for bid IV zones: {len(valid_put_strikes_bid)} out of {len(put_data_by_strike)}")
                logger.info(f"Valid call strikes for ask IV zones: {len(valid_call_strikes_ask)} out of {len(call_data_by_strike)}")
                logger.info(f"Valid put strikes for ask IV zones: {len(valid_put_strikes_ask)} out of {len(put_data_by_strike)}")
                logger.info(f"All valid call strikes for IV zones: {len(all_valid_call_strikes)} out of {len(call_data_by_strike)}")
                logger.info(f"All valid put strikes for IV zones: {len(all_valid_put_strikes)} out of {len(put_data_by_strike)}")

                # Initialize lists to store call and put curve data for breakthrough detection
                call_curves_data = []  # List of (strike, peak_y, curve_y_values, x_values)
                put_curves_data = []   # List of (strike, trough_y, curve_y_values, x_values)

                # Initialize separate lists for bid price curves (used for IV Peak determination)
                bid_call_curves_data = []
                bid_put_curves_data = []

                # Initialize separate lists for ask price curves (used for IV Walls, Overflow, Max Fear)
                ask_call_curves_data = []
                ask_put_curves_data = []



                # Get the 1% green and red levels for reference (but don't use for filtering strikes)
                # Green 1% level is the highest high, Red 1% level is the lowest low
                green_1_percent_level = max(high_values) if len(high_values) > 0 else float('inf')
                red_1_percent_level = min(low_values) if len(low_values) > 0 else float('-inf')

                logger.info(f"1% levels for reference: Green (highest high) = {green_1_percent_level:.2f}, Red (lowest low) = {red_1_percent_level:.2f}")

                # Add strike price labels at regular intervals
                current_price = start_price
                while current_price <= end_price:
                    # Show all strikes in the range (removed 1% level filtering)
                    if True:  # Always process all strikes in the range
                        # Grid lines removed per user request

                        # Only add option data if enabled
                        if hasattr(self, 'show_option_data') and self.show_option_data:
                            # Create a strike price label at x=-0.5
                            strike_label = pg.TextItem(
                                text=f"{current_price:.2f}",
                                color='white',
                                anchor=(0, 0.5)  # Anchor to middle-left
                            )
                            # Position the label at x=-0.5 and at the current price level
                            strike_label.setPos(-0.5, current_price)
                            self.plot_widget.addItem(strike_label)

                            # Get real call data for this strike if available
                            call_price = None
                            call_iv = None
                            if current_price in call_data_by_strike:
                                # Use ask price if enabled, otherwise use bid price
                                if self.use_ask_prices:
                                    call_price = call_data_by_strike[current_price]['ask']
                                else:
                                    call_price = call_data_by_strike[current_price]['bid']
                                call_iv = call_data_by_strike[current_price]['iv']

                            # Get real put data for this strike if available
                            put_price = None
                            put_iv = None
                            if current_price in put_data_by_strike:
                                # Use ask price if enabled, otherwise use bid price
                                if self.use_ask_prices:
                                    put_price = put_data_by_strike[current_price]['ask']
                                else:
                                    put_price = put_data_by_strike[current_price]['bid']
                                put_iv = put_data_by_strike[current_price]['iv']

                            # Always ensure we have call and put data (generate SABR-based values if missing)
                            # This ensures ALL strikes get call and put lines drawn
                            if True:  # Always check and generate missing data
                                # Calculate time to expiration (assume 30 days if not available)
                                time_to_expiry = 30.0 / 365.0  # Default to 30 days
                                if self.selected_expiry_date:
                                    try:
                                        from datetime import datetime
                                        expiry_date = datetime.strptime(self.selected_expiry_date, '%Y-%m-%d')
                                        today = datetime.now()
                                        days_to_expiry = max((expiry_date - today).days, 1)
                                        time_to_expiry = days_to_expiry / 365.0
                                    except:
                                        pass  # Use default

                                # Generate SABR-based implied volatilities and prices
                                if call_iv is None:
                                    # Calculate SABR implied volatility for call
                                    call_iv = self.sabr_model.sabr_volatility(reference_price, current_price, time_to_expiry)
                                    call_iv = round(call_iv, 3)  # Round to 3 decimal places for display

                                if put_iv is None:
                                    # Calculate SABR implied volatility for put
                                    put_iv = self.sabr_model.sabr_volatility(reference_price, current_price, time_to_expiry)
                                    put_iv = round(put_iv, 3)  # Round to 3 decimal places for display

                                # Generate option prices using SABR model if missing
                                if call_price is None:
                                    try:
                                        sabr_call_price, _ = calculate_sabr_option_price('c', reference_price, current_price, time_to_expiry, sabr_model=self.sabr_model)
                                        call_price = round(max(0.01, sabr_call_price), 2)
                                    except:
                                        # Fallback calculation
                                        price_diff = abs(current_price - reference_price)
                                        atm_factor = max(0.01, 1.0 - (price_diff / (price_range / 2)))
                                        call_price = round(max(0.01, 1.0 * atm_factor * (1.5 if current_price < reference_price else 0.5)), 2)

                                if put_price is None:
                                    try:
                                        sabr_put_price, _ = calculate_sabr_option_price('p', reference_price, current_price, time_to_expiry, sabr_model=self.sabr_model)
                                        put_price = round(max(0.01, sabr_put_price), 2)
                                    except:
                                        # Fallback calculation
                                        price_diff = abs(current_price - reference_price)
                                        atm_factor = max(0.01, 1.0 - (price_diff / (price_range / 2)))
                                        put_price = round(max(0.01, 1.0 * atm_factor * (1.5 if current_price > reference_price else 0.5)), 2)

                            # Add call price (bid or ask) at x=0
                            call_price_label = pg.TextItem(
                                text=f"{call_price:.2f}",
                                color='white',
                                anchor=(0, 0.5)  # Left aligned
                            )
                            call_price_label.setPos(0, current_price)
                            self.plot_widget.addItem(call_price_label)

                            # Add call IV at x=0.25
                            call_iv_label = pg.TextItem(
                                text=f"{call_iv:.2f}",  # Display as decimal without percentage sign
                                color='white',
                                anchor=(0, 0.5)  # Left aligned
                            )
                            call_iv_label.setPos(0.25, current_price)
                            self.plot_widget.addItem(call_iv_label)

                            # Add put price (bid or ask) at x=4.75
                            put_price_label = pg.TextItem(
                                text=f"{put_price:.2f}",
                                color='white',
                                anchor=(0, 0.5)  # Left aligned
                            )
                            put_price_label.setPos(4.75, current_price)
                            self.plot_widget.addItem(put_price_label)

                            # Add put IV at x=5
                            put_iv_label = pg.TextItem(
                                text=f"{put_iv:.2f}",  # Display as decimal without percentage sign
                                color='white',
                                anchor=(0, 0.5)  # Left aligned
                            )
                            put_iv_label.setPos(5, current_price)
                            self.plot_widget.addItem(put_iv_label)

                            # Initialize variables to avoid UnboundLocalError
                            call_x_smooth, call_y_smooth = [], []

                            # Create call sine wave for all strikes (validation removed)
                            if True:  # Always create call lines
                                # Create call line with high and low points
                                # Calculate offset for call line (call_price / 10)
                                call_offset = call_price / 10
                                call_starting_point = current_price + call_price

                                # Call line coordinates: strike price → trough → peak → new price (+10 formula)
                                # Start at x=0.5 with y = strike price
                                # Go to x=0.7 with y = strike - offset (trough)
                                # Go to x=1.3 with y = starting high point + offset (peak)
                                # End at x=1.5 with y = strike + call_price (new price)
                                call_x_coords = [0.5, 0.7, 1.3, 1.5]
                                call_y_coords = [
                                    current_price,                          # Strike price at x=0.5
                                    current_price - call_offset,           # Trough (strike - offset) at x=0.7
                                    call_starting_point + call_offset,     # Peak (starting high point + offset) at x=1.3
                                    call_starting_point                     # New price (strike + call_price) at x=1.5
                                ]

                                # Create B-spline curved line optimized for smooth call peaks
                                # Call constraint: NO peak at new price location (x=1.5)
                                call_x_smooth, call_y_smooth = self.create_constrained_curve(
                                    call_x_coords, call_y_coords,
                                    curve_type='call',  # B-spline optimized for smooth peaks
                                    num_points=100
                                )

                                call_connection_line = pg.PlotDataItem(
                                    x=call_x_smooth,
                                    y=call_y_smooth,
                                    pen=pg.mkPen(color='white', width=1),
                                    symbolPen=None,
                                    symbolBrush=None,
                                    symbol=None
                                )
                                self.plot_widget.addItem(call_connection_line)

                                # Store call curve data for breakthrough detection
                                if len(call_y_smooth) > 0:
                                    call_peak_y = np.max(call_y_smooth)
                                    call_curves_data.append((current_price, call_peak_y, call_y_smooth, call_x_smooth))



                            # Initialize put variables to avoid UnboundLocalError
                            put_x_smooth, put_y_smooth = [], []

                            # Create put sine wave for all strikes (validation removed)
                            if True:  # Always create put lines
                                # Create put line with high and low points
                                # Calculate offset for put line (put_price / 10)
                                put_offset = put_price / 10
                                put_starting_point = current_price - put_price

                                # Put line coordinates: new price (+10 formula) → trough → peak → strike price
                                # Start at x=3.5 with y = new price (strike - put_price)
                                # Go to x=3.7 with y = trough (new price - offset)
                                # Go to x=4.3 with y = peak (strike + offset)
                                # End at x=4.5 with y = strike price
                                put_x_coords = [3.5, 3.7, 4.3, 4.5]
                                put_y_coords = [
                                    put_starting_point,                     # New price (strike - put_price) at x=3.5
                                    put_starting_point - put_offset,       # Trough (new price - offset) at x=3.7
                                    current_price + put_offset,            # Peak (strike + offset) at x=4.3
                                    current_price                          # Strike price at x=4.5
                                ]

                                # Create B-spline curved line optimized for smooth put troughs
                                # Put constraint: NO trough at new price location (x=3.5)
                                put_x_smooth, put_y_smooth = self.create_constrained_curve(
                                    put_x_coords, put_y_coords,
                                    curve_type='put',  # B-spline optimized for smooth troughs
                                    num_points=100
                                )

                                put_connection_line = pg.PlotDataItem(
                                    x=put_x_smooth,
                                    y=put_y_smooth,
                                    pen=pg.mkPen(color='white', width=1),
                                    symbolPen=None,
                                    symbolBrush=None,
                                    symbol=None
                                )
                                self.plot_widget.addItem(put_connection_line)

                                # Store put curve data for breakthrough detection
                                if len(put_y_smooth) > 0:
                                    put_trough_y = np.min(put_y_smooth)
                                    put_curves_data.append((current_price, put_trough_y, put_y_smooth, put_x_smooth))



                            else:
                                logger.debug(f"Skipping put sine wave for strike {current_price:.2f} due to out-of-order bid/ask values")

                        else:
                            logger.debug(f"Skipping option data for strike {current_price:.2f} - option data disabled")

                        # CRITICAL FIX: Always calculate bid and ask curves for IV zones regardless of option data display toggle
                        # Generate bid call curves for IV Peak determination (independent of display toggle)
                        if current_price in call_data_by_strike and current_price in valid_call_strikes_bid:
                            bid_call_price = call_data_by_strike[current_price]['bid']
                            bid_call_offset = bid_call_price / 10
                            bid_call_starting_point = current_price + bid_call_price

                            # Calculate curve with bid prices for IV Peak determination
                            # Pattern: strike price → trough → peak → new price (+10 formula)
                            bid_call_x_coords = [0.5, 0.7, 1.3, 1.5]
                            bid_call_y_coords = [
                                current_price,                              # Strike price at x=0.5
                                current_price - bid_call_offset,           # Trough at x=0.7
                                bid_call_starting_point + bid_call_offset, # Peak at x=1.3
                                bid_call_starting_point                     # New price at x=1.5
                            ]

                            # Create curved line for bid prices (for IV Peak determination)
                            # Call constraint: NO peak at new price location (x=1.5)
                            bid_call_x_smooth, bid_call_y_smooth = self.create_constrained_curve(
                                bid_call_x_coords, bid_call_y_coords,
                                curve_type='call',  # Ensures no peak at new price location
                                num_points=100
                            )

                            # Store bid call curve data for IV Peak determination
                            if len(bid_call_y_smooth) > 0:
                                bid_call_peak_y = np.max(bid_call_y_smooth)
                                bid_call_curves_data.append((current_price, bid_call_peak_y, bid_call_y_smooth, bid_call_x_smooth))

                        # Generate ask call curves for IV Walls, Overflow, Max Fear determination (independent of display toggle)
                        if current_price in call_data_by_strike and current_price in valid_call_strikes_ask:
                            ask_call_price = call_data_by_strike[current_price]['ask']
                            ask_call_offset = ask_call_price / 10
                            ask_call_starting_point = current_price + ask_call_price

                            # Calculate curve with ask prices for IV Walls, Overflow, Max Fear determination
                            # Pattern: strike price → trough → peak → new price (+10 formula)
                            ask_call_x_coords = [0.5, 0.7, 1.3, 1.5]
                            ask_call_y_coords = [
                                current_price,                              # Strike price at x=0.5
                                current_price - ask_call_offset,           # Trough at x=0.7
                                ask_call_starting_point + ask_call_offset, # Peak at x=1.3
                                ask_call_starting_point                     # New price at x=1.5
                            ]

                            # Create curved line for ask prices (for IV Walls, Overflow, Max Fear determination)
                            # Call constraint: NO peak at new price location (x=1.5)
                            ask_call_x_smooth, ask_call_y_smooth = self.create_constrained_curve(
                                ask_call_x_coords, ask_call_y_coords,
                                curve_type='call',  # Ensures no peak at new price location
                                num_points=100
                            )

                            # Store ask call curve data for IV Walls, Overflow, Max Fear determination
                            if len(ask_call_y_smooth) > 0:
                                ask_call_peak_y = np.max(ask_call_y_smooth)
                                ask_call_curves_data.append((current_price, ask_call_peak_y, ask_call_y_smooth, ask_call_x_smooth))

                        # Generate bid put curves for IV Peak determination (independent of display toggle)
                        if current_price in put_data_by_strike and current_price in valid_put_strikes_bid:
                            bid_put_price = put_data_by_strike[current_price]['bid']
                            bid_put_offset = bid_put_price / 10
                            bid_put_starting_point = current_price - bid_put_price

                            # Calculate curve with bid prices for IV Peak determination
                            # Pattern: new price (+10 formula) → trough → peak → strike price
                            bid_put_x_coords = [3.5, 3.7, 4.3, 4.5]
                            bid_put_y_coords = [
                                bid_put_starting_point,                     # New price at x=3.5
                                bid_put_starting_point - bid_put_offset,   # Trough at x=3.7
                                current_price + bid_put_offset,            # Peak at x=4.3
                                current_price                              # Strike price at x=4.5
                            ]

                            # Create curved line for bid prices (for IV Peak determination)
                            # Put constraint: NO trough at new price location (x=3.5)
                            bid_put_x_smooth, bid_put_y_smooth = self.create_constrained_curve(
                                bid_put_x_coords, bid_put_y_coords,
                                curve_type='put',  # Ensures no trough at new price location
                                num_points=100
                            )

                            # Store bid put curve data for IV Peak determination
                            if len(bid_put_y_smooth) > 0:
                                bid_put_trough_y = np.min(bid_put_y_smooth)
                                bid_put_curves_data.append((current_price, bid_put_trough_y, bid_put_y_smooth, bid_put_x_smooth))

                        # Generate ask put curves for IV Walls, Overflow, Max Fear determination (independent of display toggle)
                        if current_price in put_data_by_strike and current_price in valid_put_strikes_ask:
                            ask_put_price = put_data_by_strike[current_price]['ask']
                            ask_put_offset = ask_put_price / 10
                            ask_put_starting_point = current_price - ask_put_price

                            # Calculate curve with ask prices for IV Walls, Overflow, Max Fear determination
                            # Pattern: new price (+10 formula) → trough → peak → strike price
                            ask_put_x_coords = [3.5, 3.7, 4.3, 4.5]
                            ask_put_y_coords = [
                                ask_put_starting_point,                     # New price at x=3.5
                                ask_put_starting_point - ask_put_offset,   # Trough at x=3.7
                                current_price + ask_put_offset,            # Peak at x=4.3
                                current_price                              # Strike price at x=4.5
                            ]

                            # Create curved line for ask prices (for IV Walls, Overflow, Max Fear determination)
                            # Put constraint: NO trough at new price location (x=3.5)
                            ask_put_x_smooth, ask_put_y_smooth = self.create_constrained_curve(
                                ask_put_x_coords, ask_put_y_coords,
                                curve_type='put',  # Ensures no trough at new price location
                                num_points=100
                            )

                            # Store ask put curve data for IV Walls, Overflow, Max Fear determination
                            if len(ask_put_y_smooth) > 0:
                                ask_put_trough_y = np.min(ask_put_y_smooth)
                                ask_put_curves_data.append((current_price, ask_put_trough_y, ask_put_y_smooth, ask_put_x_smooth))

                    # Move to the next price level
                    current_price += step_size

                # Perform breakthrough detection and add horizontal lines
                # CRITICAL: IV zones are calculated independently of the display price toggle
                # - IV Peaks use bid_call_curves_data and bid_put_curves_data (ALWAYS bid prices)
                # - Max Fear, IV Walls, Inner Walls, Overflow use ask_call_curves_data and ask_put_curves_data (ALWAYS ask prices)
                # - The displayed curves (call_curves_data, put_curves_data) change based on user toggle but don't affect IV zones
                self.detect_and_draw_breakthrough_lines(
                    call_curves_data, put_curves_data,
                    bid_call_curves_data, bid_put_curves_data,
                    ask_call_curves_data, ask_put_curves_data
                )

            # Add current price label at the leftmost part of the graph
            # Only if option data is enabled
            if hasattr(self, 'show_option_data') and self.show_option_data:
                current_price_label = pg.TextItem(
                    text=f"Current Price: {reference_price:.2f}",
                    color='white',
                    anchor=(0, 0.5)  # Anchor to middle-left
                )
                # Position the label at x=-1.5 (left edge) and on the current price line
                current_price_label.setPos(-1.5, reference_price)
                self.plot_widget.addItem(current_price_label)




            # Update min and max values to include level lines
            # Add level values to the list if they exist
            if 'highest_high' in locals():
                all_values.append(highest_high)
            if 'maxavg_high' in locals():
                all_values.append(maxavg_high)
            if 'high_median' in locals():
                all_values.append(high_median)
            if 'lowest_low' in locals():
                all_values.append(lowest_low)
            if 'maxavg_low' in locals():
                all_values.append(maxavg_low)
            if 'low_median' in locals():
                all_values.append(low_median)

            # Recalculate min and max with percentile values included
            min_val = min(all_values)
            max_val = max(all_values)

            # Padding was already calculated earlier

            # Set axis ranges
            self.plot_widget.setXRange(-1.5, 6)  # X-axis range from -1.5 to 6
            self.plot_widget.setYRange(min_val - padding_low, max_val + padding_high)  # y-axis with fixed 0.5% padding

            # Store the initial view state for reset functionality
            self.initial_view_state = (-1.5, 6, min_val - padding_low, max_val + padding_high)

            # Keep mouse interaction enabled for zooming
            self.plot_widget.setMouseEnabled(x=True, y=True)
            self.plot_widget.setMenuEnabled(False)  # Still disable context menu

            # Ensure event filter is installed for double-click zone creation (non-interfering)
            self.plot_widget.installEventFilter(self)

            # Add price-only crosshair to the plot with mouse tracking enabled
            self.crosshair = add_price_only_crosshair(self.plot_widget, hide_cursor=True, parent=self.parent)

            # Connect viewbox range changed signal to update crosshair when zooming
            self.plot_widget.getPlotItem().vb.sigRangeChanged.connect(self.on_view_range_changed)

            # Update status label
            occurrences = len(matching_rows)
            total_rows = len(data) if data is not None else 0

            # Get the occurrence count limit if set
            occurrence_limit = 0
            if hasattr(self.parent, 'get_occurrence_count'):
                occurrence_limit = self.parent.get_occurrence_count()

            # Check if we're using projected values
            is_using_theoretical = False
            if self.data_tab and hasattr(self.data_tab, 'calculation_mode'):
                is_using_theoretical = (self.data_tab.calculation_mode == "current_price" and
                                       'Projected High' in matching_rows.columns and
                                       'Projected Low' in matching_rows.columns)

            # Get the matching mode for display
            matching_mode_display = "Weekday Matching" if (hasattr(self.parent, 'get_matching_mode') and self.parent.get_matching_mode() == 'weekday') else "H/L Matching"

            # Add indicators to status message
            theoretical_indicator = " (Using Projected High/Low)" if is_using_theoretical else ""
            price_type_indicator = f" (Using {'Ask' if self.use_ask_prices else 'Bid'} Prices)" if self.show_option_data else ""

            # Create status message based on whether occurrence limit is set
            if occurrence_limit > 0:
                # If occurrence limit is set, don't show days to load
                status_message = f"Occurrences: {occurrences} (Limited to {occurrence_limit}) Attribute: {latest_category} - {matching_mode_display}{theoretical_indicator}{price_type_indicator}"
            else:
                # If no occurrence limit, show days to load
                status_message = f"Occurrences: {occurrences} Attribute: {latest_category} days to load: {total_rows} - {matching_mode_display}{theoretical_indicator}{price_type_indicator}"

            self.status_label.setText(status_message)

            # Clear parent's statistics box if available
            if hasattr(self.parent, 'clear_statistics_box'):
                self.parent.clear_statistics_box()

            # Update the parent's statistics box if available
            if hasattr(self.parent, 'update_statistics_box'):
                # Calculate price levels for the statistics box
                price_levels = {
                    'highest_high': highest_high if 'highest_high' in locals() else None,
                    'lowest_low': lowest_low if 'lowest_low' in locals() else None,
                    'average_high': np.mean(high_values) if len(high_values) > 0 else None,
                    'average_low': np.mean(low_values) if len(low_values) > 0 else None,
                    'lowest_high': min(high_values) if len(high_values) > 0 else None,
                    'highest_low': max(low_values) if len(low_values) > 0 else None,
                    # Include the raw high and low values for count calculations
                    'high_values': high_values,
                    'low_values': low_values
                }

                # Calculate true average (mean) of all highs
                if len(high_values) > 0:
                    # Convert to pandas Series for easier handling
                    high_series = pd.Series(high_values)

                    # Convert to numeric values, coercing errors to NaN
                    high_series = pd.to_numeric(high_series, errors='coerce')

                    # Drop any NaN values
                    high_series = high_series.dropna()

                    if len(high_series) > 0:
                        # Calculate the true average (mean) of all highs
                        true_avg_high = high_series.mean()
                        logger.info(f"Calculated true average of all highs: {true_avg_high}")

                        # Store for use in the white line labels
                        price_levels['true_avg_high'] = true_avg_high
                    else:
                        price_levels['true_avg_high'] = None
                else:
                    price_levels['true_avg_high'] = None

                # Calculate true average (mean) of all lows
                if len(low_values) > 0:
                    # Convert to pandas Series for easier handling
                    low_series = pd.Series(low_values)

                    # Convert to numeric values, coercing errors to NaN
                    low_series = pd.to_numeric(low_series, errors='coerce')

                    # Drop any NaN values
                    low_series = low_series.dropna()

                    if len(low_series) > 0:
                        # Calculate the true average (mean) of all lows
                        true_avg_low = low_series.mean()
                        logger.info(f"Calculated true average of all lows: {true_avg_low}")

                        # Store for use in the white line labels
                        price_levels['true_avg_low'] = true_avg_low
                    else:
                        price_levels['true_avg_low'] = None
                else:
                    price_levels['true_avg_low'] = None

                # Calculate median of all highs (long median)
                if len(high_values) > 0:
                    # Convert to pandas Series for easier handling
                    high_series = pd.Series(high_values)

                    # Convert to numeric values, coercing errors to NaN
                    high_series = pd.to_numeric(high_series, errors='coerce')

                    # Drop any NaN values
                    high_series = high_series.dropna()

                    if len(high_series) > 0:
                        # Sort the values
                        sorted_high_values = high_series.sort_values().values

                        # Get the middle element (for even counts, take the lower middle element)
                        # For example, with 10 highs, take the 5th high (index 4)
                        # Get the true median value
                        median_value = sorted_high_values[(len(sorted_high_values) - 1) // 2]

                        # Store the median value
                        price_levels['avg_high_lowest_high'] = median_value

                        logger.info(f"Calculated long median (median of all highs) from {len(high_values)} values: {price_levels['avg_high_lowest_high']}")
                    else:
                        price_levels['avg_high_lowest_high'] = None
                else:
                    price_levels['avg_high_lowest_high'] = None

                # Calculate median of all lows (short median)
                if len(low_values) > 0:
                    # Convert to pandas Series for easier handling
                    low_series = pd.Series(low_values)

                    # Convert to numeric values, coercing errors to NaN
                    low_series = pd.to_numeric(low_series, errors='coerce')

                    # Drop any NaN values
                    low_series = low_series.dropna()

                    if len(low_series) > 0:
                        # Sort the values
                        sorted_low_values = low_series.sort_values().values

                        # Get the middle element (for even counts, take the lower middle element)
                        # For example, with 4 lows, take the 2nd low (index 1)
                        # Get the true median value
                        median_value = sorted_low_values[(len(sorted_low_values) - 1) // 2]

                        # Store the median value
                        price_levels['avg_low_highest_low'] = median_value

                        logger.info(f"Calculated short median (median of all lows) from {len(low_values)} values: {price_levels['avg_low_highest_low']}")
                    else:
                        price_levels['avg_low_highest_low'] = None
                else:
                    price_levels['avg_low_highest_low'] = None

                # Calculate apex (median between highest high and lowest low)
                if price_levels['highest_high'] is not None and price_levels['lowest_low'] is not None:
                    price_levels['apex'] = (price_levels['highest_high'] + price_levels['lowest_low']) / 2
                    logger.info(f"Calculated apex: {price_levels['apex']}")
                else:
                    price_levels['apex'] = None

                # Calculate maxavg for highs (average of top 50% highs/highest highs) - State-of-the-art
                if len(high_values) > 0:
                    # Convert to pandas Series for robust data validation
                    high_series = pd.Series(high_values)

                    # Convert to numeric values, coercing errors to NaN
                    high_series = pd.to_numeric(high_series, errors='coerce')

                    # Drop any NaN or infinite values
                    high_series = high_series.dropna()
                    high_series = high_series[np.isfinite(high_series)]

                    if len(high_series) > 0:
                        # Sort high values in descending order for top percentile calculation
                        sorted_high_values = high_series.sort_values(ascending=False).values
                        # Take the top 50% (use ceiling to ensure at least 1 value for small datasets)
                        top_half_count = max(1, int(np.ceil(len(sorted_high_values) / 2)))
                        top_half_highs = sorted_high_values[:top_half_count]
                        # Calculate robust average
                        price_levels['maxavg_high'] = np.mean(top_half_highs)
                        logger.info(f"Calculated maxavg_high from {top_half_count}/{len(high_values)} values: {price_levels['maxavg_high']:.6f}")
                    else:
                        price_levels['maxavg_high'] = None
                        logger.warning("No valid high values after data cleaning for maxavg_high")
                else:
                    price_levels['maxavg_high'] = None

                # Calculate maxavg for lows (average of bottom 50% lowest lows) - State-of-the-art
                if len(low_values) > 0:
                    # Convert to pandas Series for robust data validation
                    low_series = pd.Series(low_values)

                    # Convert to numeric values, coercing errors to NaN
                    low_series = pd.to_numeric(low_series, errors='coerce')

                    # Drop any NaN or infinite values
                    low_series = low_series.dropna()
                    low_series = low_series[np.isfinite(low_series)]

                    if len(low_series) > 0:
                        # Sort low values in ascending order for bottom percentile calculation
                        sorted_low_values = low_series.sort_values(ascending=True).values
                        # Take the bottom 50% (lowest lows) - use ceiling to ensure at least 1 value
                        bottom_half_count = max(1, int(np.ceil(len(sorted_low_values) / 2)))
                        bottom_half_lows = sorted_low_values[:bottom_half_count]
                        # Calculate robust average
                        price_levels['maxavg_low'] = np.mean(bottom_half_lows)
                        logger.info(f"Calculated maxavg_low from {bottom_half_count}/{len(low_values)} values: {price_levels['maxavg_low']:.6f}")
                    else:
                        price_levels['maxavg_low'] = None
                        logger.warning("No valid low values after data cleaning for maxavg_low")
                else:
                    price_levels['maxavg_low'] = None

                # Calculate minavg for highs (average of bottom 50% highs/lowest highs) - State-of-the-art
                if len(high_values) > 0:
                    # Convert to pandas Series for robust data validation
                    high_series = pd.Series(high_values)

                    # Convert to numeric values, coercing errors to NaN
                    high_series = pd.to_numeric(high_series, errors='coerce')

                    # Drop any NaN or infinite values
                    high_series = high_series.dropna()
                    high_series = high_series[np.isfinite(high_series)]

                    if len(high_series) > 0:
                        # Sort high values in ascending order for bottom percentile calculation
                        sorted_high_values = high_series.sort_values(ascending=True).values
                        # Take the bottom 50% (lowest highs) - use ceiling to ensure at least 1 value
                        bottom_half_count = max(1, int(np.ceil(len(sorted_high_values) / 2)))
                        bottom_half_highs = sorted_high_values[:bottom_half_count]
                        # Calculate robust average
                        price_levels['minavg_high'] = np.mean(bottom_half_highs)
                        logger.info(f"Calculated minavg_high from {bottom_half_count}/{len(high_values)} values: {price_levels['minavg_high']:.6f}")
                    else:
                        price_levels['minavg_high'] = None
                        logger.warning("No valid high values after data cleaning for minavg_high")
                else:
                    price_levels['minavg_high'] = None

                # Calculate minavg for lows (average of top 50% of lows/highest lows) - State-of-the-art
                if len(low_values) > 0:
                    # Convert to pandas Series for robust data validation
                    low_series = pd.Series(low_values)

                    # Convert to numeric values, coercing errors to NaN
                    low_series = pd.to_numeric(low_series, errors='coerce')

                    # Drop any NaN or infinite values
                    low_series = low_series.dropna()
                    low_series = low_series[np.isfinite(low_series)]

                    if len(low_series) > 0:
                        # Sort low values in descending order for top percentile calculation
                        sorted_low_values = low_series.sort_values(ascending=False).values
                        # Take the top 50% (highest lows) - use ceiling to ensure at least 1 value
                        top_half_count = max(1, int(np.ceil(len(sorted_low_values) / 2)))
                        top_half_lows = sorted_low_values[:top_half_count]
                        # Calculate robust average
                        price_levels['minavg_low'] = np.mean(top_half_lows)
                        logger.info(f"Calculated minavg_low from {top_half_count}/{len(low_values)} values: {price_levels['minavg_low']:.6f}")
                    else:
                        price_levels['minavg_low'] = None
                        logger.warning("No valid low values after data cleaning for minavg_low")
                else:
                    price_levels['minavg_low'] = None

                # Add wall_long and wall_short values based on maxavg values
                if price_levels.get('maxavg_high') is not None:
                    price_levels['wall_long'] = price_levels['maxavg_high']
                    logger.info(f"Set wall_long to maxavg_high: {price_levels['wall_long']}")
                else:
                    price_levels['wall_long'] = None

                if price_levels.get('maxavg_low') is not None:
                    price_levels['wall_short'] = price_levels['maxavg_low']
                    logger.info(f"Set wall_short to maxavg_low: {price_levels['wall_short']}")
                else:
                    price_levels['wall_short'] = None

                # Extract theoretical price information if available
                theoretical_prices = []
                if is_using_theoretical and 'Projected High' in matching_rows.columns and 'Projected Low' in matching_rows.columns:
                    try:
                        # Get unique indices from the matching rows
                        indices = matching_rows.index.tolist()

                        # Add theoretical prices for each index
                        for idx in indices:
                            row = matching_rows.loc[idx]
                            if pd.notna(row.get('Projected High')):
                                theoretical_prices.append({
                                    'idx': idx,
                                    'type': 'high',
                                    'price': float(row['Projected High'])
                                })
                            if pd.notna(row.get('Projected Low')):
                                theoretical_prices.append({
                                    'idx': idx,
                                    'type': 'low',
                                    'price': float(row['Projected Low'])
                                })
                    except Exception as e:
                        logger.warning(f"Error extracting theoretical prices: {str(e)}")

                # Log the price levels for debugging
                logger.info(f"Calculated price levels for statistics box: {price_levels.keys()}")

                # Store theoretical prices as an attribute for the data subtab
                self.theoretical_prices = theoretical_prices
                # Also store as projected_prices for compatibility with data subtab
                self.projected_prices = theoretical_prices

                # Update the parent's statistics box
                self.parent.update_statistics_box(price_levels, latest_category, occurrences, theoretical_prices)

                # Add historical day range line if viewing historical data
                if hasattr(self.parent, 'is_viewing_historical_data') and self.parent.is_viewing_historical_data():
                    historical_high, historical_low = self.parent.get_historical_day_range()
                    if historical_high is not None and historical_low is not None:
                        try:
                            # Create white vertical line at x=2.35 from historical low to historical high
                            historical_range_line = pg.PlotDataItem(
                                x=[2.35, 2.35],
                                y=[historical_low, historical_high],
                                pen=pg.mkPen(color='white', width=3),
                                symbolPen=None,
                                symbolBrush=None,
                                symbol=None
                            )
                            self.plot_widget.addItem(historical_range_line)

                            # Add labels for the next day's high and low
                            next_day_high_label = pg.TextItem(
                                text=f"Next Day High: {historical_high:.2f}",
                                color='black',
                                fill='white',
                                anchor=(0, 0.5)  # Left-center aligned
                            )
                            next_day_high_label.setPos(2.4, historical_high)
                            self.plot_widget.addItem(next_day_high_label)

                            next_day_low_label = pg.TextItem(
                                text=f"Next Day Low: {historical_low:.2f}",
                                color='black',
                                fill='white',
                                anchor=(0, 0.5)  # Left-center aligned
                            )
                            next_day_low_label.setPos(2.4, historical_low)
                            self.plot_widget.addItem(next_day_low_label)

                            logger.info(f"Added next day range line at x=2.35 from {historical_low:.2f} to {historical_high:.2f}")
                        except Exception as e:
                            logger.warning(f"Failed to create historical day range line: {str(e)}")

                # Plot toggled statistics if available
                if hasattr(self.parent, 'get_toggled_stats_data'):
                    toggled_stats = self.parent.get_toggled_stats_data()
                    if toggled_stats:
                        self.plot_toggled_statistics(toggled_stats, reference_price)

                # Notify parent about zone changes for save button visibility
                self.notify_parent_zones_updated()

        except Exception as e:
            logger.error(f"Error generating density graph: {str(e)}", exc_info=True)
            self.status_label.setText(f"Error generating density graph: {str(e)}")
            self.plot_widget.clear()
            # Also notify parent on error to hide save button
            self.notify_parent_zones_updated()

    def notify_parent_zones_updated(self):
        """Notify parent (Volatility Statistics tab) that zones have been updated"""
        try:
            if hasattr(self.parent, 'update_save_zones_button_visibility'):
                self.parent.update_save_zones_button_visibility()
        except Exception as e:
            logger.error(f"Error notifying parent about zone updates: {str(e)}", exc_info=True)

    def get_data_tab(self):
        """Get the Data tab reference."""
        return self.data_tab

    # Options data is now fetched using the unified system that respects Schwab API selection

    def get_options_data(self):
        """
        Get options data using Schwab API when available and selected, otherwise fallback to Yahoo Finance.

        Returns:
            tuple: (calls_df, puts_df, current_price) or (None, None, None) if not available
        """
        try:
            # Get reference price to determine which ticker to use
            reference_price_info = self.get_reference_price()
            if reference_price_info is None:
                logger.warning("Could not determine reference price for options data")
                return None, None, None

            reference_price, _, _ = reference_price_info

            # Try to get the market odds tab to get the ticker
            market_odds_tab = None
            ticker = None

            # First check if data_tab has a reference to market_odds_tab
            if self.data_tab is not None and hasattr(self.data_tab, 'market_odds_tab'):
                market_odds_tab = self.data_tab.market_odds_tab

            # If not found, try to get it from the parent hierarchy
            if market_odds_tab is None and hasattr(self, 'parent') and self.parent is not None:
                parent = self.parent
                while parent is not None:
                    if hasattr(parent, 'market_odds_tab'):
                        market_odds_tab = parent.market_odds_tab
                        break
                    if hasattr(parent, 'parent'):
                        parent = parent.parent()
                    else:
                        break

            # Try to get ticker from market_odds_tab
            if market_odds_tab is not None and hasattr(market_odds_tab, 'ticker'):
                ticker = market_odds_tab.ticker

            # If we couldn't get ticker, use SPY as default
            if ticker is None or ticker == "":
                ticker = "SPY"
                logger.info(f"No ticker found, using default: {ticker}")
            else:
                logger.info(f"Using ticker: {ticker}")

            # Format ticker for indices (remove ^ prefix for Schwab API compatibility)
            if ticker.upper() == "SPX":
                ticker = "SPX"  # Schwab uses SPX, not ^SPX
            elif ticker.upper() == "NDX":
                ticker = "NDX"  # Schwab uses NDX, not ^NDX
            elif ticker.upper() == "VIX":
                ticker = "VIX"  # Schwab uses VIX, not ^VIX
            elif ticker.upper() == "DJI":
                ticker = "DJI"  # Schwab uses DJI, not ^DJI
            elif ticker.upper() == "RUT":
                ticker = "RUT"  # Schwab uses RUT, not ^RUT

            # Get current price using the appropriate data source
            current_price = self._get_current_price_for_options(ticker, reference_price)

            # Get available expiration dates and fetch options data
            try:
                # Import the unified options fetching function
                from options_analysis import fetch_options_for_date

                # Get available expiration dates (use Yahoo Finance for this as it's more reliable)
                import yfinance as yf
                yahoo_ticker = f"^{ticker}" if ticker.upper() in ["SPX", "NDX", "VIX", "DJI", "RUT"] else ticker
                stock = yf.Ticker(yahoo_ticker)
                expiry_dates = stock.options

                if not expiry_dates:
                    logger.warning(f"No options data available for {ticker}")
                    return None, None, None

                # Update available expiry dates if they've changed
                if list(expiry_dates) != self.available_expiry_dates:
                    self.fetch_expiry_dates(ticker)

                # Use the selected expiration date, or the nearest one if none selected
                if self.selected_expiry_date and self.selected_expiry_date in expiry_dates:
                    expiry_date = self.selected_expiry_date
                    logger.info(f"Using selected expiration date: {expiry_date} for ticker: {ticker}")
                else:
                    expiry_date = expiry_dates[0]
                    self.selected_expiry_date = expiry_date
                    logger.info(f"Using nearest expiration date: {expiry_date} for ticker: {ticker}")

                    # Update the selector if it's not already set
                    if self.expiry_selector.currentText() != expiry_date:
                        index = self.expiry_selector.findText(expiry_date)
                        if index >= 0:
                            self.expiry_selector.setCurrentIndex(index)

                # Check if synthetic pricing is enabled
                if self.use_synthetic_pricing and self.synthetic_engine is not None:
                    logger.info(f"Generating synthetic options data for {ticker} with expiry date: {expiry_date}")
                    calls_df, puts_df, _ = self.get_synthetic_options_data(current_price, expiry_date)
                else:
                    # Fetch options chain using the unified function (respects Schwab API selection)
                    logger.info(f"Fetching real options data for {ticker} with expiry date: {expiry_date}")
                    calls_df, puts_df = fetch_options_for_date(ticker, expiry_date, current_price)

                if calls_df is not None and not calls_df.empty and puts_df is not None and not puts_df.empty:
                    # Determine which data source was used
                    if self.use_synthetic_pricing and self.synthetic_engine is not None:
                        data_source = "Synthetic Options Engine"
                    else:
                        data_source = self._get_data_source_used()
                    logger.info(f"Retrieved options data from {data_source}: {len(calls_df)} calls, {len(puts_df)} puts for expiry {expiry_date}")

                    # Log sample contract symbols to verify expiry date
                    if 'contractSymbol' in calls_df.columns and len(calls_df) > 0:
                        sample_contract = calls_df.iloc[0]['contractSymbol']
                        logger.info(f"Sample contract symbol: {sample_contract}")

                    return calls_df, puts_df, current_price
                else:
                    logger.warning(f"Empty options data returned for {ticker} with expiry {expiry_date}")

            except Exception as e:
                logger.error(f"Error fetching options data: {str(e)}")

        except Exception as e:
            logger.error(f"Error in get_options_data: {str(e)}")

        logger.warning("No options data available")
        return None, None, None

    def _get_current_price_for_options(self, ticker, fallback_price):
        """
        Get current price using the appropriate data source.

        Args:
            ticker: Stock symbol
            fallback_price: Fallback price if fetching fails

        Returns:
            float: Current price
        """
        try:

            # Fallback to Yahoo Finance
            import yfinance as yf
            yahoo_ticker = f"^{ticker}" if ticker.upper() in ["SPX", "NDX", "VIX", "DJI", "RUT"] else ticker
            stock = yf.Ticker(yahoo_ticker)

            try:
                price = stock.info.get("regularMarketPrice")
                if price is None:
                    price = stock.fast_info.get("lastPrice")
                if price is not None:
                    current_price = round(float(price), 2)
                    logger.info(f"Retrieved current price from Yahoo Finance: {current_price}")
                    return current_price
            except Exception as e:
                logger.warning(f"Error fetching current price from Yahoo Finance: {str(e)}")

        except Exception as e:
            logger.error(f"Error in _get_current_price_for_options: {str(e)}")

        # Return fallback price if all else fails
        logger.info(f"Using fallback price: {fallback_price}")
        return fallback_price

    def _get_data_source_used(self):
        """Get a description of which data source was used"""
        return "Yahoo Finance"

    def zoom_in(self):
        """Zoom in on the plot by a factor of 0.8 (reducing the visible range by 20%)."""
        # Get the current view range
        x_min, x_max = self.plot_widget.viewRange()[0]
        y_min, y_max = self.plot_widget.viewRange()[1]

        # Calculate the center points
        x_center = (x_min + x_max) / 2
        y_center = (y_min + y_max) / 2

        # Calculate the new ranges (80% of current range)
        x_range = (x_max - x_min) * 0.8
        y_range = (y_max - y_min) * 0.8

        # Set the new ranges centered on the current center
        self.plot_widget.setXRange(x_center - x_range/2, x_center + x_range/2)
        self.plot_widget.setYRange(y_center - y_range/2, y_center + y_range/2)

        # Update status
        self.status_label.setText(f"Density Graph - Zoomed in")

    def zoom_out(self):
        """Zoom out on the plot by a factor of 1.25 (increasing the visible range by 25%)."""
        # Get the current view range
        x_min, x_max = self.plot_widget.viewRange()[0]
        y_min, y_max = self.plot_widget.viewRange()[1]

        # Calculate the center points
        x_center = (x_min + x_max) / 2
        y_center = (y_min + y_max) / 2

        # Calculate the new ranges (125% of current range)
        x_range = (x_max - x_min) * 1.25
        y_range = (y_max - y_min) * 1.25

        # Set the new ranges centered on the current center
        self.plot_widget.setXRange(x_center - x_range/2, x_center + x_range/2)
        self.plot_widget.setYRange(y_center - y_range/2, y_center + y_range/2)

        # Update status
        self.status_label.setText(f"Density Graph - Zoomed out")

    def reset_zoom(self):
        """Reset the zoom to the initial view state."""
        if self.initial_view_state:
            x_min, x_max, y_min, y_max = self.initial_view_state
            self.plot_widget.setXRange(x_min, x_max)
            self.plot_widget.setYRange(y_min, y_max)

            # Update status
            self.status_label.setText(f"Density Graph - Zoom reset")
        else:
            # If initial view state is not set, use default values
            self.plot_widget.setXRange(-1.5, 6)  # X-axis range from -1.5 to 6
            self.plot_widget.setYRange(-10, 10)

            # Update status
            self.status_label.setText(f"Density Graph - Zoom reset (default view)")

    def on_density_toggle(self, checked):
        """
        Handle toggling of the density visualization.

        Args:
            checked: Whether the checkbox is checked
        """
        # Store the density visibility state
        self.show_density = checked

        # Update the status label
        if checked:
            self.status_label.setText("Density Graph - Density visualization enabled")
        else:
            self.status_label.setText("Density Graph - Density visualization disabled")

        # Regenerate the graph to apply the change
        self.generate_density_graph()

    def on_option_data_toggle(self, checked):
        """
        Handle toggling of the option data (strike, put IV, call IV) visualization.

        Args:
            checked: Whether the checkbox is checked
        """
        # Store the option data visibility state
        self.show_option_data = checked

        # Update the status label
        if checked:
            self.status_label.setText("Density Graph - Option data enabled")
        else:
            self.status_label.setText("Density Graph - Option data disabled")

        # Regenerate the graph to apply the change
        self.generate_density_graph()

    def on_volume_profile_toggle(self, checked):
        """
        Handle toggling of the volume profile visualization.
        When enabled, automatically turns off options data and options zones for cleaner view.

        Args:
            checked: Whether the checkbox is checked
        """
        # Store the volume profile visibility state
        self.show_volume_profile = checked

        # When volume profile is enabled, automatically turn off options data and options zones
        if checked:
            # Turn off options data
            self.show_option_data = False
            self.show_option_data_btn.setChecked(False)

            # Turn off options zones (Option Zones)
            self.show_option_zones = False
            self.show_option_zones_btn.setChecked(False)

            self.status_label.setText("Density Graph - Volume profile enabled (Options data and zones disabled)")
        else:
            self.status_label.setText("Density Graph - Volume profile disabled")

        # Regenerate the graph to apply the change
        self.generate_density_graph()

    def on_option_zones_toggle(self, checked):
        """
        Handle toggling of Option Zones (IV Peak, IV Wall, IV Inner Wall, Max Fear, IV Overflow).

        Args:
            checked: Whether the checkbox is checked
        """
        self.show_option_zones = checked
        if checked:
            self.status_label.setText("Density Graph - Option Zones enabled")
        else:
            self.status_label.setText("Density Graph - Option Zones disabled")

        # Regenerate the graph to apply the change
        self.generate_density_graph()

    def on_price_type_toggle(self, checked):
        """
        Handle toggling between using bid prices and ask prices for options data.

        Args:
            checked: Whether the radio button is checked
        """
        # Only respond when a button is being checked (not unchecked)
        if not checked:
            return

        # Determine which price type is selected based on which button is checked
        self.use_ask_prices = self.use_ask_prices_btn.isChecked()

        # Update the status label
        if self.use_ask_prices:
            self.status_label.setText("Density Graph - Using ask prices for options")
        else:
            self.status_label.setText("Density Graph - Using bid prices for options")

        # Regenerate the graph to apply the change
        self.generate_density_graph()

    def on_option_pricing_toggle(self, checked):
        """
        Handle toggling between outright option pricing and synthetic option pricing.

        Args:
            checked: Whether the radio button is checked
        """
        # Only respond when a button is being checked (not unchecked)
        if not checked:
            return

        # Determine which option pricing type is selected based on which button is checked
        self.use_synthetic_pricing = self.synthetic_option_pricing_btn.isChecked()

        # Update the status label and initialize synthetic engine if needed
        if self.use_synthetic_pricing:
            if self.synthetic_engine is None and SYNTHETIC_OPTIONS_AVAILABLE:
                try:
                    # Get current spot price from data if available
                    current_price = 100.0  # Default
                    if self.data_tab and hasattr(self.data_tab, 'get_current_price'):
                        try:
                            current_price = float(self.data_tab.get_current_price())
                        except:
                            pass

                    self.synthetic_engine = SyntheticOptionsEngine(
                        spot_price=current_price,
                        risk_free_rate=0.05,
                        dividend_yield=0.0
                    )
                    logger.info(f"Initialized synthetic options engine with spot price: {current_price}")
                except Exception as e:
                    logger.error(f"Failed to initialize synthetic options engine: {e}")
                    self.synthetic_engine = None

            if self.synthetic_engine is not None:
                self.status_label.setText("Density Graph - Using synthetic option pricing")
            else:
                self.status_label.setText("Density Graph - Synthetic pricing unavailable, using outright")
                # Fall back to outright pricing
                self.use_synthetic_pricing = False
                self.real_option_pricing_btn.setChecked(True)
        else:
            self.status_label.setText("Density Graph - Using outright option pricing")

        # Regenerate the graph to apply the change
        self.generate_density_graph()

    def get_synthetic_options_data(self, current_price: float, expiry_date: str) -> tuple:
        """
        Generate synthetic options data using the synthetic options engine.

        Args:
            current_price: Current underlying price
            expiry_date: Expiration date string

        Returns:
            tuple: (calls_df, puts_df, current_price) in same format as real options data
        """
        if self.synthetic_engine is None:
            logger.warning("Synthetic options engine not available")
            return None, None, None

        try:
            # Update synthetic engine with current price
            self.synthetic_engine.update_spot_price(current_price)

            # Calculate time to expiry (simplified - assume 30 days for now)
            # In a real implementation, you'd parse the expiry_date string
            time_to_expiry = 30 / 365.0  # 30 days in years

            # Generate strike range around current price
            strike_range = np.arange(
                current_price * 0.8,  # 20% OTM puts
                current_price * 1.2,  # 20% OTM calls
                current_price * 0.05  # 5% strike intervals
            )

            calls_data = []
            puts_data = []

            for strike in strike_range:
                # Get call quote
                call_quote = self.synthetic_engine.get_option_quote(strike, time_to_expiry, is_call=True)
                calls_data.append({
                    'strike': strike,
                    'bid': call_quote['bid'],
                    'ask': call_quote['ask'],
                    'mid': call_quote['mid'],
                    'impliedVolatility': call_quote['implied_vol'],
                    'delta': call_quote['delta'],
                    'gamma': call_quote['gamma'],
                    'theta': call_quote['theta'],
                    'vega': call_quote['vega'],
                    'rho': call_quote['rho'],
                    'volume': 100,  # Synthetic volume
                    'openInterest': 500,  # Synthetic open interest
                    'lastPrice': call_quote['mid'],
                    'change': 0.0,
                    'percentChange': 0.0
                })

                # Get put quote
                put_quote = self.synthetic_engine.get_option_quote(strike, time_to_expiry, is_call=False)
                puts_data.append({
                    'strike': strike,
                    'bid': put_quote['bid'],
                    'ask': put_quote['ask'],
                    'mid': put_quote['mid'],
                    'impliedVolatility': put_quote['implied_vol'],
                    'delta': put_quote['delta'],
                    'gamma': put_quote['gamma'],
                    'theta': put_quote['theta'],
                    'vega': put_quote['vega'],
                    'rho': put_quote['rho'],
                    'volume': 100,  # Synthetic volume
                    'openInterest': 500,  # Synthetic open interest
                    'lastPrice': put_quote['mid'],
                    'change': 0.0,
                    'percentChange': 0.0
                })

            # Convert to DataFrames
            calls_df = pd.DataFrame(calls_data)
            puts_df = pd.DataFrame(puts_data)

            logger.info(f"Generated synthetic options data: {len(calls_df)} calls, {len(puts_df)} puts")
            return calls_df, puts_df, current_price

        except Exception as e:
            logger.error(f"Error generating synthetic options data: {e}")
            return None, None, None

    def on_expiry_changed(self, index):
        """
        Handle change in selected expiry date.

        Args:
            index: Index of the selected expiry date
        """
        if index >= 0 and self.expiry_selector.count() > 0:
            self.selected_expiry_date = self.expiry_selector.currentText()
            logger.info(f"Expiry date changed to: {self.selected_expiry_date}")

            # Update status label
            self.status_label.setText(f"Density Graph - Expiry changed to {self.selected_expiry_date}")

            # Regenerate the graph with the new expiry date
            self.generate_density_graph()

    def fetch_expiry_dates(self, ticker):
        """
        Fetch available expiration dates for the given ticker.

        Args:
            ticker: Stock symbol to fetch expiry dates for
        """
        try:
            # Import yfinance for fetching expiry dates
            import yfinance as yf

            # Format ticker for Yahoo Finance
            yahoo_ticker = f"^{ticker}" if ticker.upper() in ["SPX", "NDX", "VIX", "DJI", "RUT"] else ticker
            stock = yf.Ticker(yahoo_ticker)

            # Get available expiry dates
            expiry_dates = stock.options

            if expiry_dates:
                self.available_expiry_dates = list(expiry_dates)

                # Update the expiry selector
                self.expiry_selector.clear()
                self.expiry_selector.addItems(self.available_expiry_dates)

                # Set the first expiry date as selected
                if self.available_expiry_dates:
                    self.selected_expiry_date = self.available_expiry_dates[0]
                    logger.info(f"Fetched {len(self.available_expiry_dates)} expiry dates for {ticker}")
                    logger.info(f"Selected expiry date: {self.selected_expiry_date}")
                else:
                    self.selected_expiry_date = None
                    logger.warning(f"No expiry dates available for {ticker}")
            else:
                self.available_expiry_dates = []
                self.selected_expiry_date = None
                self.expiry_selector.clear()
                logger.warning(f"No options data available for {ticker}")

        except Exception as e:
            logger.error(f"Error fetching expiry dates for {ticker}: {str(e)}")
            self.available_expiry_dates = []
            self.selected_expiry_date = None
            self.expiry_selector.clear()

    def get_selected_expiry_date(self):
        """
        Get the currently selected expiry date.

        Returns:
            str: Selected expiry date, or None if none selected
        """
        return self.selected_expiry_date

    def create_smooth_curve(self, x_start, x_end, y_start, y_end, num_points=50, method='cubic_spline', smoothness=0.5):
        """
        Create a smooth curve between two points using various interpolation methods.

        Args:
            x_start: Starting x-coordinate
            x_end: Ending x-coordinate
            y_start: Starting y-coordinate
            y_end: Ending y-coordinate
            num_points: Number of points to generate for the curve
            method: Interpolation method ('cubic_spline', 'b_spline', or 'sinusoidal')
            smoothness: Controls the smoothness of the curve (0.0 to 1.0)
                        Higher values create more pronounced curves

        Returns:
            tuple: (x_points, y_points) arrays for the smooth curve
        """
        # Generate x points
        x_points = np.linspace(x_start, x_end, num=num_points)

        if method == 'sinusoidal':
            # Original sinusoidal method
            y_diff = y_end - y_start
            y_points = y_start + y_diff * (1 - np.cos(np.pi * (x_points - x_start) / (x_end - x_start))) / 2

        elif method == 'cubic_spline':
            # Create control points for cubic spline
            # Add intermediate control points to shape the curve
            x_range = x_end - x_start
            y_range = y_end - y_start

            # Calculate control points - we use 4 points for cubic spline
            x_controls = [x_start, x_start + x_range * 0.3, x_start + x_range * 0.7, x_end]

            # Adjust the height of control points based on smoothness
            # Higher smoothness creates more pronounced curves
            curve_height = y_range * smoothness

            # Create control points with a natural curve shape
            if y_end > y_start:  # Rising curve
                y_controls = [
                    y_start,
                    y_start + curve_height * 0.5,
                    y_end - curve_height * 0.5,
                    y_end
                ]
            else:  # Falling curve
                y_controls = [
                    y_start,
                    y_start - curve_height * 0.5,
                    y_end + curve_height * 0.5,
                    y_end
                ]

            # Create cubic spline
            cs = CubicSpline(x_controls, y_controls)
            y_points = cs(x_points)

        elif method == 'b_spline':
            # B-spline interpolation
            # Create more control points for B-spline
            num_controls = 5
            x_controls = np.linspace(x_start, x_end, num_controls)

            # Calculate intermediate y values with some randomness for natural look
            y_controls = np.zeros(num_controls)
            y_controls[0] = y_start
            y_controls[-1] = y_end

            # Create intermediate control points with natural variation
            for i in range(1, num_controls-1):
                t = i / (num_controls - 1)
                # Linear interpolation with controlled randomness
                base_y = y_start + (y_end - y_start) * t
                # Add controlled variation based on smoothness
                variation = (y_end - y_start) * smoothness * 0.3 * (0.5 - np.random.random())
                y_controls[i] = base_y + variation

            # Create B-spline
            # k=3 gives a cubic B-spline
            tck = splrep(x_controls, y_controls, k=3)
            y_points = splev(x_points, tck)

        else:
            # Default to cubic spline if method not recognized
            cs = CubicSpline([x_start, x_end], [y_start, y_end])
            y_points = cs(x_points)

        return x_points, y_points

    def create_constrained_curve(self, x_points, y_points, curve_type='call', num_points=100):
        """
        Create a constrained B-spline curve that follows the correct patterns for call and put options.

        Args:
            x_points: Array of x coordinates [4 points]
            y_points: Array of y coordinates [4 points]
            curve_type: 'call' or 'put' to apply appropriate constraints
            num_points: Number of points to generate for the output curve

        Returns:
            tuple: (x_smooth, y_smooth) arrays for the constrained B-spline curve

        Uses cubic B-splines (k=3) for smooth, professional curves.
        B-splines specifically optimized for:
        - Call lines: Smooth peaks (NO peak at new price location)
        - Put lines: Smooth troughs (NO trough at new price location)
        """
        try:
            if len(x_points) != 4 or len(y_points) != 4:
                # Fallback to straight line if not 4 points
                return np.array(x_points), np.array(y_points)

            # Convert to numpy arrays
            x_points = np.array(x_points)
            y_points = np.array(y_points)

            if curve_type == 'call':
                # Call pattern: strike price → trough → peak → new price
                # B-spline optimization: Create smooth, natural peaks
                # Constraint: NO peak at new price location (x_points[3])
                # The actual peak should be at x_points[2], not at the end

                # Create segments with controlled curvature
                x_smooth = np.linspace(x_points[0], x_points[3], num_points)

                # Use B-spline interpolation optimized for smooth peaks
                from scipy.interpolate import splrep, splev

                # Cubic B-spline (k=3) creates smooth, professional peaks
                # s=0 ensures exact interpolation through control points
                tck = splrep(x_points, y_points, k=3, s=0)
                y_smooth = splev(x_smooth, tck)

                # Minimal constraint: only prevent extreme peaks at new price location
                # Let B-spline create natural smooth peaks at the designated location
                # Only intervene if there's a clear violation at the new price point
                final_point_index = -1  # Last point (new price location)
                if len(y_smooth) > 1:
                    # Check if the final point is higher than it should be
                    if y_smooth[final_point_index] > y_points[3] * 1.01:  # 1% tolerance
                        # Gently adjust only the final point to maintain B-spline smoothness
                        y_smooth[final_point_index] = y_points[3]

            elif curve_type == 'put':
                # Put pattern: new price → trough → peak → strike price
                # B-spline optimization: Create smooth, natural troughs
                # Constraint: NO trough at new price location (x_points[0])
                # The actual trough should be at x_points[1], not at the start

                # Create segments with controlled curvature
                x_smooth = np.linspace(x_points[0], x_points[3], num_points)

                # Use B-spline interpolation optimized for smooth troughs
                from scipy.interpolate import splrep, splev

                # Cubic B-spline (k=3) creates smooth, professional troughs
                # s=0 ensures exact interpolation through control points
                tck = splrep(x_points, y_points, k=3, s=0)
                y_smooth = splev(x_smooth, tck)

                # Minimal constraint: only prevent extreme troughs at new price location
                # Let B-spline create natural smooth troughs at the designated location
                # Only intervene if there's a clear violation at the new price point
                first_point_index = 0  # First point (new price location)
                if len(y_smooth) > 1:
                    # Check if the first point is lower than it should be
                    if y_smooth[first_point_index] < y_points[0] * 0.99:  # 1% tolerance
                        # Gently adjust only the first point to maintain B-spline smoothness
                        y_smooth[first_point_index] = y_points[0]

            else:
                # Unknown curve type, fallback to straight line
                return np.array(x_points), np.array(y_points)

            return x_smooth, y_smooth

        except Exception as e:
            logger.warning(f"Error creating constrained curve: {str(e)}")
            # Fallback to straight line connection
            return np.array(x_points), np.array(y_points)

    def create_continuous_spline(self, x_points, y_points, num_output_points=500, smoothness=0.5):
        """
        Create a continuous spline through multiple points with professional-grade rendering.

        Args:
            x_points: Array of x coordinates
            y_points: Array of y coordinates
            num_output_points: Number of points to generate for the output curve
            smoothness: Controls the smoothness of the curve (0.0 to 1.0)

        Returns:
            tuple: (x_out, y_out) arrays for the smooth curve
        """
        if len(x_points) < 2 or len(y_points) < 2:
            return np.array([]), np.array([])

        if len(x_points) != len(y_points):
            return np.array([]), np.array([])

        try:
            # Sort points by x value to ensure proper ordering
            sorted_indices = np.argsort(x_points)
            x_sorted = np.array(x_points)[sorted_indices]
            y_sorted = np.array(y_points)[sorted_indices]

            # Add intermediate points to simulate IV at micro steps between strikes
            # This creates a more continuous, natural flow like professional systems
            x_enhanced = []
            y_enhanced = []

            # For each pair of consecutive points, add micro-step points in between
            for i in range(len(x_sorted) - 1):
                # Add the current point
                x_enhanced.append(x_sorted[i])
                y_enhanced.append(y_sorted[i])

                # Calculate micro steps between this point and the next
                x_start = x_sorted[i]
                x_end = x_sorted[i + 1]
                y_start = y_sorted[i]
                y_end = y_sorted[i + 1]

                # Add micro steps (every 0.05 increment as suggested)
                x_step = 0.05
                x_current = x_start + x_step

                while x_current < x_end:
                    # Calculate y value using local cubic interpolation
                    # This simulates how IV would behave between strikes
                    t = (x_current - x_start) / (x_end - x_start)

                    # Use cubic Hermite interpolation for natural IV simulation
                    # h00, h10, h01, h11 are the Hermite basis functions
                    h00 = 2*t**3 - 3*t**2 + 1
                    h10 = t**3 - 2*t**2 + t
                    h01 = -2*t**3 + 3*t**2
                    h11 = t**3 - t**2

                    # Estimate tangents for natural curve shape
                    # Use finite differences for tangent estimation
                    m0 = 0
                    m1 = 0

                    # If we have points before and after, use them for better tangent estimation
                    if i > 0:
                        m0 = (y_end - y_sorted[i-1]) / (x_end - x_sorted[i-1]) * (x_end - x_start) * 0.5
                    else:
                        m0 = (y_end - y_start) / (x_end - x_start) * (x_end - x_start) * 0.5

                    if i < len(x_sorted) - 2:
                        m1 = (y_sorted[i+2] - y_start) / (x_sorted[i+2] - x_start) * (x_end - x_start) * 0.5
                    else:
                        m1 = (y_end - y_start) / (x_end - x_start) * (x_end - x_start) * 0.5

                    # Calculate interpolated y value
                    y_interp = h00 * y_start + h10 * m0 + h01 * y_end + h11 * m1

                    # Add the micro step point
                    x_enhanced.append(x_current)
                    y_enhanced.append(y_interp)

                    # Move to next micro step
                    x_current += x_step

            # Add the last point
            x_enhanced.append(x_sorted[-1])
            y_enhanced.append(y_sorted[-1])

            # Convert to numpy arrays
            x_enhanced = np.array(x_enhanced)
            y_enhanced = np.array(y_enhanced)

            # For B-spline, we need at least k+1 points for a degree k spline
            # With our enhanced points, we should always have enough points now
            if len(x_enhanced) >= 4:
                # Use B-spline for professional-grade smoothness
                # The smoothness parameter controls how closely the curve follows the points

                # Calculate appropriate smoothness factor
                # For professional rendering, we want a balance between smoothness and accuracy
                # Lower s values make the curve follow points more closely
                s = (1.0 - smoothness) * len(x_enhanced) * 0.1

                # Create B-spline representation with cubic splines (k=3)
                tck = splrep(x_enhanced, y_enhanced, k=3, s=s)

                # Generate output points with sub-pixel resolution
                # Using more points creates smoother rendering with sub-pixel precision
                x_out = np.linspace(x_enhanced[0], x_enhanced[-1], num_output_points)
                y_out = splev(x_out, tck)
            else:
                # Use cubic spline for fewer points
                cs = CubicSpline(x_enhanced, y_enhanced, bc_type='natural')
                x_out = np.linspace(x_enhanced[0], x_enhanced[-1], num_output_points)
                y_out = cs(x_out)

            return x_out, y_out

        except Exception as e:
            logger.warning(f"Failed to create continuous spline: {str(e)}")
            return np.array([]), np.array([])

    def on_view_range_changed(self, *_):
        """
        Handle view range changes when zooming.

        Args:
            *_: Arguments passed by the signal (not used directly)
        """
        # This method is called when the view range changes due to zooming
        # We can use it to update any elements that need to be repositioned

        # If we have a crosshair, make sure it's still visible and properly positioned
        if hasattr(self, 'crosshair') and self.crosshair is not None:
            # The crosshair will automatically update on mouse movement
            # But we can force an update if needed by triggering a mouse move event
            pass  # The crosshair will update automatically on next mouse movement

        # No longer updating status label with zoom information as requested

    def on_calculation_mode_changed(self, checked):
        """
        Handle calculation mode change in the data tab.

        Args:
            checked: Whether the button is checked
        """
        if checked:  # Only respond to the button that was checked (not the ones unchecked)
            logger.info("Calculation mode changed in data tab, refreshing density graph")
            # Regenerate the density graph with the new calculation mode
            self.generate_density_graph()

    def plot_toggled_statistics(self, toggled_stats, _):
        """Plot toggled statistics on the graph

        Args:
            toggled_stats (list): List of dictionaries with statistics data
            reference_price (float): Current reference price
        """
        try:
            if not toggled_stats:
                return

            logger.info(f"Plotting {len(toggled_stats)} toggled statistics")

            # Define colors for different types of stats
            bear_color = self.chart_colors['bearish']
            bull_color = self.chart_colors['bullish']
            long_color = self.chart_colors.get('long', '#00FF00')  # Green for long theoretical
            short_color = self.chart_colors.get('short', '#FF00FF')  # Magenta for short theoretical

            # Plot each toggled statistic
            for stat in toggled_stats:
                if 'price' not in stat or 'type' not in stat:
                    logger.warning(f"Skipping stat without price or type: {stat}")
                    continue

                price = stat['price']
                stat_type = stat['type']

                # Determine color and label based on type
                if stat_type == 'bear':
                    color = bear_color
                    label_prefix = "Bear"
                elif stat_type == 'bull':
                    color = bull_color
                    label_prefix = "Bull"
                elif stat_type == 'long':
                    color = long_color
                    label_prefix = "Long"
                elif stat_type == 'short':
                    color = short_color
                    label_prefix = "Short"
                else:
                    logger.warning(f"Unknown stat type: {stat_type}")
                    continue

                # Create a horizontal line at the price level with win rate if available
                winrate_str = stat.get('winrate_str', '')
                label_text = f"{label_prefix}: {price:.2f} ({winrate_str})" if winrate_str else f"{label_prefix}: {price:.2f}"
                line = pg.InfiniteLine(
                    pos=price,
                    angle=0,  # Horizontal line
                    pen=pg.mkPen(color=color, width=2, style=QtCore.Qt.PenStyle.DashLine),  # Dashed line
                    label=label_text,
                    labelOpts={
                        'position': 0.95,  # Position near the right end
                        'color': color,
                        'movable': False,
                        'fill': (0, 0, 0, 0)  # Transparent background
                    }
                )
                self.plot_widget.addItem(line)

            logger.info("Successfully plotted toggled statistics")
        except Exception as e:
            logger.error(f"Error plotting toggled statistics: {str(e)}", exc_info=True)

    def validate_call_bid_ask_ordering(self, call_data_by_strike, use_ask_prices):
        """
        Return all call strikes as valid (bid/ask ordering validation removed).

        Args:
            call_data_by_strike: Dictionary of call data by strike price
            use_ask_prices: Boolean indicating whether to use ask prices (True) or bid prices (False)

        Returns:
            set: Set of all strike prices (no filtering applied)
        """
        if not call_data_by_strike:
            return set()

        try:
            # Get the price type we're using
            price_type = 'ask' if use_ask_prices else 'bid'

            # Return all strikes that have valid price data (no ordering validation)
            valid_strikes = set()
            for strike, data in call_data_by_strike.items():
                price = data.get(price_type, 0)
                if price is not None and price >= 0:  # Include zero prices
                    valid_strikes.add(strike)

            logger.info(f"Call {price_type} validation: {len(valid_strikes)} out of {len(call_data_by_strike)} strikes returned (no ordering filter applied)")
            return valid_strikes

        except Exception as e:
            logger.error(f"Error validating call bid/ask ordering: {str(e)}", exc_info=True)
            return set()

    def validate_put_bid_ask_ordering(self, put_data_by_strike, use_ask_prices):
        """
        Return all put strikes as valid (bid/ask ordering validation removed).

        Args:
            put_data_by_strike: Dictionary of put data by strike price
            use_ask_prices: Boolean indicating whether to use ask prices (True) or bid prices (False)

        Returns:
            set: Set of all strike prices (no filtering applied)
        """
        if not put_data_by_strike:
            return set()

        try:
            # Get the price type we're using
            price_type = 'ask' if use_ask_prices else 'bid'

            # Return all strikes that have valid price data (no ordering validation)
            valid_strikes = set()
            for strike, data in put_data_by_strike.items():
                price = data.get(price_type, 0)
                if price is not None and price >= 0:  # Include zero prices
                    valid_strikes.add(strike)

            logger.info(f"Put {price_type} validation: {len(valid_strikes)} out of {len(put_data_by_strike)} strikes returned (no ordering filter applied)")
            return valid_strikes

        except Exception as e:
            logger.error(f"Error validating put bid/ask ordering: {str(e)}", exc_info=True)
            return set()

    def add_intersection_dots(self, call_curves_data, put_curves_data):
        """
        Add dots to mark intersections between sine wave curves.

        Args:
            call_curves_data: List of (strike, peak_y, curve_y_values, x_values) for call curves
            put_curves_data: List of (strike, trough_y, curve_y_values, x_values) for put curves
        """
        try:
            # Find intersections between call curves
            for i, (strike1, _, curve1_y, curve1_x) in enumerate(call_curves_data):
                for j, (strike2, _, curve2_y, curve2_x) in enumerate(call_curves_data):
                    if i >= j:  # Avoid duplicate comparisons and self-comparison
                        continue

                    # Find intersection point
                    intersection_point = self.find_detailed_curve_intersection(curve1_x, curve1_y, curve2_x, curve2_y)

                    if intersection_point is not None:
                        intersection_x, intersection_y = intersection_point

                        # Add a white dot at the intersection
                        dot = pg.ScatterPlotItem(
                            x=[intersection_x],
                            y=[intersection_y],
                            pen=pg.mkPen(color='white', width=2),
                            brush=pg.mkBrush(color='white'),
                            size=6,
                            symbol='o'
                        )
                        self.plot_widget.addItem(dot)
                        logger.debug(f"Added intersection dot at ({intersection_x:.2f}, {intersection_y:.2f}) between call strikes {strike1:.2f} and {strike2:.2f}")

            # Find intersections between put curves
            for i, (strike1, _, curve1_y, curve1_x) in enumerate(put_curves_data):
                for j, (strike2, _, curve2_y, curve2_x) in enumerate(put_curves_data):
                    if i >= j:  # Avoid duplicate comparisons and self-comparison
                        continue

                    # Find intersection point
                    intersection_point = self.find_detailed_curve_intersection(curve1_x, curve1_y, curve2_x, curve2_y)

                    if intersection_point is not None:
                        intersection_x, intersection_y = intersection_point

                        # Add a white dot at the intersection
                        dot = pg.ScatterPlotItem(
                            x=[intersection_x],
                            y=[intersection_y],
                            pen=pg.mkPen(color='white', width=2),
                            brush=pg.mkBrush(color='white'),
                            size=6,
                            symbol='o'
                        )
                        self.plot_widget.addItem(dot)
                        logger.debug(f"Added intersection dot at ({intersection_x:.2f}, {intersection_y:.2f}) between put strikes {strike1:.2f} and {strike2:.2f}")

            # Find intersections between call and put curves
            for i, (call_strike, _, call_curve_y, call_curve_x) in enumerate(call_curves_data):
                for j, (put_strike, _, put_curve_y, put_curve_x) in enumerate(put_curves_data):

                    # Find intersection point
                    intersection_point = self.find_detailed_curve_intersection(call_curve_x, call_curve_y, put_curve_x, put_curve_y)

                    if intersection_point is not None:
                        intersection_x, intersection_y = intersection_point

                        # Add a white dot at the intersection
                        dot = pg.ScatterPlotItem(
                            x=[intersection_x],
                            y=[intersection_y],
                            pen=pg.mkPen(color='white', width=2),
                            brush=pg.mkBrush(color='white'),
                            size=6,
                            symbol='o'
                        )
                        self.plot_widget.addItem(dot)
                        logger.debug(f"Added intersection dot at ({intersection_x:.2f}, {intersection_y:.2f}) between call strike {call_strike:.2f} and put strike {put_strike:.2f}")

        except Exception as e:
            logger.error(f"Error adding intersection dots: {str(e)}", exc_info=True)

    def add_density_intersection_dots(self, all_curves, band_values=None):
        """
        Add red dots to mark intersections between density curves (non-options based sine waves).
        Excludes intersections that occur in filtered zones:
        - Between the 50% percentile bands (existing filtering)
        - Top half of the zone between bottom 25% and 50% bands (grey area)
        - Bottom half of the zone between top 50% and 25% bands (grey area)
        - Bottom half of the zone between bottom 1% and 25% bands (grey area)
        - Top half of the zone between top 25% and 1% bands (grey area)
        Only displays intersections when Density Zones is active.

        Also performs DBSCAN clustering to identify and circle the top 2 most clustered intersection areas.
        Applies clustering separately to:
        - Orange intersections (those within red zones/side cross zones)
        - Red intersections (those outside red zones/side cross zones)
        Both use the same exact clustering settings but are kept separate.

        Args:
            all_curves: List of (x_smooth, y_smooth, combined_distance, is_lowest_low, is_highest_high) for density curves
            band_values: Dict containing band values from the chart
        """
        try:
            # Check if Density Zones is active
            if not self.density_zones_active:
                logger.info("Density Zones is inactive - skipping intersection dots and peaks/troughs")
                return

            # First, find global peaks and troughs to create side cross zones BEFORE processing intersections
            global_highest_peak = None
            global_highest_peak_x = None
            global_lowest_trough = None
            global_lowest_trough_x = None

            # Track global extremes across all curves
            for curve_x, curve_y, _, _, _ in all_curves:
                if len(curve_x) == 0 or len(curve_y) == 0:
                    continue

                # Detect peaks and troughs for this curve
                peaks, troughs = self.detect_curve_peaks_and_troughs(curve_x, curve_y)

                # Track global highest peak
                for peak_x, peak_y in peaks:
                    if global_highest_peak is None or peak_y > global_highest_peak:
                        global_highest_peak = peak_y
                        global_highest_peak_x = peak_x
                        logger.info(f"New global highest peak found: {peak_y:.2f} at x={peak_x:.2f}")

                # Track global lowest trough
                for trough_x, trough_y in troughs:
                    if global_lowest_trough is None or trough_y < global_lowest_trough:
                        global_lowest_trough = trough_y
                        global_lowest_trough_x = trough_x
                        logger.info(f"New global lowest trough found: {trough_y:.2f} at x={trough_x:.2f}")

            # Create side cross zones BEFORE processing intersections
            self.add_side_cross_vertical_lines(global_highest_peak, global_highest_peak_x, global_lowest_trough, global_lowest_trough_x)

            # Get all band values for comprehensive filtering
            high_50_percentile = None
            low_50_percentile = None
            maxavg_high = None
            maxavg_low = None

            # Use the actual band values from the chart if available
            if band_values and 'high_median' in band_values and 'low_median' in band_values:
                high_50_percentile = band_values['high_median']
                low_50_percentile = band_values['low_median']
                logger.info(f"Using chart 50% bands: Low={low_50_percentile:.2f}, High={high_50_percentile:.2f}")

                # Debug: Log the band range
                band_range = high_50_percentile - low_50_percentile
                logger.info(f"50% band range: {band_range:.2f}")
            else:
                # Fallback: Calculate the 50% percentile bands from all curve data points
                all_y_values = []
                for curve_x, curve_y, _, _, _ in all_curves:
                    if len(curve_y) > 0:
                        all_y_values.extend(curve_y)

                if not all_y_values:
                    logger.warning("No curve data available for percentile calculation")
                    return

                all_y_values = np.array(all_y_values)

                # Calculate the 25th and 75th percentiles (50% band)
                low_50_percentile = np.percentile(all_y_values, 25)   # Lower boundary of 50% band
                high_50_percentile = np.percentile(all_y_values, 75)  # Upper boundary of 50% band

                logger.info(f"Fallback 50% percentile bands: Low={low_50_percentile:.2f}, High={high_50_percentile:.2f}")

            # Get 25% band values (maxavg_high and maxavg_low) from instance variables
            if hasattr(self, 'maxavg_high') and self.maxavg_high is not None:
                maxavg_high = self.maxavg_high
                logger.info(f"Using stored maxavg_high: {maxavg_high:.2f}")

            if hasattr(self, 'maxavg_low') and self.maxavg_low is not None:
                maxavg_low = self.maxavg_low
                logger.info(f"Using stored maxavg_low: {maxavg_low:.2f}")

            # Get 1% band values (highest_high and lowest_low) from instance variables
            highest_high = None
            lowest_low = None
            if hasattr(self, 'highest_high') and self.highest_high is not None:
                highest_high = self.highest_high
                logger.info(f"Using stored highest_high: {highest_high:.2f}")

            if hasattr(self, 'lowest_low') and self.lowest_low is not None:
                lowest_low = self.lowest_low
                logger.info(f"Using stored lowest_low: {lowest_low:.2f}")

            intersection_count = 0
            skipped_count = 0
            skipped_50_band = 0
            skipped_25_top_half = 0
            skipped_25_bottom_half = 0
            skipped_1_top_half = 0
            skipped_1_bottom_half = 0

            # Store all valid intersections for clustering analysis
            valid_intersections = []  # Orange intersections (in red zones)
            red_intersections = []    # Red intersections (outside red zones)

            # Clear previous snap point data
            self.valid_intersections = []
            self.red_intersections = []

            # Find ALL intersections between all density curves
            for i, (curve1_x, curve1_y, _, _, _) in enumerate(all_curves):
                for j, (curve2_x, curve2_y, _, _, _) in enumerate(all_curves):
                    if i >= j:  # Avoid duplicate comparisons and self-comparison
                        continue

                    # Find ALL intersection points between these two curves
                    intersection_points = self.find_all_curve_intersections(curve1_x, curve1_y, curve2_x, curve2_y)

                    for intersection_x, intersection_y in intersection_points:
                        skip_intersection = False
                        skip_reason = ""

                        # Check if intersection is within the 50% percentile bands (existing filtering)
                        if high_50_percentile is not None and low_50_percentile is not None:
                            band_range = abs(high_50_percentile - low_50_percentile)
                            buffer = band_range * 0.02  # 2% buffer of the band range (reduced from 5%)
                            expanded_low = low_50_percentile - buffer
                            expanded_high = high_50_percentile + buffer

                            if expanded_low <= intersection_y <= expanded_high:
                                skip_intersection = True
                                skip_reason = "within 50% bands"
                                skipped_50_band += 1

                        # Check if intersection is in top half of the zone between bottom 25% and 50% bands (grey area)
                        if not skip_intersection and maxavg_low is not None and low_50_percentile is not None:
                            # Calculate midpoint between maxavg_low (25%) and low_median (50%)
                            midpoint_bottom = (maxavg_low + low_50_percentile) / 2
                            # Filter top half: from midpoint to 50% line
                            if midpoint_bottom <= intersection_y <= low_50_percentile:
                                skip_intersection = True
                                skip_reason = f"in top half of bottom 25%-50% zone (grey area) [{midpoint_bottom:.2f} to {low_50_percentile:.2f}]"
                                skipped_25_bottom_half += 1

                        # Check if intersection is in bottom half of the zone between top 50% and 25% bands (grey area)
                        if not skip_intersection and high_50_percentile is not None and maxavg_high is not None:
                            # Calculate midpoint between high_median (50%) and maxavg_high (25%)
                            midpoint_top = (high_50_percentile + maxavg_high) / 2
                            # Filter bottom half: from 50% line to midpoint
                            if high_50_percentile <= intersection_y <= midpoint_top:
                                skip_intersection = True
                                skip_reason = f"in bottom half of top 50%-25% zone (grey area) [{high_50_percentile:.2f} to {midpoint_top:.2f}]"
                                skipped_25_top_half += 1

                        # Check if intersection is in bottom half of the zone between bottom 1% and 25% bands (grey area)
                        if not skip_intersection and lowest_low is not None and maxavg_low is not None:
                            # Calculate midpoint between lowest_low (1%) and maxavg_low (25%)
                            midpoint_bottom_1 = (lowest_low + maxavg_low) / 2
                            # Filter bottom half: from 1% line to midpoint
                            if lowest_low <= intersection_y <= midpoint_bottom_1:
                                skip_intersection = True
                                skip_reason = f"in bottom half of bottom 1%-25% zone (grey area) [{lowest_low:.2f} to {midpoint_bottom_1:.2f}]"
                                skipped_1_bottom_half += 1

                        # Check if intersection is in top half of the zone between top 25% and 1% bands (grey area)
                        if not skip_intersection and maxavg_high is not None and highest_high is not None:
                            # Calculate midpoint between maxavg_high (25%) and highest_high (1%)
                            midpoint_top_1 = (maxavg_high + highest_high) / 2
                            # Filter top half: from midpoint to 1% line
                            if midpoint_top_1 <= intersection_y <= highest_high:
                                skip_intersection = True
                                skip_reason = f"in top half of top 25%-1% zone (grey area) [{midpoint_top_1:.2f} to {highest_high:.2f}]"
                                skipped_1_top_half += 1

                        if skip_intersection:
                            skipped_count += 1
                            logger.info(f"SKIPPED intersection at ({intersection_x:.2f}, {intersection_y:.2f}) - {skip_reason}")
                            continue

                        # Check if intersection is in a side cross zone (with price restrictions)
                        zone_info = self.is_intersection_in_side_cross_zone(intersection_x, intersection_y)

                        if zone_info is not None:
                            # Orange dot for intersections inside side cross zones
                            dot_color = 'orange'
                            log_message = f"ADDED orange intersection dot at ({intersection_x:.2f}, {intersection_y:.2f}) - Inside {zone_info['type']} side cross zone"
                        else:
                            # Red dot for intersections outside side cross zones
                            dot_color = 'red'
                            log_message = f"ADDED red intersection dot at ({intersection_x:.2f}, {intersection_y:.2f}) - Outside all filtered zones and side cross zones"

                        # Check if red intersection is within ±0.01% of any percent band (invalidate for zone usage)
                        is_invalidated = False
                        if dot_color == 'red':  # Only apply to red intersections, not orange ones
                            is_invalidated = self.is_intersection_near_percent_band(intersection_y,
                                                                                  high_50_percentile, low_50_percentile,
                                                                                  maxavg_high, maxavg_low,
                                                                                  highest_high, lowest_low)
                            if is_invalidated:
                                dot_color = '#404040'  # Dark grey for invalidated intersections
                                log_message = f"ADDED dark grey intersection dot at ({intersection_x:.2f}, {intersection_y:.2f}) - INVALIDATED: within ±0.02% of percent band"

                        # Add the intersection dot with appropriate color (will be updated later if in cluster)
                        dot = pg.ScatterPlotItem(
                            x=[intersection_x],
                            y=[intersection_y],
                            pen=pg.mkPen(color=dot_color, width=2),
                            brush=pg.mkBrush(color=dot_color),
                            size=6,
                            symbol='o'
                        )
                        self.plot_widget.addItem(dot)

                        # Store valid intersection for clustering analysis
                        # Include orange intersections (those in red zones) and red intersections (outside red zones) for separate clustering
                        # Exclude invalidated intersections (dark grey) from clustering
                        if dot_color == 'orange':
                            valid_intersections.append((intersection_x, intersection_y, dot_color, dot))
                            self.valid_intersections.append((intersection_x, intersection_y, dot_color, dot))  # Store for snapping
                            logger.info(f"Added orange intersection at ({intersection_x:.2f}, {intersection_y:.2f}) to orange clustering analysis")
                        elif dot_color == 'red':
                            red_intersections.append((intersection_x, intersection_y, dot_color, dot))
                            self.red_intersections.append((intersection_x, intersection_y, dot_color, dot))  # Store for snapping
                            logger.info(f"Added red intersection at ({intersection_x:.2f}, {intersection_y:.2f}) to red clustering analysis")
                        elif dot_color == '#404040':  # Dark grey invalidated intersections
                            logger.info(f"Excluded invalidated intersection at ({intersection_x:.2f}, {intersection_y:.2f}) from clustering analysis")

                        intersection_count += 1
                        logger.info(log_message)

            logger.info(f"Added {intersection_count} intersection dots")
            logger.info(f"Skipped {skipped_count} total: {skipped_50_band} in 50% bands, {skipped_25_bottom_half} in bottom 25%-50% grey area, {skipped_25_top_half} in top 50%-25% grey area, {skipped_1_bottom_half} in bottom 1%-25% grey area, {skipped_1_top_half} in top 25%-1% grey area")

            # Perform DBSCAN clustering on orange intersections (red zone intersections) and highlight top 2 clusters
            if len(valid_intersections) >= 3:  # Need at least 3 points for meaningful clustering
                logger.info(f"Starting clustering analysis on {len(valid_intersections)} orange intersections (red zone intersections)")
                self.add_intersection_cluster_circles(valid_intersections, all_curves)
            else:
                logger.info(f"Not enough orange intersections ({len(valid_intersections)}) for clustering analysis - need at least 3")

            # Perform DBSCAN clustering on red intersections (outside red zones) with same settings as orange intersections
            if len(red_intersections) >= 3:  # Need at least 3 points for meaningful clustering
                logger.info(f"Starting clustering analysis on {len(red_intersections)} red intersections (outside red zones)")
                self.add_red_intersection_cluster_circles(red_intersections, all_curves)
            else:
                logger.info(f"Not enough red intersections ({len(red_intersections)}) for clustering analysis - need at least 3")

            # Apply ±0.04% rule to orange intersections that are NOT part of clusters
            self.apply_percent_band_rule_to_non_clustered_orange(valid_intersections,
                                                               high_50_percentile, low_50_percentile,
                                                               maxavg_high, maxavg_low,
                                                               highest_high, lowest_low)

            # Apply ±0.04% rule to red intersections that are NOT part of clusters
            self.apply_percent_band_rule_to_non_clustered_red(red_intersections,
                                                            high_50_percentile, low_50_percentile,
                                                            maxavg_high, maxavg_low,
                                                            highest_high, lowest_low)

            # Add peaks and troughs markers for density waves
            self.add_density_peaks_and_troughs(all_curves, band_values)

        except Exception as e:
            logger.error(f"Error adding density intersection dots: {str(e)}", exc_info=True)

    def is_intersection_near_percent_band(self, intersection_y, high_50_percentile, low_50_percentile,
                                        maxavg_high, maxavg_low, highest_high, lowest_low):
        """
        Check if an intersection is within ±0.02% range of any percent band.
        This invalidates the intersection for zone usage.

        Args:
            intersection_y: Y-coordinate of the intersection
            high_50_percentile: Upper boundary of 50% band
            low_50_percentile: Lower boundary of 50% band
            maxavg_high: 25% high band value
            maxavg_low: 25% low band value
            highest_high: 1% high band value
            lowest_low: 1% low band value

        Returns:
            bool: True if intersection is within ±0.02% of any percent band, False otherwise
        """
        try:
            # Define the tolerance as ±0.02% of the intersection's y-value (reduced from 0.04%)
            tolerance_percent = 0.0002  # 0.02% as decimal
            tolerance_range = abs(intersection_y * tolerance_percent)

            # List of all percent band values to check against
            band_values = []

            # Add 50% bands
            if high_50_percentile is not None:
                band_values.append(high_50_percentile)
            if low_50_percentile is not None:
                band_values.append(low_50_percentile)

            # Add 25% bands
            if maxavg_high is not None:
                band_values.append(maxavg_high)
            if maxavg_low is not None:
                band_values.append(maxavg_low)

            # Add 1% bands
            if highest_high is not None:
                band_values.append(highest_high)
            if lowest_low is not None:
                band_values.append(lowest_low)

            # Check if intersection is within ±0.02% of any band
            for band_value in band_values:
                distance = abs(intersection_y - band_value)
                if distance <= tolerance_range:
                    logger.info(f"Intersection at y={intersection_y:.2f} is within ±0.02% of band {band_value:.2f} (distance: {distance:.4f}, tolerance: {tolerance_range:.4f})")
                    return True

            return False

        except Exception as e:
            logger.error(f"Error checking intersection proximity to percent bands: {str(e)}", exc_info=True)
            return False

    def apply_percent_band_rule_to_non_clustered_orange(self, valid_intersections,
                                                       high_50_percentile, low_50_percentile,
                                                       maxavg_high, maxavg_low,
                                                       highest_high, lowest_low):
        """
        Apply ±0.02% percent band invalidation rule to orange intersections that are NOT part of clusters.
        This checks which orange intersections remained orange after clustering (i.e., not highlighted in blue)
        and invalidates those that are within ±0.02% of any percent band.

        Args:
            valid_intersections: List of (x, y, color, dot_reference) tuples for orange intersections
            high_50_percentile: Upper boundary of 50% band
            low_50_percentile: Lower boundary of 50% band
            maxavg_high: 25% high band value
            maxavg_low: 25% low band value
            highest_high: 1% high band value
            lowest_low: 1% low band value
        """
        try:
            invalidated_count = 0

            for intersection_x, intersection_y, _, dot_ref in valid_intersections:
                # Check if this intersection is still orange (not part of a cluster)
                # Clustered intersections get changed to blue colors, so we check the current pen color
                try:
                    current_pen = dot_ref.pen
                    current_color = current_pen.color()

                    # Check if the color is still orange (RGB values for orange)
                    # Orange in PyQtGraph is typically RGB(255, 165, 0) or similar orange variants
                    is_orange = (current_color.red() > 200 and
                                current_color.green() > 100 and current_color.green() < 200 and
                                current_color.blue() < 50)
                except:
                    # Fallback: assume it's still orange if we can't determine the color
                    is_orange = True

                # If it's still orange, apply the percent band rule
                if is_orange:
                    # Check if this orange intersection is within ±0.04% of any percent band
                    is_invalidated = self.is_intersection_near_percent_band(intersection_y,
                                                                          high_50_percentile, low_50_percentile,
                                                                          maxavg_high, maxavg_low,
                                                                          highest_high, lowest_low)

                    if is_invalidated:
                        # Change color to dark grey for invalidated non-clustered orange intersections
                        dot_ref.setPen(pg.mkPen(color='#404040', width=2))
                        dot_ref.setBrush(pg.mkBrush(color='#404040'))
                        invalidated_count += 1
                        logger.info(f"Invalidated non-clustered orange intersection at ({intersection_x:.2f}, {intersection_y:.2f}) - within ±0.02% of percent band")

            if invalidated_count > 0:
                logger.info(f"Applied ±0.02% rule to {invalidated_count} non-clustered orange intersections")
            else:
                logger.info("No non-clustered orange intersections were invalidated by ±0.02% rule")

        except Exception as e:
            logger.error(f"Error applying percent band rule to non-clustered orange intersections: {str(e)}", exc_info=True)

    def apply_percent_band_rule_to_non_clustered_red(self, red_intersections,
                                                   high_50_percentile, low_50_percentile,
                                                   maxavg_high, maxavg_low,
                                                   highest_high, lowest_low):
        """
        Apply ±0.02% percent band invalidation rule to red intersections that are NOT part of clusters.
        This checks which red intersections remained red after clustering (i.e., not highlighted in blue)
        and invalidates those that are within ±0.02% of any percent band.

        Args:
            red_intersections: List of (x, y, color, dot_reference) tuples for red intersections
            high_50_percentile: Upper boundary of 50% band
            low_50_percentile: Lower boundary of 50% band
            maxavg_high: 25% high band value
            maxavg_low: 25% low band value
            highest_high: 1% high band value
            lowest_low: 1% low band value
        """
        try:
            invalidated_count = 0

            for intersection_x, intersection_y, _, dot_ref in red_intersections:
                # Check if this intersection is still red (not part of a cluster)
                # Clustered intersections get changed to blue colors, so we check the current pen color
                try:
                    current_pen = dot_ref.pen
                    current_color = current_pen.color()

                    # Check if the color is still red (RGB values for red)
                    # Red in PyQtGraph is typically RGB(255, 0, 0) or similar red variants
                    is_red = (current_color.red() > 200 and
                             current_color.green() < 50 and
                             current_color.blue() < 50)
                except:
                    is_red = True  # Default to checking if we can't determine color

                # If it's still red, apply the percent band rule
                if is_red:
                    # Check if this red intersection is within ±0.04% of any percent band
                    is_invalidated = self.is_intersection_near_percent_band(intersection_y,
                                                                          high_50_percentile, low_50_percentile,
                                                                          maxavg_high, maxavg_low,
                                                                          highest_high, lowest_low)

                    if is_invalidated:
                        # Change color to dark grey for invalidated non-clustered red intersections
                        dot_ref.setPen(pg.mkPen(color='#404040', width=2))
                        dot_ref.setBrush(pg.mkBrush(color='#404040'))
                        invalidated_count += 1
                        logger.info(f"Invalidated non-clustered red intersection at ({intersection_x:.2f}, {intersection_y:.2f}) - within ±0.02% of percent band")

            if invalidated_count > 0:
                logger.info(f"Applied ±0.02% rule to {invalidated_count} non-clustered red intersections")
            else:
                logger.info("No non-clustered red intersections were invalidated by ±0.02% rule")

        except Exception as e:
            logger.error(f"Error applying percent band rule to non-clustered red intersections: {str(e)}", exc_info=True)

    def add_density_peaks_and_troughs(self, all_curves, band_values=None):
        """
        Add markers for peaks and troughs of density wave curves.
        Excludes peaks/troughs that occur in filtered zones:
        - Between the 50% percentile bands (existing filtering)
        - Top half of the zone between bottom 25% and 50% bands (grey area)
        - Bottom half of the zone between top 50% and 25% bands (grey area)
        - Bottom half of the zone between bottom 1% and 25% bands (grey area)
        - Top half of the zone between top 25% and 1% bands (grey area)

        Also performs DBSCAN clustering to identify and highlight clustered peaks and troughs
        that are outside the red zone (same settings as intersections but no red zone restriction).

        Args:
            all_curves: List of (x_smooth, y_smooth, combined_distance, is_lowest_low, is_highest_high) for density curves
            band_values: Dict containing band values from the chart
        """
        try:
            # Get all band values for comprehensive filtering
            high_50_percentile = None
            low_50_percentile = None
            maxavg_high = None
            maxavg_low = None

            # Use the same band logic as intersection dots
            if band_values and 'high_median' in band_values and 'low_median' in band_values:
                high_50_percentile = band_values['high_median']
                low_50_percentile = band_values['low_median']
                logger.info(f"Using chart 50% bands for peaks/troughs: Low={low_50_percentile:.2f}, High={high_50_percentile:.2f}")
            else:
                # Fallback: Calculate the 50% percentile bands from all curve data points
                all_y_values = []
                for curve_x, curve_y, _, _, _ in all_curves:
                    if len(curve_y) > 0:
                        all_y_values.extend(curve_y)

                if not all_y_values:
                    logger.warning("No curve data available for peaks/troughs percentile calculation")
                    return

                all_y_values = np.array(all_y_values)
                low_50_percentile = np.percentile(all_y_values, 25)   # Lower boundary of 50% band
                high_50_percentile = np.percentile(all_y_values, 75)  # Upper boundary of 50% band
                logger.info(f"Fallback 50% percentile bands for peaks/troughs: Low={low_50_percentile:.2f}, High={high_50_percentile:.2f}")

            # Get 25% band values (maxavg_high and maxavg_low) from instance variables
            if hasattr(self, 'maxavg_high') and self.maxavg_high is not None:
                maxavg_high = self.maxavg_high
                logger.info(f"Using stored maxavg_high for peaks/troughs: {maxavg_high:.2f}")

            if hasattr(self, 'maxavg_low') and self.maxavg_low is not None:
                maxavg_low = self.maxavg_low
                logger.info(f"Using stored maxavg_low for peaks/troughs: {maxavg_low:.2f}")

            # Get 1% band values (highest_high and lowest_low) from instance variables
            highest_high = None
            lowest_low = None
            if hasattr(self, 'highest_high') and self.highest_high is not None:
                highest_high = self.highest_high
                logger.info(f"Using stored highest_high for peaks/troughs: {highest_high:.2f}")

            if hasattr(self, 'lowest_low') and self.lowest_low is not None:
                lowest_low = self.lowest_low
                logger.info(f"Using stored lowest_low for peaks/troughs: {lowest_low:.2f}")

            peak_count = 0
            trough_count = 0
            skipped_peaks = 0
            skipped_troughs = 0
            skipped_peaks_50_band = 0
            skipped_peaks_25_top_half = 0
            skipped_peaks_25_bottom_half = 0
            skipped_peaks_1_top_half = 0
            skipped_peaks_1_bottom_half = 0
            skipped_troughs_50_band = 0
            skipped_troughs_25_top_half = 0
            skipped_troughs_25_bottom_half = 0
            skipped_troughs_1_top_half = 0
            skipped_troughs_1_bottom_half = 0

            # Track the highest peak and lowest trough across all curves for side cross vertical lines
            global_highest_peak = None
            global_lowest_trough = None

            # Store all valid peaks and troughs for clustering analysis
            all_valid_peaks = []
            all_valid_troughs = []

            # Clear previous snap point data
            self.all_valid_peaks = []
            self.all_valid_troughs = []

            # Process each density curve to find peaks and troughs
            for curve_x, curve_y, _, _, _ in all_curves:
                if len(curve_y) < 3:  # Need at least 3 points to detect peaks/troughs
                    continue

                # Find peaks and troughs in this curve
                peaks, troughs = self.detect_curve_peaks_and_troughs(curve_x, curve_y)

                # Track global highest peak and lowest trough BEFORE filtering
                for peak_x, peak_y in peaks:
                    if global_highest_peak is None or peak_y > global_highest_peak:
                        global_highest_peak = peak_y
                        logger.debug(f"New global highest peak found: ({peak_x:.2f}, {peak_y:.2f})")

                for trough_x, trough_y in troughs:
                    if global_lowest_trough is None or trough_y < global_lowest_trough:
                        global_lowest_trough = trough_y
                        logger.debug(f"New global lowest trough found: ({trough_x:.2f}, {trough_y:.2f})")

                # Add peak rays (excluding those in filtered zones)
                for peak_x, peak_y in peaks:
                    skip_peak = False
                    skip_reason = ""

                    # Check if peak is within the 50% percentile bands
                    if high_50_percentile is not None and low_50_percentile is not None:
                        if low_50_percentile <= peak_y <= high_50_percentile:
                            skip_peak = True
                            skip_reason = "within 50% bands"
                            skipped_peaks_50_band += 1

                    # Check if peak is in top half of the zone between bottom 25% and 50% bands (grey area)
                    if not skip_peak and maxavg_low is not None and low_50_percentile is not None:
                        # Calculate midpoint between maxavg_low (25%) and low_median (50%)
                        midpoint_bottom = (maxavg_low + low_50_percentile) / 2
                        # Filter top half: from midpoint to 50% line
                        if midpoint_bottom <= peak_y <= low_50_percentile:
                            skip_peak = True
                            skip_reason = f"in top half of bottom 25%-50% zone (grey area) [{midpoint_bottom:.2f} to {low_50_percentile:.2f}]"
                            skipped_peaks_25_bottom_half += 1

                    # Check if peak is in bottom half of the zone between top 50% and 25% bands (grey area)
                    if not skip_peak and high_50_percentile is not None and maxavg_high is not None:
                        # Calculate midpoint between high_median (50%) and maxavg_high (25%)
                        midpoint_top = (high_50_percentile + maxavg_high) / 2
                        # Filter bottom half: from 50% line to midpoint
                        if high_50_percentile <= peak_y <= midpoint_top:
                            skip_peak = True
                            skip_reason = f"in bottom half of top 50%-25% zone (grey area) [{high_50_percentile:.2f} to {midpoint_top:.2f}]"
                            skipped_peaks_25_top_half += 1

                    # Check if peak is in bottom half of the zone between bottom 1% and 25% bands (grey area)
                    if not skip_peak and lowest_low is not None and maxavg_low is not None:
                        # Calculate midpoint between lowest_low (1%) and maxavg_low (25%)
                        midpoint_bottom_1 = (lowest_low + maxavg_low) / 2
                        # Filter bottom half: from 1% line to midpoint
                        if lowest_low <= peak_y <= midpoint_bottom_1:
                            skip_peak = True
                            skip_reason = f"in bottom half of bottom 1%-25% zone (grey area) [{lowest_low:.2f} to {midpoint_bottom_1:.2f}]"
                            skipped_peaks_1_bottom_half += 1

                    # Check if peak is in top half of the zone between top 25% and 1% bands (grey area)
                    if not skip_peak and maxavg_high is not None and highest_high is not None:
                        # Calculate midpoint between maxavg_high (25%) and highest_high (1%)
                        midpoint_top_1 = (maxavg_high + highest_high) / 2
                        # Filter top half: from midpoint to 1% line
                        if midpoint_top_1 <= peak_y <= highest_high:
                            skip_peak = True
                            skip_reason = f"in top half of top 25%-1% zone (grey area) [{midpoint_top_1:.2f} to {highest_high:.2f}]"
                            skipped_peaks_1_top_half += 1

                    if skip_peak:
                        skipped_peaks += 1
                        logger.debug(f"SKIPPED peak at ({peak_x:.2f}, {peak_y:.2f}) - {skip_reason}")
                        continue

                    # Store valid peak for clustering analysis
                    all_valid_peaks.append((peak_x, peak_y))
                    self.all_valid_peaks.append((peak_x, peak_y))  # Store for snapping

                    # Add a dark green line marker for peaks (outside filtered zones)
                    self.add_density_ray(peak_y, 'Peak', f'Density Peak: {peak_y:.2f}', color='darkgreen', peak_x=peak_x)
                    peak_count += 1
                    logger.debug(f"Added peak line marker at {peak_y:.2f}")

                # Add trough rays (excluding those in filtered zones)
                for trough_x, trough_y in troughs:
                    skip_trough = False
                    skip_reason = ""

                    # Check if trough is within the 50% percentile bands
                    if high_50_percentile is not None and low_50_percentile is not None:
                        if low_50_percentile <= trough_y <= high_50_percentile:
                            skip_trough = True
                            skip_reason = "within 50% bands"
                            skipped_troughs_50_band += 1

                    # Check if trough is in top half of the zone between bottom 25% and 50% bands (grey area)
                    if not skip_trough and maxavg_low is not None and low_50_percentile is not None:
                        # Calculate midpoint between maxavg_low (25%) and low_median (50%)
                        midpoint_bottom = (maxavg_low + low_50_percentile) / 2
                        # Filter top half: from midpoint to 50% line
                        if midpoint_bottom <= trough_y <= low_50_percentile:
                            skip_trough = True
                            skip_reason = f"in top half of bottom 25%-50% zone (grey area) [{midpoint_bottom:.2f} to {low_50_percentile:.2f}]"
                            skipped_troughs_25_bottom_half += 1

                    # Check if trough is in bottom half of the zone between top 50% and 25% bands (grey area)
                    if not skip_trough and high_50_percentile is not None and maxavg_high is not None:
                        # Calculate midpoint between high_median (50%) and maxavg_high (25%)
                        midpoint_top = (high_50_percentile + maxavg_high) / 2
                        # Filter bottom half: from 50% line to midpoint
                        if high_50_percentile <= trough_y <= midpoint_top:
                            skip_trough = True
                            skip_reason = f"in bottom half of top 50%-25% zone (grey area) [{high_50_percentile:.2f} to {midpoint_top:.2f}]"
                            skipped_troughs_25_top_half += 1

                    # Check if trough is in bottom half of the zone between bottom 1% and 25% bands (grey area)
                    if not skip_trough and lowest_low is not None and maxavg_low is not None:
                        # Calculate midpoint between lowest_low (1%) and maxavg_low (25%)
                        midpoint_bottom_1 = (lowest_low + maxavg_low) / 2
                        # Filter bottom half: from 1% line to midpoint
                        if lowest_low <= trough_y <= midpoint_bottom_1:
                            skip_trough = True
                            skip_reason = f"in bottom half of bottom 1%-25% zone (grey area) [{lowest_low:.2f} to {midpoint_bottom_1:.2f}]"
                            skipped_troughs_1_bottom_half += 1

                    # Check if trough is in top half of the zone between top 25% and 1% bands (grey area)
                    if not skip_trough and maxavg_high is not None and highest_high is not None:
                        # Calculate midpoint between maxavg_high (25%) and highest_high (1%)
                        midpoint_top_1 = (maxavg_high + highest_high) / 2
                        # Filter top half: from midpoint to 1% line
                        if midpoint_top_1 <= trough_y <= highest_high:
                            skip_trough = True
                            skip_reason = f"in top half of top 25%-1% zone (grey area) [{midpoint_top_1:.2f} to {highest_high:.2f}]"
                            skipped_troughs_1_top_half += 1

                    if skip_trough:
                        skipped_troughs += 1
                        logger.debug(f"SKIPPED trough at ({trough_x:.2f}, {trough_y:.2f}) - {skip_reason}")
                        continue

                    # Store valid trough for clustering analysis
                    all_valid_troughs.append((trough_x, trough_y))
                    self.all_valid_troughs.append((trough_x, trough_y))  # Store for snapping

                    # Add a dark red line marker for troughs (outside filtered zones)
                    self.add_density_ray(trough_y, 'Trough', f'Density Trough: {trough_y:.2f}', color='darkred', trough_x=trough_x)
                    trough_count += 1
                    logger.debug(f"Added trough line marker at {trough_y:.2f}")

            logger.info(f"Added {peak_count} peak markers and {trough_count} trough markers")
            logger.info(f"Skipped {skipped_peaks} peaks: {skipped_peaks_50_band} in 50% bands, {skipped_peaks_25_bottom_half} in bottom 25%-50% grey area, {skipped_peaks_25_top_half} in top 50%-25% grey area, {skipped_peaks_1_bottom_half} in bottom 1%-25% grey area, {skipped_peaks_1_top_half} in top 25%-1% grey area")
            logger.info(f"Skipped {skipped_troughs} troughs: {skipped_troughs_50_band} in 50% bands, {skipped_troughs_25_bottom_half} in bottom 25%-50% grey area, {skipped_troughs_25_top_half} in top 50%-25% grey area, {skipped_troughs_1_bottom_half} in bottom 1%-25% grey area, {skipped_troughs_1_top_half} in top 25%-1% grey area")

            # Apply DBSCAN clustering to peaks and troughs
            self.apply_dbscan_to_peaks_and_troughs(all_valid_peaks, all_valid_troughs)

            # Note: Side cross vertical zones are now created earlier in add_density_intersection_dots method

        except Exception as e:
            logger.error(f"Error adding density peaks and troughs: {str(e)}", exc_info=True)

    def filter_cluster_outliers(self, cluster_points, cluster_point_types):
        """
        Filter out outliers from a cluster to keep only the closer/denser parts.
        This removes points that form a "tail" by focusing on the densest region.

        Args:
            cluster_points: numpy array of (x, y) points in the cluster
            cluster_point_types: list of point types ('peak' or 'trough')

        Returns:
            tuple: (filtered_points, filtered_types) with outliers removed
        """
        try:
            print(f"DEBUG: Filtering cluster with {len(cluster_points)} points")

            if len(cluster_points) <= 2:
                # Don't filter very small clusters
                logger.debug("DEBUG: Cluster too small to filter, keeping all points")
                return cluster_points, cluster_point_types

            # For vertical clusters (peaks/troughs), focus on Y-axis density
            y_values = cluster_points[:, 1]
            logger.debug(f"DEBUG: Y-values in cluster: {[f'{y:.2f}' for y in y_values]}")

            # Sort points by Y value to analyze density
            sorted_indices = np.argsort(y_values)
            sorted_y = y_values[sorted_indices]
            logger.debug(f"DEBUG: Sorted Y-values: {[f'{y:.2f}' for y in sorted_y]}")

            # Calculate gaps between consecutive points
            gaps = np.diff(sorted_y)
            logger.debug(f"DEBUG: Gaps between points: {[f'{g:.2f}' for g in gaps]}")

            # Find the largest gap and compare it to the smallest gap
            if len(gaps) > 0:
                max_gap = np.max(gaps)
                min_gap = np.min(gaps)
                max_gap_idx = np.argmax(gaps)

                logger.debug(f"DEBUG: Largest gap: {max_gap:.2f} at index {max_gap_idx}")
                logger.debug(f"DEBUG: Smallest gap: {min_gap:.2f}")

                # If the largest gap is more than 2x the smallest gap, remove the tail
                if max_gap > 2.0 * min_gap and max_gap > 0.01:  # Small minimum to avoid noise
                    print(f"DEBUG: Large gap ({max_gap:.2f}) is >2x small gap ({min_gap:.2f}), removing tail")

                    # Determine which side of the gap has more points (the dense core)
                    lower_count = max_gap_idx + 1  # Points below the gap
                    upper_count = len(sorted_y) - lower_count  # Points above the gap
                    logger.debug(f"DEBUG: Lower side has {lower_count} points, upper side has {upper_count} points")

                    if lower_count >= upper_count:
                        # Keep the lower (denser) part
                        keep_indices = sorted_indices[:max_gap_idx + 1]
                        print(f"DEBUG: Keeping lower {len(keep_indices)} points, removing upper tail")
                    else:
                        # Keep the upper (denser) part
                        keep_indices = sorted_indices[max_gap_idx + 1:]
                        print(f"DEBUG: Keeping upper {len(keep_indices)} points, removing lower tail")

                    # Filter the points and types
                    filtered_points = cluster_points[keep_indices]
                    filtered_types = [cluster_point_types[i] for i in keep_indices]

                    print(f"DEBUG: Filtered cluster: removed {len(cluster_points) - len(filtered_points)} tail points")
                    return filtered_points, filtered_types
                else:
                    print(f"DEBUG: Large gap ({max_gap:.2f}) is not >2x small gap ({min_gap:.2f}), keeping all points")

            # If no significant gap found, return original points
            return cluster_points, cluster_point_types

        except Exception as e:
            logger.error(f"Error filtering cluster outliers: {str(e)}")
            # Return original points if filtering fails
            return cluster_points, cluster_point_types

    def apply_dbscan_to_peaks_and_troughs(self, all_valid_peaks, all_valid_troughs):
        """
        Apply DBSCAN clustering to peaks and troughs and display cluster results
        only in the vertical sidebar (right-side markers).
        Also creates zones from clustered peaks/troughs when there are no clustered intersections in an area.

        Args:
            all_valid_peaks: List of (x, y) tuples for valid peaks
            all_valid_troughs: List of (x, y) tuples for valid troughs
        """
        try:
            print(f"DEBUG: apply_dbscan_to_peaks_and_troughs called with {len(all_valid_peaks)} peaks and {len(all_valid_troughs)} troughs")

            if not SKLEARN_AVAILABLE:
                logger.warning("scikit-learn not available. Skipping DBSCAN clustering for peaks and troughs.")
                return

            # Combine peaks and troughs for clustering
            all_points = []
            point_types = []

            # Add peaks
            for peak_x, peak_y in all_valid_peaks:
                all_points.append([peak_x, peak_y])
                point_types.append('peak')

            # Add troughs
            for trough_x, trough_y in all_valid_troughs:
                all_points.append([trough_x, trough_y])
                point_types.append('trough')

            print(f"DEBUG: Combined {len(all_points)} total points for clustering")

            if len(all_points) < 3:
                logger.info("Not enough peaks and troughs for DBSCAN clustering")
                return

            # Convert to numpy array
            points_array = np.array(all_points)

            # Apply DBSCAN clustering
            # eps: maximum distance between points in the same cluster
            # min_samples: minimum number of points to form a cluster
            eps = self.peaks_troughs_eps  # Use configurable parameter
            min_samples = self.peaks_troughs_min_samples  # Use configurable parameter

            dbscan = DBSCAN(eps=eps, min_samples=min_samples)
            cluster_labels = dbscan.fit_predict(points_array)

            # Count clusters (excluding noise points labeled as -1)
            unique_labels = set(cluster_labels)
            n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
            n_noise = list(cluster_labels).count(-1)

            logger.info(f"DBSCAN clustering results: {n_clusters} clusters, {n_noise} noise points")
            print(f"DEBUG: About to process {len(unique_labels)} unique labels: {list(unique_labels)}")

            # Store cluster data for zone creation
            peak_trough_clusters = []

            # Process clusters and add markers only to the vertical sidebar
            cluster_colors = ['blue', 'cyan', 'magenta', 'yellow', 'orange', 'purple', 'brown', 'pink']

            for cluster_id in unique_labels:
                if cluster_id == -1:
                    continue  # Skip noise points

                # Get points in this cluster
                cluster_mask = cluster_labels == cluster_id
                cluster_points = points_array[cluster_mask]
                cluster_point_types = [point_types[i] for i in range(len(point_types)) if cluster_mask[i]]

                # Filter out outliers from the cluster to keep only the closer/denser parts
                print(f"DEBUG: Cluster {cluster_id} before filtering: {len(cluster_points)} points at Y-values: {[f'{p[1]:.2f}' for p in cluster_points]}")
                filtered_points, filtered_types = self.filter_cluster_outliers(cluster_points, cluster_point_types)
                print(f"DEBUG: Cluster {cluster_id} after filtering: {len(filtered_points)} points at Y-values: {[f'{p[1]:.2f}' for p in filtered_points]}")

                # Only proceed if we still have enough points after filtering
                if len(filtered_points) >= 2:
                    # Count peaks and troughs in filtered cluster
                    peak_count = filtered_types.count('peak')
                    trough_count = filtered_types.count('trough')

                    # Store filtered cluster data for potential zone creation
                    peak_trough_clusters.append({
                        'id': cluster_id,
                        'points': filtered_points,
                        'types': filtered_types,
                        'peak_count': peak_count,
                        'trough_count': trough_count
                    })

                    # Use filtered points for visualization
                    cluster_points = filtered_points
                    cluster_point_types = filtered_types
                else:
                    logger.info(f"Cluster {cluster_id} filtered out - not enough points remaining after outlier removal")
                    continue

                # Choose color based on cluster ID
                color = cluster_colors[cluster_id % len(cluster_colors)]

                # Add markers for each point in the cluster (only right-side markers)
                for i, (_, point_y) in enumerate(cluster_points):
                    point_type = cluster_point_types[i]

                    # Create description with cluster information
                    description = f"Cluster {cluster_id} {point_type.title()} ({peak_count}P/{trough_count}T): {point_y:.2f}"

                    # Add only right-side marker (no on-peak/on-trough markers)
                    self.add_density_ray_sidebar_only(point_y, point_type.title(), description, color)

                logger.info(f"Added cluster {cluster_id} markers: {peak_count} peaks, {trough_count} troughs (color: {color})")

            # Store peak/trough clusters for priority ranking system
            self.peak_trough_clusters = peak_trough_clusters
            self.peak_trough_non_clusters = self.get_non_clustered_peaks_troughs(all_valid_peaks, all_valid_troughs, cluster_labels, points_array, point_types)

            # Create zones from peak/trough clusters if there are no intersection clusters in those areas (legacy method)
            # self.create_zones_from_peak_trough_clusters(peak_trough_clusters, all_valid_peaks, all_valid_troughs)

            # Use new priority ranking system for zone creation
            self.create_zones_with_priority_ranking_if_ready()

        except Exception as e:
            logger.error(f"Error applying DBSCAN to peaks and troughs: {str(e)}", exc_info=True)

    def create_zones_from_peak_trough_clusters(self, peak_trough_clusters, _=None, __=None):
        """
        Create orange zones from clustered peaks and troughs when there are no clustered intersections in those areas.
        This provides an alternative zone creation method when intersection clustering doesn't produce zones.
        Peak/trough zones do NOT extend to nearest peaks/troughs - they only span the actual cluster points.

        Limitation: Only one zone above each 25% band and one zone below each 25% band.
        This limits it to 2 zones maximum at the bullish side and 2 zones maximum on the bearish side.

        Args:
            peak_trough_clusters: List of cluster data dictionaries
            all_valid_peaks: List of (x, y) tuples for all valid peaks (unused - kept for compatibility)
            all_valid_troughs: List of (x, y) tuples for all valid troughs (unused - kept for compatibility)
        """
        try:
            if not peak_trough_clusters:
                logger.info("No peak/trough clusters available for zone creation")
                return

            # Get reference price for zone positioning
            reference_price_info = self.get_reference_price()
            if reference_price_info is None:
                logger.warning("Could not determine reference price for peak/trough zone creation")
                return

            reference_price, _, _ = reference_price_info
            logger.info(f"Creating zones from peak/trough clusters using reference price: {reference_price:.2f}")

            # Filter clusters to only include significant ones
            significant_clusters = []
            for cluster in peak_trough_clusters:
                cluster_id = cluster['id']
                cluster_points = cluster['points']
                peak_count = cluster['peak_count']
                trough_count = cluster['trough_count']
                total_points = len(cluster_points)

                # Only consider clusters with sufficient points (at least 2 points)
                if total_points < 2:
                    logger.debug(f"Skipping cluster {cluster_id}: insufficient points ({total_points})")
                    continue

                # Calculate cluster center and boundaries
                min_y = np.min(cluster_points[:, 1])
                max_y = np.max(cluster_points[:, 1])
                cluster_center_y = (min_y + max_y) / 2

                # Determine if this cluster is significant enough for a zone
                # Criteria: at least 2 points and reasonable spread
                y_spread = max_y - min_y
                if y_spread < 0.1:  # Very tight cluster
                    logger.debug(f"Skipping cluster {cluster_id}: too tight (spread: {y_spread:.3f})")
                    continue

                # Add cluster center and area information for filtering
                cluster['center_y'] = cluster_center_y
                cluster['area'] = self.get_cluster_filtered_area(cluster_center_y, reference_price)
                cluster['y_spread'] = y_spread

                # Calculate tightness score (lower is better/tighter)
                cluster['tightness_score'] = y_spread / total_points if total_points > 0 else float('inf')

                significant_clusters.append(cluster)
                logger.debug(f"Cluster {cluster_id}: center={cluster_center_y:.2f}, area='{cluster['area']}', tightness={cluster['tightness_score']:.3f}")

            if not significant_clusters:
                logger.info("No significant peak/trough clusters found for zone creation")
                return

            # Group clusters by area for limiting to one zone per band area
            clusters_by_area = {}
            for cluster in significant_clusters:
                area = cluster['area']
                if area not in clusters_by_area:
                    clusters_by_area[area] = []
                clusters_by_area[area].append(cluster)

            # Limit to 1 zone per filtered area - keep only the tightest cluster in each area
            limited_clusters = []
            for area, area_clusters in clusters_by_area.items():
                if area_clusters:
                    # Sort by tightness and take the tightest (lowest score)
                    area_clusters.sort(key=lambda x: x['tightness_score'], reverse=False)
                    tightest_cluster = area_clusters[0]
                    limited_clusters.append(tightest_cluster)
                    logger.info(f"Selected tightest peak/trough cluster in area '{area}': tightness={tightest_cluster['tightness_score']:.3f}, center={tightest_cluster['center_y']:.2f}")

            # NOTE: Zone creation now handled by priority ranking system
            # Create zones from the limited clusters
            zones_detected = 0
            for cluster in limited_clusters:
                cluster_id = cluster['id']
                cluster_points = cluster['points']
                peak_count = cluster['peak_count']
                trough_count = cluster['trough_count']
                cluster_center_y = cluster['center_y']

                # Detect zone from clustered peaks/troughs (zone creation handled by priority ranking)
                logger.info(f"Peak/trough cluster {cluster_id} detected: {peak_count} peaks, {trough_count} troughs, center at {cluster_center_y:.2f}, area '{cluster['area']}' - zones handled by priority ranking")

                # NOTE: Zone creation now handled by priority ranking system
                # self.add_peak_trough_cluster_zone(cluster_points, None, None, reference_price, cluster_id)
                zones_detected += 1

            logger.info(f"Detected {zones_detected} peak/trough clusters (zones handled by priority ranking system)")
            logger.info(f"Cluster distribution: {len([c for c in limited_clusters if c['center_y'] > reference_price])} above reference price, {len([c for c in limited_clusters if c['center_y'] <= reference_price])} below reference price")

        except Exception as e:
            logger.error(f"Error creating zones from peak/trough clusters: {str(e)}", exc_info=True)

    def add_peak_trough_cluster_zone(self, cluster_points, _=None, __=None, reference_price=None, cluster_id=None):
        """
        Add an orange zone box that encompasses a clustered peak/trough area.
        Unlike intersection zones, these zones do NOT extend to nearest peaks/troughs.
        They only span the actual clustered points and use orange color.

        Args:
            cluster_points: numpy array of (x, y) coordinates for all points in the cluster
            all_peaks: List of (x, y) tuples for all peaks in the density curves (unused for peak/trough zones)
            all_troughs: List of (x, y) tuples for all troughs in the density curves (unused for peak/trough zones)
            reference_price: Current price for determining zone position (for logging only)
            cluster_id: ID of the cluster for logging purposes
        """
        try:
            if len(cluster_points) == 0:
                logger.warning(f"No cluster points provided for peak/trough zone {cluster_id}")
                return

            # Find the y boundaries of the cluster points (no extension)
            min_y = np.min(cluster_points[:, 1])  # Bottom point (lowest)
            max_y = np.max(cluster_points[:, 1])  # Top point (highest)

            # Use the density gradient x-axis range
            min_x = 1.5
            max_x = 3.5

            # Determine cluster center for logging purposes
            cluster_center_y = (min_y + max_y) / 2

            # Peak/trough zones do NOT extend - they only span the actual cluster points
            zone_min_y = min_y
            zone_max_y = max_y

            # Calculate zone dimensions
            left = min_x
            bottom = zone_min_y
            box_width = max_x - min_x
            box_height = zone_max_y - zone_min_y

            # Ensure minimum height for visibility
            if box_height < 0.1:
                box_height = 0.1
                bottom = zone_min_y - 0.05

            # Create zone box using orange color (same as intersection zones)
            box_rect = pg.QtWidgets.QGraphicsRectItem(left, bottom, box_width, box_height)

            # Use orange color scheme for peak/trough zones (same as intersection zones)
            zone_pen = pg.mkPen(color='orange', width=2)
            box_rect.setPen(zone_pen)
            box_rect.setBrush(pg.mkBrush(color=(255, 165, 0, 30)))  # Light orange semi-transparent fill

            # Add the box to the plot
            self.plot_widget.addItem(box_rect)

            # Log zone creation with position info
            if reference_price:
                position = "above" if cluster_center_y > reference_price else "below"
            else:
                position = "unknown"
            logger.info(f"Added orange peak/trough cluster zone {cluster_id}: ({zone_min_y:.2f} to {zone_max_y:.2f}) - {position} reference price")

        except Exception as e:
            logger.error(f"Error adding peak/trough cluster zone: {str(e)}", exc_info=True)

    def create_zones_with_priority_ranking(self, orange_intersect_clusters=None, orange_intersect_non_clusters=None,
                                         red_intersect_clusters=None, red_intersect_non_clusters=None,
                                         peak_trough_clusters=None, peak_trough_non_clusters=None):
        """
        Create zones using the priority ranking system:
        #1: Orange Intersect cluster
        #2: Orange Intersect non-cluster
        #3: Red Intersect cluster
        #4: Red Intersect non-cluster
        #5: Peak/trough cluster
        #6: Peak/trough non-cluster

        The system works as a fallback ladder where you go down the priority list in order.
        Different priority levels can "cross together" to form zones, with non-clustered
        options only including the nearest elements.

        PRIORITY EXCLUSION RULE: Only the most important zone gets plotted in each area.
        For example, if both an "orange intersect cluster" and a "red intersect cluster"
        exist in the same area, only the orange intersect cluster zone is plotted since it has higher priority.
        NO COMBINING: Higher priority elements create standalone zones and exclude all lower priority elements.

        IMPORTANT: Zone boundaries are set at the exact prices of confluences (intersections,
        peaks, troughs) with no padding added. The top and bottom of each zone align precisely
        with the detected confluence points.

        Zones cannot be split by percentage bands - a zone cannot have a percent band inside of it.
        Maximum 1 zone above each 25% band and 1 zone below each 25% band.
        This limits it to 2 zones maximum on bullish side and 2 zones maximum on bearish side.

        Price-based extension rules:
        - If zone is above current price, it cannot be extended down past current price
        - If zone is below current price, it cannot be extended up past current price

        Args:
            orange_intersect_clusters: List of orange intersection cluster data
            orange_intersect_non_clusters: List of individual orange intersections
            red_intersect_clusters: List of red intersection cluster data
            red_intersect_non_clusters: List of individual red intersections
            peak_trough_clusters: List of peak/trough cluster data
            peak_trough_non_clusters: List of individual peaks/troughs
        """
        try:
            logger.info("Starting zone creation with priority ranking system")

            # Get reference price and band values for zone positioning
            reference_price_info = self.get_reference_price()
            if reference_price_info is None:
                logger.warning("Could not determine reference price for priority zone creation")
                return

            reference_price, _, _ = reference_price_info

            # Get band values for area determination
            band_values = self.get_band_values_for_zones()
            if not band_values:
                logger.warning("Could not get band values for priority zone creation")
                return

            # Define the four zone areas based on percentage bands
            zone_areas = self.define_zone_areas(reference_price, band_values)

            # Create zones for each area using priority ranking
            total_zones_created = 0
            for area_name, area_bounds in zone_areas.items():
                zones_in_area = self.create_zone_for_area(
                    area_name, area_bounds, reference_price,
                    orange_intersect_clusters, orange_intersect_non_clusters,
                    red_intersect_clusters, red_intersect_non_clusters,
                    peak_trough_clusters, peak_trough_non_clusters
                )
                total_zones_created += zones_in_area

            logger.info(f"Priority ranking system created {total_zones_created} zones across all areas")

        except Exception as e:
            logger.error(f"Error in priority ranking zone creation: {str(e)}", exc_info=True)

    def get_band_values_for_zones(self):
        """
        Get the current band values needed for zone area determination.

        Returns:
            dict: Dictionary containing band values or None if not available
        """
        try:
            band_values = {}

            # Get band values from instance attributes (set during density graph generation)
            band_values['highest_high'] = getattr(self, 'highest_high', None)
            band_values['maxavg_high'] = getattr(self, 'maxavg_high', None)  # 25% high band
            band_values['high_median'] = getattr(self, 'high_median', None)  # 50% high band
            band_values['low_median'] = getattr(self, 'low_median', None)    # 50% low band
            band_values['maxavg_low'] = getattr(self, 'maxavg_low', None)    # 25% low band
            band_values['lowest_low'] = getattr(self, 'lowest_low', None)

            # Check if we have the essential band values
            essential_bands = ['maxavg_high', 'high_median', 'low_median', 'maxavg_low']
            if all(band_values[band] is not None for band in essential_bands):
                logger.info("Successfully retrieved band values for zone creation")
                return band_values
            else:
                missing_bands = [band for band in essential_bands if band_values[band] is None]
                logger.warning(f"Missing essential band values: {missing_bands}")
                return None

        except Exception as e:
            logger.error(f"Error getting band values for zones: {str(e)}")
            return None

    def define_zone_areas(self, _, band_values):
        """
        Define the four zone areas based on percentage bands.

        Args:
            reference_price: Current/pivot price for reference
            band_values: Dictionary containing band values

        Returns:
            dict: Dictionary mapping area names to their bounds
        """
        try:
            zone_areas = {}

            # Above reference price areas
            if band_values['maxavg_high'] and band_values['highest_high']:
                zone_areas['above_green_25'] = {
                    'min_y': band_values['maxavg_high'],
                    'max_y': band_values['highest_high'],
                    'side': 'bullish'
                }

            if band_values['high_median'] and band_values['maxavg_high']:
                zone_areas['below_green_25'] = {
                    'min_y': band_values['high_median'],
                    'max_y': band_values['maxavg_high'],
                    'side': 'bullish'
                }

            # Below reference price areas
            if band_values['low_median'] and band_values['maxavg_low']:
                zone_areas['above_red_25'] = {
                    'min_y': band_values['maxavg_low'],
                    'max_y': band_values['low_median'],
                    'side': 'bearish'
                }

            if band_values['maxavg_low'] and band_values['lowest_low']:
                zone_areas['below_red_25'] = {
                    'min_y': band_values['lowest_low'],
                    'max_y': band_values['maxavg_low'],
                    'side': 'bearish'
                }

            logger.info(f"Defined {len(zone_areas)} zone areas: {list(zone_areas.keys())}")
            return zone_areas

        except Exception as e:
            logger.error(f"Error defining zone areas: {str(e)}")
            return {}

    def create_zone_for_area(self, area_name, area_bounds, reference_price,
                           orange_intersect_clusters, orange_intersect_non_clusters,
                           red_intersect_clusters, red_intersect_non_clusters,
                           peak_trough_clusters, peak_trough_non_clusters):
        """
        Create zones for a specific area using the priority ranking system.
        This method creates ALL types of zones that are detected in the area simultaneously,
        with the priority ranking determining which elements can combine together.

        Args:
            area_name: Name of the zone area
            area_bounds: Dictionary with min_y, max_y, and side
            reference_price: Current/pivot price for reference
            orange_intersect_clusters: List of orange intersection clusters
            orange_intersect_non_clusters: List of individual orange intersections
            red_intersect_clusters: List of red intersection clusters
            red_intersect_non_clusters: List of individual red intersections
            peak_trough_clusters: List of peak/trough clusters
            peak_trough_non_clusters: List of individual peaks/troughs

        Returns:
            int: Number of zones created in this area
        """
        try:
            logger.info(f"Creating zones for area '{area_name}' with bounds {area_bounds['min_y']:.2f} to {area_bounds['max_y']:.2f}")
            zones_created = 0

            # Check for all types of elements in this area
            found_elements = {}

            # Priority 1: Orange Intersect cluster
            orange_cluster = self.find_elements_in_area(orange_intersect_clusters, area_bounds, 'cluster')
            if orange_cluster:
                found_elements['orange_intersect_cluster'] = orange_cluster
                logger.info(f"Found Orange Intersect cluster in area '{area_name}' - Priority 1")

            # Priority 2: Orange Intersect non-cluster
            orange_non_cluster = self.find_elements_in_area(orange_intersect_non_clusters, area_bounds, 'individual')
            if orange_non_cluster:
                found_elements['orange_intersect_non_cluster'] = orange_non_cluster
                logger.info(f"Found Orange Intersect non-cluster in area '{area_name}' - Priority 2")

            # Priority 3: Red Intersect cluster
            red_cluster = self.find_elements_in_area(red_intersect_clusters, area_bounds, 'cluster')
            if red_cluster:
                found_elements['red_intersect_cluster'] = red_cluster
                logger.info(f"Found Red Intersect cluster in area '{area_name}' - Priority 3")

            # Priority 4: Red Intersect non-cluster
            red_non_cluster = self.find_elements_in_area(red_intersect_non_clusters, area_bounds, 'individual')
            if red_non_cluster:
                found_elements['red_intersect_non_cluster'] = red_non_cluster
                logger.info(f"Found Red Intersect non-cluster in area '{area_name}' - Priority 4")

            # Priority 5: Peak/trough cluster
            peak_trough_cluster = self.find_elements_in_area(peak_trough_clusters, area_bounds, 'cluster')
            if peak_trough_cluster:
                found_elements['peak_trough_cluster'] = peak_trough_cluster
                logger.info(f"Found Peak/trough cluster in area '{area_name}' - Priority 5")

            # Priority 6: Peak/trough non-cluster
            peak_trough_non_cluster = self.find_elements_in_area(peak_trough_non_clusters, area_bounds, 'individual')
            if peak_trough_non_cluster:
                found_elements['peak_trough_non_cluster'] = peak_trough_non_cluster
                logger.info(f"Found Peak/trough non-cluster in area '{area_name}' - Priority 6")

            if not found_elements:
                logger.info(f"No elements found for zone creation in area '{area_name}'")
                return 0

            # Create zones based on priority ranking and combination rules
            zones_created += self.create_priority_based_zones(found_elements, area_name, area_bounds, reference_price)

            return zones_created

        except Exception as e:
            logger.error(f"Error creating zones for area '{area_name}': {str(e)}")
            return 0

    def create_priority_based_zones(self, found_elements, area_name, area_bounds, reference_price):
        """
        Create zones based on STRICT priority ranking system. Only the highest priority element
        creates a zone, all lower priority elements are excluded.

        The priority ranking determines which element gets to create the zone:
        #1: Orange Intersect cluster (highest priority)
        #2: Orange Intersect non-cluster
        #3: Red Intersect cluster
        #4: Red Intersect non-cluster
        #5: Peak/trough cluster
        #6: Peak/trough non-cluster (lowest priority)

        STRICT EXCLUSION: Only the first available element type creates a zone.
        All other element types in the same area are excluded.

        Args:
            found_elements: Dictionary of element types found in the area
            area_name: Name of the zone area
            area_bounds: Dictionary with area bounds
            reference_price: Current/pivot price for reference

        Returns:
            int: Number of zones created
        """
        try:
            zones_created = 0

            # Define priority order
            priority_order = [
                'orange_intersect_cluster',
                'orange_intersect_non_cluster',
                'red_intersect_cluster',
                'red_intersect_non_cluster',
                'peak_trough_cluster',
                'peak_trough_non_cluster'
            ]

            # Apply strict zone limit: maximum 1 zone per area
            # This ensures we have max 2 zones on bullish side and 2 zones on bearish side
            max_zones_per_area = 1

            # Create zones starting from highest priority
            for primary_type in priority_order:
                if primary_type not in found_elements:
                    continue

                # Check zone limit before creating
                if zones_created >= max_zones_per_area:
                    logger.info(f"Reached maximum zone limit ({max_zones_per_area}) for area '{area_name}' - skipping lower priority {primary_type} (higher priority zone already exists)")
                    break

                primary_element = found_elements[primary_type]
                logger.info(f"Creating zone with primary element: {primary_type}")

                # Create zone elements structure - STRICT PRIORITY EXCLUSION
                # The highest priority element creates a standalone zone without combining with others
                zone_elements = {
                    'primary': primary_element,
                    'type': primary_type,
                    'combining': []  # No combining elements - strict priority exclusion
                }

                # PRIORITY EXCLUSION: The highest priority element gets its own zone
                # All lower priority elements in this area are excluded
                logger.info(f"Creating standalone {primary_type} zone - excluding all lower priority elements due to strict priority exclusion")

                # Create the zone visualization
                zone_created = self.create_zone_from_elements(zone_elements, area_name, area_bounds, reference_price)
                if zone_created:
                    zones_created += 1
                    logger.info(f"Successfully created zone {zones_created}/{max_zones_per_area} in area '{area_name}' - highest priority {primary_type} zone plotted")

                    # Log which elements were excluded due to priority system
                    excluded_elements = [elem_type for elem_type in found_elements.keys() if elem_type != primary_type]
                    if excluded_elements:
                        logger.info(f"EXCLUDED lower priority elements in area '{area_name}': {excluded_elements} (due to strict priority exclusion)")

                    # Since we only allow 1 zone per area, break after creating the first zone
                    # This ensures only the most important (highest priority) zone gets plotted
                    logger.info(f"Area '{area_name}' zone creation complete - strict priority exclusion enforced")
                    break

            logger.info(f"Created {zones_created} zones in area '{area_name}'")
            return zones_created

        except Exception as e:
            logger.error(f"Error creating priority-based zones: {str(e)}")
            return 0

    def find_elements_in_area(self, elements, area_bounds, element_type):
        """
        Find elements (clusters or individuals) that fall within the specified area bounds.

        Args:
            elements: List of elements to search through
            area_bounds: Dictionary with min_y and max_y bounds
            element_type: 'cluster' or 'individual'

        Returns:
            Element data if found, None otherwise
        """
        try:
            if not elements:
                return None

            min_y = area_bounds['min_y']
            max_y = area_bounds['max_y']

            for element in elements:
                if element_type == 'cluster':
                    # For clusters, check if cluster center is in area
                    if 'center_y' in element:
                        center_y = element['center_y']
                    elif 'points' in element:
                        # Calculate center from points
                        points = element['points']
                        if len(points) > 0:
                            center_y = np.mean([point[1] for point in points])
                        else:
                            continue
                    else:
                        continue

                    if min_y <= center_y <= max_y:
                        logger.debug(f"Found cluster with center {center_y:.2f} in area {min_y:.2f}-{max_y:.2f}")
                        return element

                else:  # individual
                    # For individuals, check if the point is in area
                    if isinstance(element, (list, tuple)) and len(element) >= 2:
                        y_pos = element[1]
                        if min_y <= y_pos <= max_y:
                            logger.debug(f"Found individual at {y_pos:.2f} in area {min_y:.2f}-{max_y:.2f}")
                            return element
                    elif isinstance(element, dict) and 'y' in element:
                        y_pos = element['y']
                        if min_y <= y_pos <= max_y:
                            logger.debug(f"Found individual at {y_pos:.2f} in area {min_y:.2f}-{max_y:.2f}")
                            return element

            return None

        except Exception as e:
            logger.error(f"Error finding elements in area: {str(e)}")
            return None

    def add_combining_elements(self, zone_elements, area_bounds,
                             orange_intersect_non_clusters, red_intersect_clusters,
                             red_intersect_non_clusters, peak_trough_clusters,
                             peak_trough_non_clusters):
        """
        Add lower priority elements that can combine with the primary zone element.
        For non-clustered options, only include the nearest elements.

        RESTRICTION: Different types of clusters cannot combine to form a zone.
        For example, a red intersection cluster and a peak cluster cannot make a zone together.
        Only clusters of the same base type (intersection vs peak/trough) can combine.

        Args:
            zone_elements: Dictionary containing primary zone element
            area_bounds: Dictionary with area bounds
            orange_intersect_non_clusters: List of individual orange intersections
            red_intersect_clusters: List of red intersection clusters
            red_intersect_non_clusters: List of individual red intersections
            peak_trough_clusters: List of peak/trough clusters
            peak_trough_non_clusters: List of individual peaks/troughs

        Returns:
            Updated zone_elements dictionary with combining elements
        """
        try:
            primary_element = zone_elements['primary']
            primary_type = zone_elements['type']

            # Get primary element position for distance calculations
            primary_y = self.get_element_center_y(primary_element, primary_type)
            if primary_y is None:
                return zone_elements

            zone_elements['combining'] = []

            # Determine the base type of the primary element for cluster type restrictions
            primary_base_type = self.get_base_element_type(primary_type)
            is_primary_cluster = 'cluster' in primary_type

            logger.info(f"Primary element type: {primary_type}, base type: {primary_base_type}, is cluster: {is_primary_cluster}")

            # Add elements in priority order (only those not already used as primary)
            # RESTRICTION: Only allow combining elements that are compatible with the primary type

            # Orange Intersect non-cluster (if not primary)
            if primary_type != 'orange_intersect_non_cluster' and orange_intersect_non_clusters:
                # Allow if primary is also intersection-based or if primary is non-cluster
                if primary_base_type == 'intersection' or not is_primary_cluster:
                    nearest = self.find_nearest_element_in_area(orange_intersect_non_clusters, area_bounds, primary_y, 'individual')
                    if nearest:
                        zone_elements['combining'].append({'element': nearest, 'type': 'orange_intersect_non_cluster'})
                        logger.info(f"Added orange intersect non-cluster to {primary_type} zone")
                else:
                    logger.info(f"Skipped orange intersect non-cluster - incompatible with {primary_type} cluster")

            # Red Intersect cluster (if not primary)
            if primary_type not in ['orange_intersect_cluster', 'red_intersect_cluster'] and red_intersect_clusters:
                # Only allow if primary is also intersection-based cluster
                if primary_base_type == 'intersection' and is_primary_cluster:
                    cluster = self.find_elements_in_area(red_intersect_clusters, area_bounds, 'cluster')
                    if cluster:
                        zone_elements['combining'].append({'element': cluster, 'type': 'red_intersect_cluster'})
                        logger.info(f"Added red intersect cluster to {primary_type} zone")
                else:
                    logger.info(f"Skipped red intersect cluster - incompatible with {primary_type} (different cluster types cannot combine)")

            # Red Intersect non-cluster (if not primary)
            if primary_type not in ['orange_intersect_cluster', 'orange_intersect_non_cluster', 'red_intersect_cluster', 'red_intersect_non_cluster'] and red_intersect_non_clusters:
                # Allow if primary is also intersection-based or if primary is non-cluster
                if primary_base_type == 'intersection' or not is_primary_cluster:
                    nearest = self.find_nearest_element_in_area(red_intersect_non_clusters, area_bounds, primary_y, 'individual')
                    if nearest:
                        zone_elements['combining'].append({'element': nearest, 'type': 'red_intersect_non_cluster'})
                        logger.info(f"Added red intersect non-cluster to {primary_type} zone")
                else:
                    logger.info(f"Skipped red intersect non-cluster - incompatible with {primary_type} cluster")

            # Peak/trough cluster (if not primary)
            if 'peak_trough' not in primary_type and peak_trough_clusters:
                # Only allow if primary is also peak/trough-based cluster
                if primary_base_type == 'peak_trough' and is_primary_cluster:
                    cluster = self.find_elements_in_area(peak_trough_clusters, area_bounds, 'cluster')
                    if cluster:
                        zone_elements['combining'].append({'element': cluster, 'type': 'peak_trough_cluster'})
                        logger.info(f"Added peak/trough cluster to {primary_type} zone")
                else:
                    logger.info(f"Skipped peak/trough cluster - incompatible with {primary_type} (different cluster types cannot combine)")

            # Peak/trough non-cluster (if not primary)
            if primary_type != 'peak_trough_non_cluster' and peak_trough_non_clusters:
                # Allow if primary is also peak/trough-based or if primary is non-cluster
                if primary_base_type == 'peak_trough' or not is_primary_cluster:
                    nearest = self.find_nearest_element_in_area(peak_trough_non_clusters, area_bounds, primary_y, 'individual')
                    if nearest:
                        zone_elements['combining'].append({'element': nearest, 'type': 'peak_trough_non_cluster'})
                        logger.info(f"Added peak/trough non-cluster to {primary_type} zone")
                else:
                    logger.info(f"Skipped peak/trough non-cluster - incompatible with {primary_type} cluster")

            if zone_elements['combining']:
                logger.info(f"Added {len(zone_elements['combining'])} combining elements to zone")
            else:
                logger.info(f"No compatible combining elements found for {primary_type} zone")

            return zone_elements

        except Exception as e:
            logger.error(f"Error adding combining elements: {str(e)}")
            return zone_elements

    def find_nearest_element_in_area(self, elements, area_bounds, reference_y, _):
        """
        Find the nearest individual element to a reference point within the area bounds.

        Args:
            elements: List of individual elements
            area_bounds: Dictionary with min_y and max_y bounds
            reference_y: Y-coordinate to find nearest element to
            element_type: 'individual' (for consistency)

        Returns:
            Nearest element if found, None otherwise
        """
        try:
            if not elements:
                return None

            min_y = area_bounds['min_y']
            max_y = area_bounds['max_y']

            nearest_element = None
            nearest_distance = float('inf')

            for element in elements:
                element_y = None

                if isinstance(element, (list, tuple)) and len(element) >= 2:
                    element_y = element[1]
                elif isinstance(element, dict) and 'y' in element:
                    element_y = element['y']
                else:
                    continue

                # Check if element is in area bounds
                if min_y <= element_y <= max_y:
                    distance = abs(element_y - reference_y)
                    if distance < nearest_distance:
                        nearest_distance = distance
                        nearest_element = element

            if nearest_element:
                logger.debug(f"Found nearest element at distance {nearest_distance:.2f}")

            return nearest_element

        except Exception as e:
            logger.error(f"Error finding nearest element in area: {str(e)}")
            return None

    def get_element_center_y(self, element, element_type):
        """
        Get the Y-coordinate center of an element.

        Args:
            element: The element (cluster or individual)
            element_type: Type of element

        Returns:
            float: Y-coordinate of element center, or None if not found
        """
        try:
            if 'cluster' in element_type:
                if isinstance(element, dict):
                    if 'center_y' in element:
                        return element['center_y']
                    elif 'points' in element:
                        points = element['points']
                        if len(points) > 0:
                            return np.mean([point[1] for point in points])
            else:  # individual
                if isinstance(element, (list, tuple)) and len(element) >= 2:
                    return element[1]
                elif isinstance(element, dict) and 'y' in element:
                    return element['y']

            return None

        except Exception as e:
            logger.error(f"Error getting element center Y: {str(e)}")
            return None

    def create_zone_from_elements(self, zone_elements, area_name, area_bounds, reference_price):
        """
        Create the actual zone visualization from the collected elements.

        Args:
            zone_elements: Dictionary containing primary and combining elements
            area_name: Name of the zone area
            area_bounds: Dictionary with area bounds
            reference_price: Current/pivot price for reference

        Returns:
            bool: True if zone was created successfully
        """
        try:
            primary_element = zone_elements['primary']
            primary_type = zone_elements['type']
            combining_elements = zone_elements.get('combining', [])

            # Calculate zone boundaries from all elements
            zone_bounds = self.calculate_zone_bounds(primary_element, primary_type, combining_elements, area_bounds)
            if not zone_bounds:
                logger.warning(f"Could not calculate zone bounds for area '{area_name}'")
                return False

            # Debug: Log element positions vs zone bounds
            self.debug_zone_element_positions(primary_element, primary_type, combining_elements, zone_bounds, area_name)

            # Ensure zone doesn't cross percentage bands and enforce price-based extension rules
            zone_bounds = self.enforce_band_boundaries(zone_bounds, area_bounds, reference_price)

            # Create the zone visualization
            zone_color = self.get_zone_color(primary_type)
            zone_created = self.create_priority_zone_visualization(zone_bounds, zone_color, area_name, primary_type, combining_elements, primary_element)

            if zone_created:
                logger.info(f"Successfully created {primary_type} zone in area '{area_name}' with {len(combining_elements)} combining elements")
                return True
            else:
                logger.warning(f"Failed to create zone visualization for area '{area_name}'")
                return False

        except Exception as e:
            logger.error(f"Error creating zone from elements: {str(e)}")
            return False

    def calculate_zone_bounds(self, primary_element, primary_type, combining_elements, _):
        """
        Calculate the boundaries for a zone based on primary and combining elements.

        Args:
            primary_element: The primary zone element
            primary_type: Type of primary element
            combining_elements: List of combining elements
            area_bounds: Dictionary with area bounds

        Returns:
            dict: Zone bounds with min_y, max_y, min_x, max_x
        """
        try:
            # Start with primary element bounds
            if 'cluster' in primary_type:
                if isinstance(primary_element, dict) and 'points' in primary_element:
                    points = primary_element['points']
                    min_y = min(point[1] for point in points)
                    max_y = max(point[1] for point in points)
                    min_x = min(point[0] for point in points)
                    max_x = max(point[0] for point in points)
                else:
                    # Fallback for cluster without points
                    center_y = self.get_element_center_y(primary_element, primary_type)
                    if center_y is None:
                        return None
                    min_y = max_y = center_y
                    min_x, max_x = 1.5, 3.5  # Default x range
            else:  # individual
                element_y = self.get_element_center_y(primary_element, primary_type)
                if element_y is None:
                    return None
                min_y = max_y = element_y

                # For individuals, use default x range or extract from element
                if isinstance(primary_element, (list, tuple)) and len(primary_element) >= 2:
                    element_x = primary_element[0]
                    min_x = max_x = element_x
                else:
                    min_x, max_x = 1.5, 3.5  # Default x range

            # Expand bounds to include combining elements
            for combining in combining_elements:
                element = combining['element']
                element_type = combining['type']

                if 'cluster' in element_type:
                    if isinstance(element, dict) and 'points' in element:
                        points = element['points']
                        min_y = min(min_y, min(point[1] for point in points))
                        max_y = max(max_y, max(point[1] for point in points))
                        min_x = min(min_x, min(point[0] for point in points))
                        max_x = max(max_x, max(point[0] for point in points))
                else:  # individual
                    element_y = self.get_element_center_y(element, element_type)
                    if element_y is not None:
                        min_y = min(min_y, element_y)
                        max_y = max(max_y, element_y)

                        if isinstance(element, (list, tuple)) and len(element) >= 2:
                            element_x = element[0]
                            min_x = min(min_x, element_x)
                            max_x = max(max_x, element_x)

            # IMPORTANT: Zone boundaries must be at the exact price of confluences
            # No padding is added - the top and bottom of zones are at the exact confluence prices
            # This ensures zones align precisely with the detected confluence points

            # No minimum zone size requirements - zones should be exactly the size needed

            # Use full density gradient x-range for zone width
            min_x = 1.5
            max_x = 3.5

            logger.debug(f"Calculated zone bounds (exact confluence prices): Y({min_y:.3f} to {max_y:.3f}), X({min_x:.1f} to {max_x:.1f})")

            return {
                'min_y': min_y,
                'max_y': max_y,
                'min_x': min_x,
                'max_x': max_x
            }

        except Exception as e:
            logger.error(f"Error calculating zone bounds: {str(e)}")
            return None

    def enforce_band_boundaries(self, zone_bounds, area_bounds, reference_price=None):
        """
        Ensure zone doesn't cross percentage band boundaries and enforce price-based extension rules.

        Rules:
        - Zones cannot cross percentage bands
        - If zone is above current price, it cannot be extended down past current price
        - If zone is below current price, it cannot be extended up past current price

        Args:
            zone_bounds: Dictionary with zone bounds
            area_bounds: Dictionary with area bounds
            reference_price: Current/pivot price for extension constraints

        Returns:
            dict: Adjusted zone bounds
        """
        try:
            # Constrain zone to area bounds to prevent crossing bands
            zone_bounds['min_y'] = max(zone_bounds['min_y'], area_bounds['min_y'])
            zone_bounds['max_y'] = min(zone_bounds['max_y'], area_bounds['max_y'])

            # Apply price-based extension constraints
            if reference_price is not None:
                zone_center_y = (zone_bounds['min_y'] + zone_bounds['max_y']) / 2

                if zone_center_y > reference_price:
                    # Zone is above current price - cannot extend down past current price
                    if zone_bounds['min_y'] < reference_price:
                        zone_bounds['min_y'] = reference_price
                        logger.info(f"Zone above current price: constrained min_y to reference price {reference_price:.2f}")

                elif zone_center_y < reference_price:
                    # Zone is below current price - cannot extend up past current price
                    if zone_bounds['max_y'] > reference_price:
                        zone_bounds['max_y'] = reference_price
                        logger.info(f"Zone below current price: constrained max_y to reference price {reference_price:.2f}")

            # No minimum zone size requirements - zones should be exactly the size needed
            # Zones will be the exact size of their elements plus small padding

            return zone_bounds

        except Exception as e:
            logger.error(f"Error enforcing band boundaries: {str(e)}")
            return zone_bounds

    def get_zone_color(self, primary_type):
        """
        Get the color for a zone based on its primary element type.

        Args:
            primary_type: Type of primary element

        Returns:
            str: Color name for the zone
        """
        if 'orange_intersect' in primary_type:
            return 'orange'
        elif 'red_intersect' in primary_type:
            return 'red'
        elif 'peak_trough' in primary_type:
            return 'orange'  # User preference: orange for peak/trough zones instead of purple
        else:
            return 'orange'  # Default fallback

    def create_priority_zone_visualization(self, zone_bounds, zone_color, area_name, zone_type, combining_elements=None, primary_element=None):
        """
        Create the visual editable zone rectangle on the plot.

        Args:
            zone_bounds: Dictionary with zone bounds
            zone_color: Color for the zone
            area_name: Name of the zone area
            zone_type: Type of zone for logging
            combining_elements: List of combining elements for label generation
            primary_element: Primary element data for peak/trough determination

        Returns:
            bool: True if zone was created successfully
        """
        try:
            # Check if a zone with similar bounds already exists to prevent duplicates
            for existing_zone in self.density_zones:
                if (abs(existing_zone.zone_bounds['min_y'] - zone_bounds['min_y']) < 0.01 and
                    abs(existing_zone.zone_bounds['max_y'] - zone_bounds['max_y']) < 0.01 and
                    existing_zone.zone_type == zone_type and
                    existing_zone.area_name == area_name):
                    logger.warning(f"Duplicate zone detected - skipping creation of {zone_type} in {area_name}")
                    return True  # Return success to avoid error handling

            # Create editable density zone
            zone_id = self.zone_id_counter
            self.zone_id_counter += 1

            editable_zone = EditableDensityZone(
                zone_id=zone_id,
                zone_bounds=zone_bounds,
                zone_color=zone_color,
                zone_type=zone_type,
                area_name=area_name,
                primary_element=primary_element,
                combining_elements=combining_elements or [],
                density_graph_tab=self
            )

            # Add zone to plot
            self.plot_widget.addItem(editable_zone)

            # Store zone reference for management
            self.density_zones.append(editable_zone)

            # Add text label to the right of the zone describing what it's made of
            self.add_zone_label(zone_bounds, zone_type, area_name, combining_elements, primary_element)

            logger.info(f"Created editable {zone_color} {zone_type} zone {zone_id} in area '{area_name}': "
                       f"({zone_bounds['min_y']:.2f} to {zone_bounds['max_y']:.2f}) - boundaries at exact confluence prices")
            logger.debug(f"Total zones now: {len(self.density_zones)}")

            # Notify parent about zone creation for save button visibility
            self.notify_parent_zones_updated()

            return True

        except Exception as e:
            logger.error(f"Error creating editable zone visualization: {str(e)}")
            return False

    def add_zone_label(self, zone_bounds, primary_type, _, combining_elements=None, primary_element=None):
        """
        Add a text label to the right of the zone describing what elements it's made of.

        Args:
            zone_bounds: Dictionary with zone bounds
            primary_type: Type of primary element
            area_name: Name of the zone area
            combining_elements: List of combining elements
            primary_element: Primary element data for peak/trough determination
        """
        try:
            # Generate label text based on zone composition
            label_text = self.generate_zone_label_text(primary_type, combining_elements, primary_element)

            # Position label to the right of the zone
            label_x = zone_bounds['max_x'] + 0.1  # Small offset to the right
            label_y = (zone_bounds['min_y'] + zone_bounds['max_y']) / 2  # Center vertically

            # Create text item
            text_item = pg.TextItem(
                text=label_text,
                color=(255, 255, 255),  # White text
                anchor=(0, 0.5)  # Left-aligned, vertically centered
            )

            # Set font size
            text_item.setFont(pg.QtGui.QFont("Arial", 8))

            # Position the text
            text_item.setPos(label_x, label_y)

            # Add to plot
            self.plot_widget.addItem(text_item)

            logger.info(f"Added zone label '{label_text}' at ({label_x:.2f}, {label_y:.2f})")

        except Exception as e:
            logger.error(f"Error adding zone label: {str(e)}")

    def generate_zone_label_text(self, primary_type, combining_elements=None, primary_element=None):
        """
        Generate descriptive text for a zone based on its composition.
        Since zones are either made from clusters OR non-clusters (not mixed),
        we determine the cluster/non-cluster status from the primary element.

        Args:
            primary_type: Type of primary element
            combining_elements: List of combining elements
            primary_element: Primary element data for peak/trough determination

        Returns:
            str: Descriptive text for the zone
        """
        try:
            # Determine if this is a cluster-based zone or non-cluster zone
            is_cluster_zone = 'cluster' in primary_type

            # Get base element types (without cluster/non-cluster specification)
            element_types = set()

            # Add primary element display type (pass element data for peak/trough determination)
            primary_display = self.get_display_element_type(primary_type, primary_element)
            element_types.add(primary_display)

            # Add combining elements display types
            if combining_elements:
                for combining in combining_elements:
                    element_type = combining['type']
                    element_data = combining.get('element')
                    display_type = self.get_display_element_type(element_type, element_data)
                    element_types.add(display_type)

            # Convert to sorted list for consistent ordering
            element_list = sorted(list(element_types))

            # Create label with cluster/non-cluster specification
            if is_cluster_zone:
                if len(element_list) == 1:
                    return f"{element_list[0]} cluster"
                else:
                    return f"{' + '.join(element_list)} cluster"
            else:
                return " + ".join(element_list)

        except Exception as e:
            logger.error(f"Error generating zone label text: {str(e)}")
            return "zone"

    def get_base_element_type(self, element_type):
        """
        Extract the base element type (without cluster/non-cluster specification).
        Returns technical base types for compatibility checking.

        Args:
            element_type: Technical element type string

        Returns:
            str: Base element type for compatibility checking
        """
        try:
            # Map technical types to base types for compatibility checking
            # Intersection types can combine with each other
            # Peak/trough types can only combine with other peak/trough types
            base_type_mapping = {
                'orange_intersect_cluster': 'intersection',
                'orange_intersect_non_cluster': 'intersection',
                'red_intersect_cluster': 'intersection',
                'red_intersect_non_cluster': 'intersection',
                'peak_trough_cluster': 'peak_trough',
                'peak_trough_non_cluster': 'peak_trough'
            }

            return base_type_mapping.get(element_type, element_type)

        except Exception as e:
            logger.error(f"Error getting base element type: {str(e)}")
            return element_type

    def get_display_element_type(self, element_type, element_data=None):
        """
        Extract the display element type (without cluster/non-cluster specification).
        Returns user-friendly display names for zone labels.
        For peak/trough clusters, determines if it's primarily peaks or troughs.

        Args:
            element_type: Technical element type string
            element_data: Optional element data for determining peak vs trough clusters

        Returns:
            str: Display element type for zone labels
        """
        try:
            # Handle peak/trough clusters with specific peak vs trough determination
            if element_type in ['peak_trough_cluster', 'peak_trough_non_cluster']:
                if element_data is not None:
                    # For clusters, check peak_count vs trough_count
                    if element_type == 'peak_trough_cluster' and isinstance(element_data, dict):
                        peak_count = element_data.get('peak_count', 0)
                        trough_count = element_data.get('trough_count', 0)

                        if peak_count > trough_count:
                            return 'peak'
                        elif trough_count > peak_count:
                            return 'trough'
                        else:
                            return 'peak/trough'  # Equal counts or both zero

                    # For non-clusters (individual points), check the point type
                    elif element_type == 'peak_trough_non_cluster':
                        # element_data should be a tuple (x, y, type) for individual points
                        if isinstance(element_data, (list, tuple)) and len(element_data) >= 3:
                            point_type = element_data[2]
                            if point_type == 'peak':
                                return 'peak'
                            elif point_type == 'trough':
                                return 'trough'

                # Fallback for peak/trough types when data is not available
                return 'peak/trough'

            # Map other technical types to display names for zone labels
            display_type_mapping = {
                'orange_intersect_cluster': 'orange intersect',
                'orange_intersect_non_cluster': 'orange intersect',
                'red_intersect_cluster': 'red intersect',
                'red_intersect_non_cluster': 'red intersect'
            }

            return display_type_mapping.get(element_type, element_type)

        except Exception as e:
            logger.error(f"Error getting display element type: {str(e)}")
            return element_type



    def debug_zone_element_positions(self, primary_element, primary_type, combining_elements, zone_bounds, area_name):
        """
        Debug method to log element positions vs zone bounds to help diagnose zone positioning issues.

        Args:
            primary_element: The primary zone element
            primary_type: Type of primary element
            combining_elements: List of combining elements
            zone_bounds: Calculated zone bounds
            area_name: Name of the zone area
        """
        try:
            logger.info(f"=== Zone Debug for area '{area_name}' ===")
            logger.info(f"Zone bounds: Y({zone_bounds['min_y']:.3f} to {zone_bounds['max_y']:.3f})")

            # Log primary element positions
            if 'cluster' in primary_type:
                if isinstance(primary_element, dict) and 'points' in primary_element:
                    points = primary_element['points']
                    y_positions = [point[1] for point in points]
                    logger.info(f"Primary {primary_type} cluster Y positions: {[f'{y:.3f}' for y in y_positions]}")
                    logger.info(f"Primary cluster Y range: {min(y_positions):.3f} to {max(y_positions):.3f}")
                else:
                    center_y = self.get_element_center_y(primary_element, primary_type)
                    logger.info(f"Primary {primary_type} center Y: {center_y:.3f}")
            else:
                element_y = self.get_element_center_y(primary_element, primary_type)
                logger.info(f"Primary {primary_type} Y position: {element_y:.3f}")

            # Log combining element positions
            for combining in combining_elements:
                element = combining['element']
                element_type = combining['type']

                if 'cluster' in element_type:
                    if isinstance(element, dict) and 'points' in element:
                        points = element['points']
                        y_positions = [point[1] for point in points]
                        logger.info(f"Combining {element_type} cluster Y positions: {[f'{y:.3f}' for y in y_positions]}")
                        logger.info(f"Combining cluster Y range: {min(y_positions):.3f} to {max(y_positions):.3f}")
                    else:
                        center_y = self.get_element_center_y(element, element_type)
                        logger.info(f"Combining {element_type} center Y: {center_y:.3f}")
                else:
                    element_y = self.get_element_center_y(element, element_type)
                    logger.info(f"Combining {element_type} Y position: {element_y:.3f}")

            logger.info(f"=== End Zone Debug ===")

        except Exception as e:
            logger.error(f"Error in zone debug logging: {str(e)}")

    def get_non_clustered_peaks_troughs(self, _, __, cluster_labels, points_array, point_types):
        """
        Get peaks and troughs that are not part of any cluster (noise points).

        Args:
            all_valid_peaks: List of (x, y) tuples for valid peaks
            all_valid_troughs: List of (x, y) tuples for valid troughs
            cluster_labels: Array of cluster labels from DBSCAN
            points_array: Array of all points used in clustering
            point_types: List of point types ('peak' or 'trough')

        Returns:
            list: List of non-clustered peak/trough points
        """
        try:
            non_clustered = []

            # Find noise points (cluster label -1)
            for i, label in enumerate(cluster_labels):
                if label == -1:  # Noise point (not in any cluster)
                    point = points_array[i]
                    point_type = point_types[i]
                    non_clustered.append({
                        'x': point[0],
                        'y': point[1],
                        'type': point_type
                    })

            logger.info(f"Found {len(non_clustered)} non-clustered peaks/troughs")
            return non_clustered

        except Exception as e:
            logger.error(f"Error getting non-clustered peaks/troughs: {str(e)}")
            return []

    def create_zones_with_priority_ranking_if_ready(self):
        """
        Create zones using priority ranking system if all required data is available.
        This method coordinates between intersection and peak/trough clustering results.
        """
        try:
            # Check if we have intersection data (set during intersection processing)
            orange_intersect_clusters = getattr(self, 'orange_intersect_clusters', None)
            orange_intersect_non_clusters = getattr(self, 'orange_intersect_non_clusters', None)
            red_intersect_clusters = getattr(self, 'red_intersect_clusters', None)
            red_intersect_non_clusters = getattr(self, 'red_intersect_non_clusters', None)

            # Check if we have peak/trough data (set during peak/trough processing)
            peak_trough_clusters = getattr(self, 'peak_trough_clusters', None)
            peak_trough_non_clusters = getattr(self, 'peak_trough_non_clusters', None)

            # Only proceed if we have at least some data
            has_intersection_data = any([orange_intersect_clusters, orange_intersect_non_clusters,
                                       red_intersect_clusters, red_intersect_non_clusters])
            has_peak_trough_data = any([peak_trough_clusters, peak_trough_non_clusters])

            if not (has_intersection_data or has_peak_trough_data):
                logger.info("No data available yet for priority ranking zone creation")
                return

            logger.info("Creating zones with priority ranking system")

            # Create zones using the priority ranking system
            self.create_zones_with_priority_ranking(
                orange_intersect_clusters=orange_intersect_clusters,
                orange_intersect_non_clusters=orange_intersect_non_clusters,
                red_intersect_clusters=red_intersect_clusters,
                red_intersect_non_clusters=red_intersect_non_clusters,
                peak_trough_clusters=peak_trough_clusters,
                peak_trough_non_clusters=peak_trough_non_clusters
            )

        except Exception as e:
            logger.error(f"Error in priority ranking coordination: {str(e)}")

    def store_intersection_data_for_priority_ranking(self, orange_clusters, orange_non_clusters,
                                                   red_clusters, red_non_clusters):
        """
        Store intersection clustering results for use in priority ranking system.

        Args:
            orange_clusters: List of orange intersection clusters
            orange_non_clusters: List of individual orange intersections
            red_clusters: List of red intersection clusters
            red_non_clusters: List of individual red intersections
        """
        try:
            self.orange_intersect_clusters = orange_clusters
            self.orange_intersect_non_clusters = orange_non_clusters
            self.red_intersect_clusters = red_clusters
            self.red_intersect_non_clusters = red_non_clusters

            logger.info(f"Stored intersection data: {len(orange_clusters or [])} orange clusters, "
                       f"{len(orange_non_clusters or [])} orange non-clusters, "
                       f"{len(red_clusters or [])} red clusters, "
                       f"{len(red_non_clusters or [])} red non-clusters")

            # Try to create zones if peak/trough data is also ready
            self.create_zones_with_priority_ranking_if_ready()

        except Exception as e:
            logger.error(f"Error storing intersection data: {str(e)}")

    def get_non_clustered_orange_intersections(self, intersections, cluster_labels):
        """
        Get orange intersections that are not part of any cluster (noise points).

        Args:
            intersections: List of (x, y, color, dot_reference) tuples
            cluster_labels: Array of cluster labels from DBSCAN

        Returns:
            list: List of non-clustered orange intersection points
        """
        try:
            non_clustered = []

            # Find noise points (cluster label -1)
            for i, label in enumerate(cluster_labels):
                if label == -1:  # Noise point (not in any cluster)
                    if i < len(intersections):
                        x, y, color, dot_ref = intersections[i]
                        non_clustered.append({
                            'x': x,
                            'y': y,
                            'color': color,
                            'dot_ref': dot_ref
                        })

            logger.info(f"Found {len(non_clustered)} non-clustered orange intersections")
            return non_clustered

        except Exception as e:
            logger.error(f"Error getting non-clustered orange intersections: {str(e)}")
            return []

    def get_non_clustered_red_intersections(self, red_intersections, cluster_labels):
        """
        Get red intersections that are not part of any cluster (noise points).

        Args:
            red_intersections: List of (x, y, color, dot_reference) tuples
            cluster_labels: Array of cluster labels from DBSCAN

        Returns:
            list: List of non-clustered red intersection points
        """
        try:
            non_clustered = []

            # Find noise points (cluster label -1)
            for i, label in enumerate(cluster_labels):
                if label == -1:  # Noise point (not in any cluster)
                    if i < len(red_intersections):
                        x, y, color, dot_ref = red_intersections[i]
                        non_clustered.append({
                            'x': x,
                            'y': y,
                            'color': color,
                            'dot_ref': dot_ref
                        })

            logger.info(f"Found {len(non_clustered)} non-clustered red intersections")
            return non_clustered

        except Exception as e:
            logger.error(f"Error getting non-clustered red intersections: {str(e)}")
            return []

    def add_density_ray_sidebar_only(self, price_level, ray_type, description, color='gray'):
        """
        Add small line markers at the specified price level for density zones.
        Creates ONLY right-side markers (no on-peak/on-trough markers).

        Args:
            price_level: The Y-coordinate (price) where to draw the marker
            ray_type: Type of marker ('Peak', 'Trough', 'Intersection')
            description: Description for the marker
            color: Color of the marker (default: 'gray')
        """
        try:
            # Get the current plot range to determine marker positions
            view_range = self.plot_widget.getViewBox().viewRange()
            x_min, x_max = view_range[0]

            # Calculate small line length (2% of chart width)
            line_length = (x_max - x_min) * 0.02

            # Create right-side line marker
            right_side_x = x_max - (x_max - x_min) * 0.05  # 5% from right edge
            right_line_start = right_side_x - line_length / 2
            right_line_end = right_side_x + line_length / 2

            right_marker = pg.PlotDataItem(
                x=[right_line_start, right_line_end],
                y=[price_level, price_level],
                pen=pg.mkPen(color=color, width=3, style=pg.QtCore.Qt.PenStyle.SolidLine),  # Slightly thicker for cluster markers
                name=f"{description} (Cluster)"
            )

            # Add the right-side marker to the plot
            self.plot_widget.addItem(right_marker)

            logger.debug(f"Added {color} cluster {ray_type} sidebar marker at price {price_level:.2f}: {description}")

        except Exception as e:
            logger.error(f"Error adding density ray sidebar marker: {str(e)}", exc_info=True)

    def add_side_cross_vertical_lines(self, highest_peak_y, highest_peak_x, lowest_trough_y, lowest_trough_x):
        """
        Add vertical zones for side cross classification.
        Creates red semi-transparent vertical zones around the highest peak and lowest trough.

        Price-based restrictions:
        - Left red zone (around lowest trough) only works below current price
        - Right red zone (around highest peak) only works above current price

        Args:
            highest_peak_y: Y-coordinate of the highest peak
            highest_peak_x: X-coordinate of the highest peak
            lowest_trough_y: Y-coordinate of the lowest trough
            lowest_trough_x: X-coordinate of the lowest trough
        """
        try:
            logger.info("Adding side cross vertical zones with price-based restrictions")

            # Get the current reference price for zone restrictions
            reference_price_info = self.get_reference_price()
            if reference_price_info is None:
                logger.warning("Could not get reference price for zone restrictions, using full zones")
                current_price = None
            else:
                current_price, _, _ = reference_price_info
                logger.info(f"Using reference price {current_price:.2f} for zone restrictions")

            # Get the plot range to determine zone height
            view_range = self.plot_widget.getViewBox().viewRange()
            y_min, y_max = view_range[1]

            # Define zone width (reasonable width around the peak/trough)
            zone_width = 0.3  # Small, reasonably sized zones

            # Store zone boundaries for intersection coloring
            self.side_cross_zones = []

            # Add vertical zone around the highest peak (RIGHT zone - only above current price)
            if highest_peak_y is not None and highest_peak_x is not None:
                # Create zone boundaries
                zone_left = highest_peak_x - zone_width / 2
                zone_right = highest_peak_x + zone_width / 2

                # Determine zone height based on current price restriction
                if current_price is not None:
                    # Right zone only works above current price
                    zone_y_min = max(current_price, y_min)
                    zone_height = y_max - zone_y_min
                    logger.info(f"Right zone (peak) restricted to above current price {current_price:.2f}")
                else:
                    # Fallback to full height if no current price available
                    zone_y_min = y_min
                    zone_height = y_max - y_min
                    logger.info("Right zone (peak) using full height (no current price available)")

                # Store zone boundaries for intersection coloring (include price restriction)
                self.side_cross_zones.append({
                    'left': zone_left,
                    'right': zone_right,
                    'type': 'peak',
                    'center_x': highest_peak_x,
                    'center_y': highest_peak_y,
                    'price_restriction': 'above',
                    'reference_price': current_price
                })

                # Create filled rectangle for the zone (barely visible)
                peak_zone = pg.QtWidgets.QGraphicsRectItem(zone_left, zone_y_min, zone_width, zone_height)
                peak_zone.setBrush(pg.mkBrush(color=(255, 0, 0, 10)))  # Red with barely visible opacity (10/255 ≈ 0.04)
                peak_zone.setPen(pg.mkPen(color=(255, 0, 0, 30), width=1, style=QtCore.Qt.PenStyle.DashLine))  # Very faint red border
                self.plot_widget.addItem(peak_zone)

                logger.info(f"Added right vertical zone around highest peak at x={highest_peak_x:.2f}, y={highest_peak_y:.2f} (zone: {zone_left:.2f} to {zone_right:.2f}, y: {zone_y_min:.2f} to {y_max:.2f})")

            # Add vertical zone around the lowest trough (LEFT zone - only below current price)
            if lowest_trough_y is not None and lowest_trough_x is not None:
                # Create zone boundaries
                zone_left = lowest_trough_x - zone_width / 2
                zone_right = lowest_trough_x + zone_width / 2

                # Determine zone height based on current price restriction
                if current_price is not None:
                    # Left zone only works below current price
                    zone_y_max = min(current_price, y_max)
                    zone_height = zone_y_max - y_min
                    logger.info(f"Left zone (trough) restricted to below current price {current_price:.2f}")
                else:
                    # Fallback to full height if no current price available
                    zone_y_max = y_max
                    zone_height = y_max - y_min
                    logger.info("Left zone (trough) using full height (no current price available)")

                # Store zone boundaries for intersection coloring (include price restriction)
                self.side_cross_zones.append({
                    'left': zone_left,
                    'right': zone_right,
                    'type': 'trough',
                    'center_x': lowest_trough_x,
                    'center_y': lowest_trough_y,
                    'price_restriction': 'below',
                    'reference_price': current_price
                })

                # Create filled rectangle for the zone (barely visible)
                trough_zone = pg.QtWidgets.QGraphicsRectItem(zone_left, y_min, zone_width, zone_height)
                trough_zone.setBrush(pg.mkBrush(color=(255, 0, 0, 10)))  # Red with barely visible opacity (10/255 ≈ 0.04)
                trough_zone.setPen(pg.mkPen(color=(255, 0, 0, 30), width=1, style=QtCore.Qt.PenStyle.DashLine))  # Very faint red border
                self.plot_widget.addItem(trough_zone)

                logger.info(f"Added left vertical zone around lowest trough at x={lowest_trough_x:.2f}, y={lowest_trough_y:.2f} (zone: {zone_left:.2f} to {zone_right:.2f}, y: {y_min:.2f} to {zone_y_max:.2f})")

            if highest_peak_y is None and lowest_trough_y is None:
                logger.info("No peaks or troughs found for side cross vertical zones")

        except Exception as e:
            logger.error(f"Error adding side cross vertical zones: {str(e)}", exc_info=True)

    def create_wave_segment_gradient(self, all_curves):
        """
        Create gradient effect using 25% transparency boxes for each density wave segment.
        Data is processed only from x=2 to x=3, but visual boxes span from x=1.5 to x=3.5.
        These boxes stack to create the gradient.
        Also calculates and displays density scores from 0-20 based on wave overlap intensity.

        Args:
            all_curves: List of curve data tuples (x_smooth, y_smooth, combined_distance, is_lowest_low, is_highest_high)
        """
        try:
            logger.info("Creating wave segment gradient with 25% transparency boxes that stack for density effect")
            logger.info(f"Processing {len(all_curves)} curves for wave segment gradient")

            # Define the x-range between the vertical lines for data processing
            x_start = 2.0
            x_end = 3.0

            # Use grey color with 25% transparency for each wave segment
            # Higher transparency makes the gradient brighter while still allowing stacking when waves overlap
            grey_color = QtGui.QColor('#808080')  # Standard grey color
            transparency = int(255 * 0.25)  # 25% transparency (63.75 out of 255)
            grey_color.setAlpha(transparency)

            wave_count = 0
            # Store wave segments for density calculation
            wave_segments = []

            # Process each density wave curve
            for x_smooth, y_smooth, _, _, _ in all_curves:
                try:
                    # Extract only the part of the curve between x=2 and x=3
                    segment_x, segment_y = self.extract_curve_segment(x_smooth, y_smooth, x_start, x_end)

                    if len(segment_x) < 2 or len(segment_y) < 2:
                        continue  # Skip if not enough points in the segment

                    # Store segment data for density calculation
                    wave_segments.append((segment_x, segment_y))

                    # Create a filled polygon for this wave segment
                    # The polygon will be bounded by the curve on top and a baseline at the bottom
                    self.create_wave_segment_box(segment_x, segment_y, grey_color)
                    wave_count += 1

                except Exception as e:
                    logger.warning(f"Error processing wave segment: {str(e)}")
                    continue

            logger.info(f"Created {wave_count} wave segment boxes for gradient effect")

        except Exception as e:
            logger.error(f"Error creating wave segment gradient: {str(e)}", exc_info=True)

    def extract_curve_segment(self, x_data, y_data, x_start, x_end):
        """
        Extract the portion of a curve that falls between x_start and x_end.

        Args:
            x_data: Array of x coordinates for the curve
            y_data: Array of y coordinates for the curve
            x_start: Starting x coordinate (2.0)
            x_end: Ending x coordinate (3.0)

        Returns:
            tuple: (segment_x, segment_y) arrays for the curve segment
        """
        try:
            x_data = np.array(x_data)
            y_data = np.array(y_data)

            # Find indices where x values are within the range
            mask = (x_data >= x_start) & (x_data <= x_end)

            if not np.any(mask):
                return np.array([]), np.array([])

            # Extract the segment
            segment_x = x_data[mask]
            segment_y = y_data[mask]

            # If we don't have points exactly at the boundaries, interpolate them
            if len(segment_x) > 0:
                # Add boundary points if needed
                if segment_x[0] > x_start:
                    # Interpolate y value at x_start
                    y_start_interp = np.interp(x_start, x_data, y_data)
                    segment_x = np.concatenate([[x_start], segment_x])
                    segment_y = np.concatenate([[y_start_interp], segment_y])

                if segment_x[-1] < x_end:
                    # Interpolate y value at x_end
                    y_end_interp = np.interp(x_end, x_data, y_data)
                    segment_x = np.concatenate([segment_x, [x_end]])
                    segment_y = np.concatenate([segment_y, [y_end_interp]])

            return segment_x, segment_y

        except Exception as e:
            logger.warning(f"Error extracting curve segment: {str(e)}")
            return np.array([]), np.array([])

    def create_wave_segment_box(self, segment_x, segment_y, color):
        """
        Create vertical boxes that extend from the bottom to the top of each wave.
        The box spans from x=1.5 to x=3.5 for aesthetic purposes, but uses
        y-values calculated from the restricted x=2 to x=3 wave segment data.
        When multiple waves overlap, the 25% transparency stacks to create a denser gradient effect.

        Args:
            segment_x: Array of x coordinates for the segment (between x=2 and x=3)
            segment_y: Array of y coordinates for the segment (between x=2 and x=3)
            color: QColor with 25% transparency already set
        """
        try:
            if len(segment_x) < 2 or len(segment_y) < 2:
                return

            # Find the minimum and maximum y-values for this wave segment (from x=2 to x=3 data)
            y_min = min(segment_y)
            y_max = max(segment_y)

            # Define the aesthetic x-range for visual spanning (x=1.5 to x=3.5)
            aesthetic_x_start = 1.5
            aesthetic_x_end = 3.5

            # Create a vertical box that spans from x=1.5 to x=3.5 but uses y-values from the restricted segment
            polygon_points = []

            # Create the top edge of the box (left to right at y_max from x=1.5 to x=3.5)
            polygon_points.append(QtCore.QPointF(aesthetic_x_start, y_max))
            polygon_points.append(QtCore.QPointF(aesthetic_x_end, y_max))

            # Create the bottom edge of the box (right to left at y_min from x=3.5 to x=1.5)
            polygon_points.append(QtCore.QPointF(aesthetic_x_end, y_min))
            polygon_points.append(QtCore.QPointF(aesthetic_x_start, y_min))

            # Create the polygon
            polygon = QtGui.QPolygonF(polygon_points)

            # Create a graphics polygon item
            polygon_item = QtWidgets.QGraphicsPolygonItem(polygon)

            # Set the brush to the grey color with low transparency
            polygon_item.setBrush(QtGui.QBrush(color))

            # No border for cleaner look
            polygon_item.setPen(pg.mkPen(None))

            # Set a low z-value so it appears behind the curves but allows stacking
            polygon_item.setZValue(-10)

            # Add to the plot
            self.plot_widget.addItem(polygon_item)

        except Exception as e:
            logger.warning(f"Error creating wave segment box: {str(e)}")

    def add_gradient_density_scores(self, wave_segments):
        """
        Calculate density scores based on wave overlap intensity and display them as whole numbers 0-20
        on the right side of the gradient for all 100 y-axis bins. Higher scores indicate areas with more overlapping waves.

        Args:
            wave_segments: List of (segment_x, segment_y) tuples for each wave segment
        """
        try:
            if not wave_segments:
                logger.warning("No wave segments available for density scoring")
                return

            # Get the current y-axis range
            view_box = self.plot_widget.getPlotItem().getViewBox()
            y_range = view_box.viewRange()[1]  # Get y-axis range [min, max]
            y_min, y_max = y_range

            # Create a grid to calculate density at different y-levels
            num_y_bins = 100  # Resolution for density calculation
            y_bins = np.linspace(y_min, y_max, num_y_bins)
            density_counts = np.zeros(num_y_bins)

            # Count how many wave segments overlap at each y-level
            for _, segment_y in wave_segments:
                if len(segment_y) == 0:
                    continue

                # Find the y-range covered by this segment
                seg_y_min = np.min(segment_y)
                seg_y_max = np.max(segment_y)

                # Add density contribution to all y-bins within this segment's range
                for i, y_bin in enumerate(y_bins):
                    if seg_y_min <= y_bin <= seg_y_max:
                        density_counts[i] += 1

            # Normalize density counts to 0-20 scale
            if np.max(density_counts) > 0:
                # Scale to 0-20 range
                normalized_density = (density_counts / np.max(density_counts)) * 20
                # Round to whole numbers
                density_scores = np.round(normalized_density).astype(int)
            else:
                density_scores = np.zeros(num_y_bins, dtype=int)

            # Create score labels for all 100 bins
            for i, (y_bin, score) in enumerate(zip(y_bins, density_scores)):
                score_label = pg.TextItem(
                    text=str(score),
                    color='white',
                    anchor=(0, 0.5)  # Left-center aligned
                )
                # Position at x=3.6 (just to the right of the gradient area which ends at x=3.5)
                score_label.setPos(3.6, y_bin)
                self.plot_widget.addItem(score_label)

            logger.info(f"Added {len(density_scores)} gradient density scores for all bins based on {len(wave_segments)} wave segments")
            logger.info(f"Max density score: {np.max(density_scores)}, Min density score: {np.min(density_scores)}")

        except Exception as e:
            logger.error(f"Error adding gradient density scores: {str(e)}", exc_info=True)

    def add_volume_profile_density_scores(self, bin_centers, density_counts, volume_x_start, volume_max_width, band_values=None, bin_edges=None):
        """
        Add density scores (0-20) over the volume profile bars. Only displays when volume profile is enabled.
        Scores are positioned over their corresponding bins/bars.

        Args:
            bin_centers: Array of y-coordinates for bin centers
            density_counts: Array of density values for each bin
            volume_x_start: X-coordinate where volume profile bars start
            volume_max_width: Maximum width of volume profile bars
            band_values: Dictionary containing band values for filtering purple rays
            bin_edges: Array of bin edge boundaries for snapping purple rays to profile steps
        """
        try:
            if len(density_counts) == 0:
                logger.warning("No density data available for volume profile scoring")
                return

            # Normalize density counts to 0-20 scale
            if np.max(density_counts) > 0:
                # Scale to 0-20 range
                normalized_density = (density_counts / np.max(density_counts)) * 20
                # Round to whole numbers
                density_scores = np.round(normalized_density).astype(int)
            else:
                density_scores = np.zeros(len(density_counts), dtype=int)

            # Add score labels over each volume profile bar and track scores for ray detection
            visible_scores = []  # Store (score, bin_center, score_x_position) for visible bars

            for bin_center, density, score in zip(bin_centers, density_counts, density_scores):
                # Skip bins with very low density (same threshold as volume profile bars)
                if density < 0.02:
                    continue

                # Calculate the width of this specific bar
                bar_width = density * volume_max_width

                # Position the score at the right edge of the bar
                score_x_position = volume_x_start + bar_width + 0.1  # Small offset from bar edge

                # Store for ray detection
                visible_scores.append((score, bin_center, score_x_position))

                # Create score label
                score_label = pg.TextItem(
                    text=str(score),
                    color='white',
                    anchor=(0, 0.5)  # Left-center aligned
                )

                # Position over the corresponding volume profile bar
                score_label.setPos(score_x_position, bin_center)
                self.plot_widget.addItem(score_label)

            # Add purple rays for significant score changes
            self.add_density_score_change_rays(visible_scores, volume_x_start, volume_max_width, band_values, bin_edges)

            logger.info(f"Added {len(visible_scores)} density scores over volume profile bars")
            logger.info(f"Volume profile density scores - Max: {np.max(density_scores)}, Min: {np.min(density_scores)}")

        except Exception as e:
            logger.error(f"Error adding volume profile density scores: {str(e)}", exc_info=True)

    def add_density_score_change_rays(self, visible_scores, volume_x_start, volume_max_width, band_values=None, bin_edges=None):
        """
        Add purple rays extending from the density profile to mark significant score changes:
        1. Score changes from 2 to 1 or 1 to 2 (mark the 1)
        2. Score drops/rises by at least 2 digits (mark the lower score)

        Purple rays snap to the volume profile bin boundaries (steps) rather than score y-coordinates.
        Applies the same filtering logic used for other density elements to exclude rays in filtered zones.

        Args:
            visible_scores: List of (score, bin_center, score_x_position) tuples for visible bars
            volume_x_start: X-coordinate where volume profile bars start
            volume_max_width: Maximum width of volume profile bars
            band_values: Dictionary containing band values for filtering
            bin_edges: Array of bin edge boundaries for snapping rays to profile steps
        """
        try:
            if len(visible_scores) < 2:
                logger.info("Not enough visible scores for ray detection")
                return

            # Get filtering band values (same logic as peaks/troughs and intersection dots)
            high_50_percentile = None
            low_50_percentile = None
            maxavg_high = None
            maxavg_low = None
            lowest_low = None
            highest_high = None

            # Use the actual band values from the chart if available (same as intersection dots)
            if band_values and 'high_median' in band_values and 'low_median' in band_values:
                high_50_percentile = band_values['high_median']
                low_50_percentile = band_values['low_median']
                logger.info(f"Using chart 50% bands for purple rays: Low={low_50_percentile:.2f}, High={high_50_percentile:.2f}")
            else:
                logger.warning("No 50% band values available for purple ray filtering")

            # Get 25% band values (maxavg_high and maxavg_low) from instance variables
            if hasattr(self, 'maxavg_high') and self.maxavg_high is not None:
                maxavg_high = self.maxavg_high
                logger.info(f"Using stored maxavg_high for purple rays: {maxavg_high:.2f}")
            if hasattr(self, 'maxavg_low') and self.maxavg_low is not None:
                maxavg_low = self.maxavg_low
                logger.info(f"Using stored maxavg_low for purple rays: {maxavg_low:.2f}")

            # Get 1% band values from instance variables
            if hasattr(self, 'lowest_low') and self.lowest_low is not None:
                lowest_low = self.lowest_low
            if hasattr(self, 'highest_high') and self.highest_high is not None:
                highest_high = self.highest_high

            ray_positions = []  # Store y-positions where rays should be added
            filtered_count = 0  # Track how many rays were filtered out

            # Sort visible scores by y-position (bin_center) to process from bottom to top
            sorted_scores = sorted(visible_scores, key=lambda x: x[1])  # Sort by bin_center

            # Check each adjacent pair of scores for significant changes
            for i in range(len(sorted_scores) - 1):
                current_score, current_y, _ = sorted_scores[i]
                next_score, next_y, _ = sorted_scores[i + 1]

                # Check for score changes from 2 to 1 or 1 to 2 (mark the 1)
                if (current_score == 2 and next_score == 1) or (current_score == 1 and next_score == 2):
                    # Mark the position with score 1
                    candidate_y = current_y if current_score == 1 else next_y

                    # Apply filtering logic before adding ray
                    if not self.is_position_in_filtered_zone(candidate_y, high_50_percentile, low_50_percentile,
                                                           maxavg_high, maxavg_low, lowest_low, highest_high):
                        ray_positions.append((candidate_y, "2↔1 change"))
                        logger.debug(f"Found 2↔1 change: marking score 1 at y={candidate_y:.2f}")
                    else:
                        filtered_count += 1
                        logger.debug(f"FILTERED 2↔1 change at y={candidate_y:.2f} (in filtered zone)")

                # Check for score changes of at least 2 digits (mark the lower score)
                score_diff = abs(current_score - next_score)
                if score_diff >= 2:
                    lower_score = min(current_score, next_score)
                    candidate_y = current_y if current_score == lower_score else next_y

                    # Apply filtering logic before adding ray
                    if not self.is_position_in_filtered_zone(candidate_y, high_50_percentile, low_50_percentile,
                                                           maxavg_high, maxavg_low, lowest_low, highest_high):
                        ray_positions.append((candidate_y, f"±{score_diff} change"))
                        logger.debug(f"Found ±{score_diff} change: marking lower score {lower_score} at y={candidate_y:.2f}")
                    else:
                        filtered_count += 1
                        logger.debug(f"FILTERED ±{score_diff} change at y={candidate_y:.2f} (in filtered zone)")

            # Create purple rays for each identified position
            for ray_y, _ in ray_positions:
                # Snap ray to appropriate bin edge based on current price if bin_edges are available
                snapped_ray_y = ray_y  # Default to original position

                if bin_edges is not None and len(bin_edges) > 0:
                    # Get current price for reference
                    current_price = self.get_current_price()

                    # Find the bin that contains this ray position
                    bin_index = None
                    for i in range(len(bin_edges) - 1):
                        if bin_edges[i] <= ray_y <= bin_edges[i + 1]:
                            bin_index = i
                            break

                    if bin_index is not None:
                        # Determine snapping based on position relative to current price
                        if ray_y < current_price:
                            # Below current price: snap to TOP of bin
                            snapped_ray_y = bin_edges[bin_index + 1]  # Upper edge of bin
                            snap_direction = "top"
                        else:
                            # Above current price: snap to BOTTOM of bin
                            snapped_ray_y = bin_edges[bin_index]      # Lower edge of bin
                            snap_direction = "bottom"

                        logger.debug(f"Snapped ray from y={ray_y:.2f} to {snap_direction} of bin: y={snapped_ray_y:.2f} (current price: {current_price:.2f})")
                    else:
                        # Fallback: use closest edge if ray is outside all bins
                        distances = np.abs(bin_edges - ray_y)
                        closest_edge_index = np.argmin(distances)
                        snapped_ray_y = bin_edges[closest_edge_index]
                        logger.debug(f"Ray outside bins, snapped to closest edge: y={snapped_ray_y:.2f}")

                # Create a horizontal purple ray extending from the volume profile area
                ray_start_x = volume_x_start - 0.5  # Start slightly to the left of volume profile
                ray_end_x = volume_x_start + volume_max_width + 8.0  # Extend much further to the right

                # Create the purple ray line using the snapped y-position
                ray_line = pg.PlotDataItem(
                    x=[ray_start_x, ray_end_x],
                    y=[snapped_ray_y, snapped_ray_y],
                    pen=pg.mkPen(color='purple', width=2, style=QtCore.Qt.PenStyle.DashLine),
                    symbolPen=None,
                    symbolBrush=None,
                    symbol=None
                )

                # Set z-value to appear above volume profile but below text
                ray_line.setZValue(5)
                self.plot_widget.addItem(ray_line)

            logger.info(f"Added {len(ray_positions)} purple rays for significant score changes (filtered out {filtered_count})")

        except Exception as e:
            logger.error(f"Error adding density score change rays: {str(e)}", exc_info=True)

    def is_position_in_filtered_zone(self, y_position, high_50_percentile, low_50_percentile,
                                   maxavg_high, maxavg_low, lowest_low, highest_high):
        """
        Check if a y-position falls within any of the filtered zones used for density elements.
        Uses the same filtering logic as peaks, troughs, and intersection dots.

        Args:
            y_position: Y-coordinate to check
            high_50_percentile: Upper boundary of 50% band
            low_50_percentile: Lower boundary of 50% band
            maxavg_high: 25% high band value
            maxavg_low: 25% low band value
            lowest_low: 1% low band value
            highest_high: 1% high band value

        Returns:
            bool: True if position is in a filtered zone, False otherwise
        """
        try:
            # Check if position is within the 50% percentile bands
            if high_50_percentile is not None and low_50_percentile is not None:
                band_range = abs(high_50_percentile - low_50_percentile)
                buffer = band_range * 0.02  # 2% buffer of the band range (reduced from 5%)
                expanded_low = low_50_percentile - buffer
                expanded_high = high_50_percentile + buffer

                if expanded_low <= y_position <= expanded_high:
                    return True

            # Check if position is in top half of the zone between bottom 25% and 50% bands (grey area)
            if maxavg_low is not None and low_50_percentile is not None:
                midpoint_bottom = (maxavg_low + low_50_percentile) / 2
                if midpoint_bottom <= y_position <= low_50_percentile:
                    return True

            # Check if position is in bottom half of the zone between top 50% and 25% bands (grey area)
            if high_50_percentile is not None and maxavg_high is not None:
                midpoint_top = (high_50_percentile + maxavg_high) / 2
                if high_50_percentile <= y_position <= midpoint_top:
                    return True

            # Check if position is in bottom half of the zone between bottom 1% and 25% bands (grey area)
            if lowest_low is not None and maxavg_low is not None:
                midpoint_bottom_1 = (lowest_low + maxavg_low) / 2
                if lowest_low <= y_position <= midpoint_bottom_1:
                    return True

            # Check if position is in top half of the zone between top 25% and 1% bands (grey area)
            if maxavg_high is not None and highest_high is not None:
                midpoint_top_1 = (maxavg_high + highest_high) / 2
                if midpoint_top_1 <= y_position <= highest_high:
                    return True

            return False

        except Exception as e:
            logger.error(f"Error checking filtered zone for position {y_position}: {str(e)}")
            return False  # Default to not filtered if error occurs

    def get_current_price(self):
        """
        Get the current price for reference in ray snapping.

        Returns:
            float: Current price, or a reasonable default if not available
        """
        try:
            # Try to get current price from various sources
            if hasattr(self, 'current_price') and self.current_price is not None:
                return float(self.current_price)

            # Try to get from parent if available
            if hasattr(self, 'parent') and self.parent and hasattr(self.parent, 'current_price'):
                return float(self.parent.current_price)

            # Try to get from plot data range as fallback
            view_box = self.plot_widget.getPlotItem().getViewBox()
            y_range = view_box.viewRange()[1]  # Get y-axis range [min, max]
            y_min, y_max = y_range

            # Use middle of visible range as reasonable default
            default_price = (y_min + y_max) / 2
            logger.debug(f"Using middle of y-range as current price: {default_price:.2f}")
            return default_price

        except Exception as e:
            logger.error(f"Error getting current price: {str(e)}")
            return 100.0  # Fallback default

    def is_intersection_in_side_cross_zone(self, intersection_x, intersection_y=None):
        """
        Check if an intersection point falls within any of the side cross zones.
        Now includes price-based restrictions:
        - Left zone (trough) only works below current price
        - Right zone (peak) only works above current price

        Args:
            intersection_x: X-coordinate of the intersection point
            intersection_y: Y-coordinate of the intersection point (needed for price restrictions)

        Returns:
            dict: Zone information if intersection is in a zone, None otherwise
        """
        if not hasattr(self, 'side_cross_zones') or not self.side_cross_zones:
            return None

        for zone in self.side_cross_zones:
            # Check if intersection is within the x-boundaries of the zone
            if zone['left'] <= intersection_x <= zone['right']:
                # Check price-based restrictions if intersection_y is provided
                if intersection_y is not None and 'price_restriction' in zone and zone['reference_price'] is not None:
                    reference_price = zone['reference_price']

                    if zone['price_restriction'] == 'above':
                        # Right zone (peak) - only works above current price
                        if intersection_y <= reference_price:
                            logger.debug(f"Intersection at ({intersection_x:.2f}, {intersection_y:.2f}) in right zone but below reference price {reference_price:.2f} - excluding")
                            continue
                    elif zone['price_restriction'] == 'below':
                        # Left zone (trough) - only works below current price
                        if intersection_y >= reference_price:
                            logger.debug(f"Intersection at ({intersection_x:.2f}, {intersection_y:.2f}) in left zone but above reference price {reference_price:.2f} - excluding")
                            continue

                # Intersection is within zone and passes price restrictions
                return zone

        return None

    def detect_curve_peaks_and_troughs(self, curve_x, curve_y):
        """
        Detect peaks and troughs in a single density curve using local maxima/minima detection.

        Args:
            curve_x: X coordinates of the curve
            curve_y: Y coordinates of the curve

        Returns:
            tuple: (peaks, troughs) where each is a list of (x, y) coordinates
        """
        try:
            peaks = []
            troughs = []

            if len(curve_y) < 3:
                return peaks, troughs

            # Convert to numpy arrays for easier processing
            curve_x = np.array(curve_x)
            curve_y = np.array(curve_y)

            # Find local maxima (peaks) and minima (troughs)
            for i in range(1, len(curve_y) - 1):
                current_y = curve_y[i]
                prev_y = curve_y[i - 1]
                next_y = curve_y[i + 1]

                # Check for peak: current point is higher than both neighbors
                if current_y > prev_y and current_y > next_y:
                    peaks.append((curve_x[i], current_y))

                # Check for trough: current point is lower than both neighbors
                elif current_y < prev_y and current_y < next_y:
                    troughs.append((curve_x[i], current_y))

            # Also check endpoints if they are extremes
            if len(curve_y) >= 2:
                # Check first point
                if curve_y[0] > curve_y[1]:
                    peaks.append((curve_x[0], curve_y[0]))
                elif curve_y[0] < curve_y[1]:
                    troughs.append((curve_x[0], curve_y[0]))

                # Check last point
                if curve_y[-1] > curve_y[-2]:
                    peaks.append((curve_x[-1], curve_y[-1]))
                elif curve_y[-1] < curve_y[-2]:
                    troughs.append((curve_x[-1], curve_y[-1]))

            return peaks, troughs

        except Exception as e:
            logger.error(f"Error detecting curve peaks and troughs: {str(e)}", exc_info=True)
            return [], []

    def add_density_ray(self, price_level, ray_type, description, color='gray', peak_x=None, trough_x=None):
        """
        Add small line markers at the specified price level for density zones.
        Creates both right-side markers and on-peak/on-trough markers.

        Args:
            price_level: The Y-coordinate (price) where to draw the marker
            ray_type: Type of marker ('Peak', 'Trough', 'Intersection')
            description: Description for the marker
            color: Color of the marker (default: 'gray')
            peak_x: X-coordinate of the actual peak (for on-peak markers)
            trough_x: X-coordinate of the actual trough (for on-trough markers)
        """
        try:
            # Get the current plot range to determine marker positions
            view_range = self.plot_widget.getViewBox().viewRange()
            x_min, x_max = view_range[0]

            # Calculate small line length (2% of chart width)
            line_length = (x_max - x_min) * 0.02

            # Create right-side line marker
            right_side_x = x_max - (x_max - x_min) * 0.05  # 5% from right edge
            right_line_start = right_side_x - line_length / 2
            right_line_end = right_side_x + line_length / 2

            right_marker = pg.PlotDataItem(
                x=[right_line_start, right_line_end],
                y=[price_level, price_level],
                pen=pg.mkPen(color=color, width=2, style=pg.QtCore.Qt.PenStyle.SolidLine),
                name=f"{description} (Right Side)"
            )

            # Add the right-side marker to the plot
            self.plot_widget.addItem(right_marker)

            # Create on-peak/on-trough line marker if coordinates are provided
            if ray_type == 'Peak' and peak_x is not None:
                on_peak_line_start = peak_x - line_length / 2
                on_peak_line_end = peak_x + line_length / 2

                on_peak_marker = pg.PlotDataItem(
                    x=[on_peak_line_start, on_peak_line_end],
                    y=[price_level, price_level],
                    pen=pg.mkPen(color=color, width=2, style=pg.QtCore.Qt.PenStyle.SolidLine),
                    name=f"{description} (On Peak)"
                )
                self.plot_widget.addItem(on_peak_marker)
                logger.debug(f"Added {color} on-peak line marker at ({peak_x:.2f}, {price_level:.2f})")

            elif ray_type == 'Trough' and trough_x is not None:
                on_trough_line_start = trough_x - line_length / 2
                on_trough_line_end = trough_x + line_length / 2

                on_trough_marker = pg.PlotDataItem(
                    x=[on_trough_line_start, on_trough_line_end],
                    y=[price_level, price_level],
                    pen=pg.mkPen(color=color, width=2, style=pg.QtCore.Qt.PenStyle.SolidLine),
                    name=f"{description} (On Trough)"
                )
                self.plot_widget.addItem(on_trough_marker)
                logger.debug(f"Added {color} on-trough line marker at ({trough_x:.2f}, {price_level:.2f})")

            logger.debug(f"Added {color} {ray_type} line marker at price {price_level:.2f}: {description}")

        except Exception as e:
            logger.error(f"Error adding density ray: {str(e)}", exc_info=True)

    def find_all_curve_intersections(self, x1, y1, x2, y2):
        """
        Find ALL intersection points between two curves.

        Args:
            x1, y1: Arrays for first curve
            x2, y2: Arrays for second curve

        Returns:
            list: List of (x, y) tuples for all intersection points
        """
        try:
            if len(x1) == 0 or len(y1) == 0 or len(x2) == 0 or len(y2) == 0:
                return []

            # Convert to numpy arrays for easier processing
            x1, y1 = np.array(x1), np.array(y1)
            x2, y2 = np.array(x2), np.array(y2)

            # Find overlapping x-range
            x_min = max(np.min(x1), np.min(x2))
            x_max = min(np.max(x1), np.max(x2))

            if x_min >= x_max:
                return []  # No overlapping x-range

            # Create interpolation functions for both curves
            from scipy.interpolate import interp1d

            # Only use points within the overlapping range
            mask1 = (x1 >= x_min) & (x1 <= x_max)
            mask2 = (x2 >= x_min) & (x2 <= x_max)

            if np.sum(mask1) < 2 or np.sum(mask2) < 2:
                return []  # Not enough points for interpolation

            f1 = interp1d(x1[mask1], y1[mask1], kind='linear', bounds_error=False, fill_value='extrapolate')
            f2 = interp1d(x2[mask2], y2[mask2], kind='linear', bounds_error=False, fill_value='extrapolate')

            # Sample points in the overlapping range with higher resolution
            x_sample = np.linspace(x_min, x_max, 500)  # Increased resolution to catch more intersections
            y1_sample = f1(x_sample)
            y2_sample = f2(x_sample)

            # Find where curves cross (sign changes in difference)
            diff = y1_sample - y2_sample
            sign_changes = np.where(np.diff(np.sign(diff)))[0]

            intersections = []
            for idx in sign_changes:
                # Get more precise intersection point using linear interpolation
                x_left = x_sample[idx]
                x_right = x_sample[idx + 1]
                y1_left = y1_sample[idx]
                y1_right = y1_sample[idx + 1]
                y2_left = y2_sample[idx]
                y2_right = y2_sample[idx + 1]

                # Linear interpolation to find exact intersection
                if abs((y1_right - y1_left) - (y2_right - y2_left)) > 1e-10:  # Avoid division by zero
                    t = ((y2_left - y1_left)) / ((y1_right - y1_left) - (y2_right - y2_left))
                    intersection_x = x_left + t * (x_right - x_left)
                    intersection_y = y1_left + t * (y1_right - y1_left)
                    intersections.append((float(intersection_x), float(intersection_y)))

            return intersections

        except Exception as e:
            logger.debug(f"Error finding all curve intersections: {str(e)}")
            return []

    def detect_and_draw_breakthrough_lines(self, call_curves_data, put_curves_data,
                                          bid_call_curves_data, bid_put_curves_data,
                                          ask_call_curves_data, ask_put_curves_data):
        """
        Detect breakthrough points where call lines intersect with other call lines
        and put lines intersect with other put lines, then draw horizontal lines
        at the highest call breakthrough and lowest put breakthrough.

        IV Peak lines are derived from bid prices and their bid-based breakthrough points.
        IV Walls, Overflow, and Max Fear are derived from ask prices and their ask-based breakthrough points.
        Each price type (bid/ask) uses its own breakthrough reference to determine which zones to draw.

        Args:
            call_curves_data: List of (strike, peak_y, curve_y_values, x_values) for displayed call curves
            put_curves_data: List of (strike, trough_y, curve_y_values, x_values) for displayed put curves
            bid_call_curves_data: List of (strike, peak_y, curve_y_values, x_values) for bid call curves (IV Peak)
            bid_put_curves_data: List of (strike, trough_y, curve_y_values, x_values) for bid put curves (IV Peak)
            ask_call_curves_data: List of (strike, peak_y, curve_y_values, x_values) for ask call curves (IV Walls/Overflow/Max Fear)
            ask_put_curves_data: List of (strike, trough_y, curve_y_values, x_values) for ask put curves (IV Walls/Overflow/Max Fear)
        """
        try:
            logger.info(f"Starting breakthrough detection with {len(call_curves_data)} call curves and {len(put_curves_data)} put curves")
            logger.info(f"Bid curves: {len(bid_call_curves_data)} calls, {len(bid_put_curves_data)} puts")
            logger.info(f"Ask curves: {len(ask_call_curves_data)} calls, {len(ask_put_curves_data)} puts")
            logger.info(f"IMPORTANT: IV zones are calculated independently of display toggle. Current display mode: {'Ask' if hasattr(self, 'use_ask_prices') and self.use_ask_prices else 'Bid'} prices")

            # Find call line breakthroughs using BID prices for IV Peak determination
            bid_call_breakthrough_y = self.find_highest_call_breakthrough(bid_call_curves_data)

            # Find put line breakthroughs using BID prices for IV Peak determination
            bid_put_breakthrough_y = self.find_lowest_put_breakthrough(bid_put_curves_data)

            # Find call line breakthroughs using ASK prices for IV Walls/Overflow/Max Fear reference
            ask_call_breakthrough_y = self.find_highest_call_breakthrough(ask_call_curves_data)

            # Find put line breakthroughs using ASK prices for IV Walls/Overflow/Max Fear reference
            ask_put_breakthrough_y = self.find_lowest_put_breakthrough(ask_put_curves_data)



            # Draw horizontal lines at breakthrough points only if Option Zones is enabled
            if self.show_option_zones:
                # Draw BID-based IV Peak lines
                if bid_call_breakthrough_y is not None:
                    self.draw_breakthrough_line(bid_call_breakthrough_y, 'call')
                    logger.info(f"Drew call IV Peak line (from bid prices) at y={bid_call_breakthrough_y:.2f}")

                if bid_put_breakthrough_y is not None:
                    self.draw_breakthrough_line(bid_put_breakthrough_y, 'put')
                    logger.info(f"Drew put IV Peak line (from bid prices) at y={bid_put_breakthrough_y:.2f}")

                # Draw ASK-based IV Walls, Overflow, and Max Fear lines
                # Use ASK breakthrough as reference point (not bid breakthrough)
                if ask_call_breakthrough_y is not None:
                    logger.info(f"Using ask call breakthrough at y={ask_call_breakthrough_y:.2f} as reference for ask-based zones")
                    # Add horizontal lines at 1st, 2nd, 3rd, and 5th highest call lines above ASK breakthrough
                    self.draw_ranked_call_highs_above_breakthrough(ask_call_curves_data, ask_call_breakthrough_y)

                if ask_put_breakthrough_y is not None:
                    logger.info(f"Using ask put breakthrough at y={ask_put_breakthrough_y:.2f} as reference for ask-based zones")
                    # Add horizontal lines at 1st, 2nd, 3rd, and 5th lowest put lines below ASK breakthrough
                    self.draw_ranked_put_lows_below_breakthrough(ask_put_curves_data, ask_put_breakthrough_y)
            else:
                logger.info("Option Zones disabled - skipping IV zone lines")

        except Exception as e:
            logger.error(f"Error in breakthrough detection: {str(e)}", exc_info=True)







    def find_highest_call_breakthrough(self, call_curves_data):
        """
        Find the highest call line that actually broke through another call line.

        Args:
            call_curves_data: List of (strike, peak_y, curve_y_values, x_values) for call curves

        Returns:
            float or None: Y-coordinate of the highest call breakthrough, or None if no breakthrough found
        """
        try:
            if len(call_curves_data) < 2:
                logger.info("Not enough call curves for breakthrough detection")
                return None

            breakthroughs = []

            # Compare each call curve with every other call curve to find intersections
            for i, (strike1, peak1, curve1_y, curve1_x) in enumerate(call_curves_data):
                for j, (strike2, peak2, curve2_y, curve2_x) in enumerate(call_curves_data):
                    if i >= j:  # Avoid duplicate comparisons and self-comparison
                        continue

                    # Check if curves intersect by finding where they cross
                    intersection_y = self.find_curve_intersection(curve1_x, curve1_y, curve2_x, curve2_y)

                    if intersection_y is not None:
                        # Determine which curve broke through the other
                        # The curve with the higher peak is considered to have broken through
                        if peak1 > peak2:
                            breakthroughs.append((strike1, peak1, intersection_y))
                            logger.debug(f"Call breakthrough: Strike {strike1:.2f} (peak {peak1:.2f}) broke through strike {strike2:.2f} at y={intersection_y:.2f}")
                        elif peak2 > peak1:
                            breakthroughs.append((strike2, peak2, intersection_y))
                            logger.debug(f"Call breakthrough: Strike {strike2:.2f} (peak {peak2:.2f}) broke through strike {strike1:.2f} at y={intersection_y:.2f}")

            if not breakthroughs:
                logger.info("No call breakthroughs detected")
                return None

            # Find the breakthrough with the highest peak (the highest call line that broke through)
            highest_breakthrough = max(breakthroughs, key=lambda x: x[1])  # Sort by peak_y (index 1)
            breakthrough_strike, breakthrough_peak, intersection_y = highest_breakthrough

            logger.info(f"Highest call breakthrough: Strike {breakthrough_strike:.2f} with peak {breakthrough_peak:.2f}")
            return breakthrough_peak  # Return the peak Y coordinate of the highest breakthrough

        except Exception as e:
            logger.error(f"Error finding call breakthroughs: {str(e)}", exc_info=True)
            return None

    def find_lowest_put_breakthrough(self, put_curves_data):
        """
        Find the lowest put line that actually broke through another put line.

        Args:
            put_curves_data: List of (strike, trough_y, curve_y_values, x_values) for put curves

        Returns:
            float or None: Y-coordinate of the lowest put breakthrough, or None if no breakthrough found
        """
        try:
            if len(put_curves_data) < 2:
                logger.info("Not enough put curves for breakthrough detection")
                return None

            breakthroughs = []

            # Compare each put curve with every other put curve to find intersections
            for i, (strike1, trough1, curve1_y, curve1_x) in enumerate(put_curves_data):
                for j, (strike2, trough2, curve2_y, curve2_x) in enumerate(put_curves_data):
                    if i >= j:  # Avoid duplicate comparisons and self-comparison
                        continue

                    # Check if curves intersect by finding where they cross
                    intersection_y = self.find_curve_intersection(curve1_x, curve1_y, curve2_x, curve2_y)

                    if intersection_y is not None:
                        # Determine which curve broke through the other
                        # The curve with the lower trough is considered to have broken through
                        if trough1 < trough2:
                            breakthroughs.append((strike1, trough1, intersection_y))
                            logger.debug(f"Put breakthrough: Strike {strike1:.2f} (trough {trough1:.2f}) broke through strike {strike2:.2f} at y={intersection_y:.2f}")
                        elif trough2 < trough1:
                            breakthroughs.append((strike2, trough2, intersection_y))
                            logger.debug(f"Put breakthrough: Strike {strike2:.2f} (trough {trough2:.2f}) broke through strike {strike1:.2f} at y={intersection_y:.2f}")

            if not breakthroughs:
                logger.info("No put breakthroughs detected")
                return None

            # Find the breakthrough with the lowest trough (the lowest put line that broke through)
            lowest_breakthrough = min(breakthroughs, key=lambda x: x[1])  # Sort by trough_y (index 1)
            breakthrough_strike, breakthrough_trough, intersection_y = lowest_breakthrough

            logger.info(f"Lowest put breakthrough: Strike {breakthrough_strike:.2f} with trough {breakthrough_trough:.2f}")
            return breakthrough_trough  # Return the trough Y coordinate of the lowest breakthrough

        except Exception as e:
            logger.error(f"Error finding put breakthroughs: {str(e)}", exc_info=True)
            return None

    def find_detailed_curve_intersection(self, x1, y1, x2, y2):
        """
        Find intersection points between two curves, returning both x and y coordinates.

        Args:
            x1, y1: Arrays for first curve
            x2, y2: Arrays for second curve

        Returns:
            tuple or None: (x, y) coordinates of intersection point, or None if no intersection
        """
        try:
            if len(x1) == 0 or len(y1) == 0 or len(x2) == 0 or len(y2) == 0:
                return None

            # Convert to numpy arrays for easier processing
            x1, y1 = np.array(x1), np.array(y1)
            x2, y2 = np.array(x2), np.array(y2)

            # Find overlapping x-range
            x_min = max(np.min(x1), np.min(x2))
            x_max = min(np.max(x1), np.max(x2))

            if x_min >= x_max:
                return None  # No overlapping x-range

            # Create interpolation functions for both curves
            from scipy.interpolate import interp1d

            # Only use points within the overlapping range
            mask1 = (x1 >= x_min) & (x1 <= x_max)
            mask2 = (x2 >= x_min) & (x2 <= x_max)

            if np.sum(mask1) < 2 or np.sum(mask2) < 2:
                return None  # Not enough points for interpolation

            f1 = interp1d(x1[mask1], y1[mask1], kind='linear', bounds_error=False, fill_value='extrapolate')
            f2 = interp1d(x2[mask2], y2[mask2], kind='linear', bounds_error=False, fill_value='extrapolate')

            # Sample points in the overlapping range
            x_sample = np.linspace(x_min, x_max, 100)
            y1_sample = f1(x_sample)
            y2_sample = f2(x_sample)

            # Find where curves cross (sign changes in difference)
            diff = y1_sample - y2_sample
            sign_changes = np.where(np.diff(np.sign(diff)))[0]

            if len(sign_changes) > 0:
                # Return both x and y coordinates of the first intersection
                intersection_x = x_sample[sign_changes[0]]
                intersection_y = f1(intersection_x)
                return (float(intersection_x), float(intersection_y))

            return None

        except Exception as e:
            logger.debug(f"Error finding detailed curve intersection: {str(e)}")
            return None

    def find_curve_intersection(self, x1, y1, x2, y2):
        """
        Find intersection points between two curves.

        Args:
            x1, y1: Arrays for first curve
            x2, y2: Arrays for second curve

        Returns:
            float or None: Y-coordinate of intersection point, or None if no intersection
        """
        try:
            if len(x1) == 0 or len(y1) == 0 or len(x2) == 0 or len(y2) == 0:
                return None

            # Convert to numpy arrays for easier processing
            x1, y1 = np.array(x1), np.array(y1)
            x2, y2 = np.array(x2), np.array(y2)

            # Find overlapping x-range
            x_min = max(np.min(x1), np.min(x2))
            x_max = min(np.max(x1), np.max(x2))

            if x_min >= x_max:
                return None  # No overlapping x-range

            # Create interpolation functions for both curves
            from scipy.interpolate import interp1d

            # Only use points within the overlapping range
            mask1 = (x1 >= x_min) & (x1 <= x_max)
            mask2 = (x2 >= x_min) & (x2 <= x_max)

            if np.sum(mask1) < 2 or np.sum(mask2) < 2:
                return None  # Not enough points for interpolation

            f1 = interp1d(x1[mask1], y1[mask1], kind='linear', bounds_error=False, fill_value='extrapolate')
            f2 = interp1d(x2[mask2], y2[mask2], kind='linear', bounds_error=False, fill_value='extrapolate')

            # Sample points in the overlapping range
            x_sample = np.linspace(x_min, x_max, 100)
            y1_sample = f1(x_sample)
            y2_sample = f2(x_sample)

            # Find where curves cross (sign changes in difference)
            diff = y1_sample - y2_sample
            sign_changes = np.where(np.diff(np.sign(diff)))[0]

            if len(sign_changes) > 0:
                # Return the y-coordinate of the first intersection
                intersection_x = x_sample[sign_changes[0]]
                intersection_y = f1(intersection_x)
                return float(intersection_y)

            return None

        except Exception as e:
            logger.debug(f"Error finding curve intersection: {str(e)}")
            return None

    def draw_breakthrough_line(self, y_position, line_type):
        """
        Draw a horizontal line at the breakthrough position (IV Peak from bid prices).

        Args:
            y_position: Y-coordinate where to draw the line
            line_type: 'call' or 'put' to determine line color and style
        """
        try:
            # Choose color and style based on line type
            if line_type == 'call':
                color = '#808080'  # Grey for IV Peak (call breakthroughs from bid prices)
                line_style = QtCore.Qt.PenStyle.SolidLine
                label_text = f"IV Peak (Bid): {y_position:.2f}"
            else:  # put
                color = '#808080'  # Grey for IV Peak (put breakthroughs from bid prices)
                line_style = QtCore.Qt.PenStyle.SolidLine
                label_text = f"IV Peak (Bid): {y_position:.2f}"

            # Create horizontal line
            breakthrough_line = pg.InfiniteLine(
                pos=y_position,
                angle=0,  # Horizontal line
                pen=pg.mkPen(color=color, width=2, style=line_style),
                label=label_text,
                labelOpts={
                    'position': 0.95,  # Position near the right end
                    'color': color,
                    'movable': False,
                    'fill': (0, 0, 0, 100)  # Semi-transparent black background
                }
            )

            # Add the line to the plot with lower z-order so it renders behind other elements
            breakthrough_line.setZValue(-10)  # Lower z-value means it renders behind
            self.plot_widget.addItem(breakthrough_line)

            logger.info(f"Added {line_type} breakthrough line at y={y_position:.2f}")

        except Exception as e:
            logger.error(f"Error drawing breakthrough line: {str(e)}", exc_info=True)

    def draw_ranked_call_highs_above_breakthrough(self, call_curves_data, breakthrough_y):
        """
        Draw horizontal lines at the 1st, 2nd, 3rd, and 5th highest call line peaks above the breakthrough.
        These represent IV Walls, IV Overflow, and Max Fear levels derived from ASK prices.
        Only draws zones that don't touch the ASK-based breakthrough point.

        Args:
            call_curves_data: List of (strike, peak_y, curve_y_values, x_values) for ask call curves
            breakthrough_y: Y-coordinate of the ASK call breakthrough line (reference point for ask-based zones)
        """
        try:
            if not call_curves_data:
                logger.info("No call curves data available for ranked highs")
                return

            # Filter call curves that are above the breakthrough line and get their peaks
            peaks_above_breakthrough = []
            for strike, peak_y, _, _ in call_curves_data:
                if peak_y > breakthrough_y:
                    peaks_above_breakthrough.append((strike, peak_y))

            if not peaks_above_breakthrough:
                logger.info("No call peaks found above breakthrough line")
                return

            # Sort by peak_y in ascending order (closest to breakthrough first)
            peaks_above_breakthrough.sort(key=lambda x: x[1])

            # Define which ranks to draw (1st, 2nd, 3rd, 5th highest)
            ranks_to_draw = [1, 2, 3, 5]

            logger.info(f"Found {len(peaks_above_breakthrough)} call peaks above breakthrough at y={breakthrough_y:.2f}")
            for i, (strike, peak) in enumerate(peaks_above_breakthrough[:10]):  # Log first 10 for debugging
                logger.info(f"  Next {i+1}: Strike {strike:.2f}, Peak {peak:.2f}")

            for rank in ranks_to_draw:
                if rank <= len(peaks_above_breakthrough):
                    strike, peak_y = peaks_above_breakthrough[rank - 1]  # rank-1 because list is 0-indexed

                    # Map rank to IV label and color (from ask prices)
                    rank_labels = {
                        1: "IV Inner Wall (Ask)",
                        2: "IV Wall (Ask)",
                        3: "IV Overflow (Ask)",
                        5: "Max Fear (Ask)"
                    }

                    rank_colors = {
                        1: '#2962ff',  # Bright blue for IV Inner Wall
                        2: '#2962ff',  # Bright blue for IV Wall
                        3: '#2962ff',  # Bright blue for IV Overflow
                        5: '#FF0000'   # Red for Max Fear
                    }



                    # Draw horizontal line with IV label and appropriate color
                    line_color = rank_colors[rank]
                    label_text = f"{rank_labels[rank]}: {peak_y:.2f}"

                    ranked_line = pg.InfiniteLine(
                        pos=peak_y,
                        angle=0,  # Horizontal line
                        pen=pg.mkPen(color=line_color, width=1, style=QtCore.Qt.PenStyle.DashLine),
                        label=label_text,
                        labelOpts={
                            'position': 0.85,  # Position near the right end
                            'color': line_color,
                            'movable': False,
                            'fill': (0, 0, 0, 80)  # Semi-transparent black background
                        }
                    )

                    # Add the line to the plot with lower z-order
                    ranked_line.setZValue(-15)  # Even lower than breakthrough lines
                    self.plot_widget.addItem(ranked_line)

                    logger.info(f"Added call #{rank} highest line at y={peak_y:.2f} (strike {strike:.2f})")
                else:
                    logger.info(f"Not enough call peaks above breakthrough for rank #{rank}")

        except Exception as e:
            logger.error(f"Error drawing ranked call highs: {str(e)}", exc_info=True)

    def draw_ranked_put_lows_below_breakthrough(self, put_curves_data, breakthrough_y):
        """
        Draw horizontal lines at the 1st, 2nd, 3rd, and 5th lowest put line troughs below the breakthrough.
        These represent IV Walls, IV Overflow, and Max Fear levels derived from ASK prices.
        Only draws zones that don't touch the ASK-based breakthrough point.

        Args:
            put_curves_data: List of (strike, trough_y, curve_y_values, x_values) for ask put curves
            breakthrough_y: Y-coordinate of the ASK put breakthrough line (reference point for ask-based zones)
        """
        try:
            if not put_curves_data:
                logger.info("No put curves data available for ranked lows")
                return

            # Filter put curves that are below the breakthrough line and get their troughs
            troughs_below_breakthrough = []
            for strike, trough_y, _, _ in put_curves_data:
                if trough_y < breakthrough_y:
                    troughs_below_breakthrough.append((strike, trough_y))

            if not troughs_below_breakthrough:
                logger.info("No put troughs found below breakthrough line")
                return

            # Sort by trough_y in descending order (closest to breakthrough first)
            troughs_below_breakthrough.sort(key=lambda x: x[1], reverse=True)

            # Define which ranks to draw (1st, 2nd, 3rd, 5th lowest)
            ranks_to_draw = [1, 2, 3, 5]

            logger.info(f"Found {len(troughs_below_breakthrough)} put troughs below breakthrough at y={breakthrough_y:.2f}")
            for i, (strike, trough) in enumerate(troughs_below_breakthrough[:10]):  # Log first 10 for debugging
                logger.info(f"  Next {i+1}: Strike {strike:.2f}, Trough {trough:.2f}")

            for rank in ranks_to_draw:
                if rank <= len(troughs_below_breakthrough):
                    strike, trough_y = troughs_below_breakthrough[rank - 1]  # rank-1 because list is 0-indexed

                    # Map rank to IV label and color (from ask prices)
                    rank_labels = {
                        1: "IV Inner Wall (Ask)",
                        2: "IV Wall (Ask)",
                        3: "IV Overflow (Ask)",
                        5: "Max Fear (Ask)"
                    }

                    rank_colors = {
                        1: '#2962ff',  # Bright blue for IV Inner Wall
                        2: '#2962ff',  # Bright blue for IV Wall
                        3: '#2962ff',  # Bright blue for IV Overflow
                        5: '#FF0000'   # Red for Max Fear
                    }



                    # Draw horizontal line with IV label and appropriate color
                    line_color = rank_colors[rank]
                    label_text = f"{rank_labels[rank]}: {trough_y:.2f}"

                    ranked_line = pg.InfiniteLine(
                        pos=trough_y,
                        angle=0,  # Horizontal line
                        pen=pg.mkPen(color=line_color, width=1, style=QtCore.Qt.PenStyle.DashLine),
                        label=label_text,
                        labelOpts={
                            'position': 0.85,  # Position near the right end
                            'color': line_color,
                            'movable': False,
                            'fill': (0, 0, 0, 80)  # Semi-transparent black background
                        }
                    )

                    # Add the line to the plot with lower z-order
                    ranked_line.setZValue(-15)  # Even lower than breakthrough lines
                    self.plot_widget.addItem(ranked_line)

                    logger.info(f"Added put #{rank} lowest line at y={trough_y:.2f} (strike {strike:.2f})")
                else:
                    logger.info(f"Not enough put troughs below breakthrough for rank #{rank}")

        except Exception as e:
            logger.error(f"Error drawing ranked put lows: {str(e)}", exc_info=True)

    def add_intersection_cluster_circles(self, intersections, all_curves=None):
        """
        Use DBSCAN clustering to identify the most clustered intersection areas
        and highlight intersection dots in blue for the top 2 clusters on each side of the reference price.
        Also marks the top and bottom of clustered intersect areas with orange boxes.

        Only analyzes orange intersections (those within red zones/side cross zones).
        - Top 2 clusters above reference price: Dark blue (#1E3A8A) for tightest, Light blue (#60A5FA) for 2nd tightest
        - Top 2 clusters below reference price: Dark blue (#1E3A8A) for tightest, Light blue (#60A5FA) for 2nd tightest
        - Orange boxes mark the highest and lowest intersections in each cluster

        Args:
            intersections: List of (x, y, original_color, dot_reference) tuples representing orange intersection data
            all_curves: List of curve data for peak/trough detection (optional)
        """
        try:
            import numpy as np
            from sklearn.cluster import DBSCAN

            if len(intersections) < 3:
                logger.info("Not enough intersections for clustering")
                return

            # Extract coordinates and dot references from orange intersection data
            points = np.array([(x, y) for x, y, _, _ in intersections])
            dot_references = [dot_ref for _, _, _, dot_ref in intersections]
            logger.info(f"Starting DBSCAN clustering on {len(points)} orange intersection points (red zone intersections)")

            # Get reference price to separate above/below clusters
            reference_price = self.get_reference_price_for_clustering()
            logger.info(f"Using reference price {reference_price:.2f} to separate above/below clusters")

            # Normalize the data for better clustering
            # Since x and y might have different scales, we normalize them
            x_range = points[:, 0].max() - points[:, 0].min()
            y_range = points[:, 1].max() - points[:, 1].min()

            if x_range == 0 or y_range == 0:
                logger.info("All intersections are on a line, skipping clustering")
                return

            # Create normalized points for clustering
            normalized_points = points.copy()
            normalized_points[:, 0] = (points[:, 0] - points[:, 0].min()) / x_range
            normalized_points[:, 1] = (points[:, 1] - points[:, 1].min()) / y_range

            # DBSCAN parameters for very tight clustering
            # eps: maximum distance between points in a cluster (in normalized space)
            # min_samples: minimum number of points to form a cluster
            eps = self.intersection_eps_primary  # Use configurable parameter
            min_samples = self.intersection_min_samples_primary  # Use configurable parameter

            # Perform DBSCAN clustering
            dbscan = DBSCAN(eps=eps, min_samples=min_samples)
            cluster_labels = dbscan.fit_predict(normalized_points)

            # Find unique clusters (excluding noise points labeled as -1)
            unique_clusters = np.unique(cluster_labels)
            valid_clusters = unique_clusters[unique_clusters != -1]

            logger.info(f"DBSCAN found {len(valid_clusters)} clusters from {len(points)} points")

            if len(valid_clusters) == 0:
                logger.info("No clusters found, trying with more relaxed parameters")
                # Try with more relaxed parameters but still keep them very tight
                eps = self.intersection_eps_fallback  # Use configurable fallback parameter
                min_samples = self.intersection_min_samples_fallback  # Use configurable fallback parameter
                dbscan = DBSCAN(eps=eps, min_samples=min_samples)
                cluster_labels = dbscan.fit_predict(normalized_points)
                unique_clusters = np.unique(cluster_labels)
                valid_clusters = unique_clusters[unique_clusters != -1]
                logger.info(f"Relaxed DBSCAN found {len(valid_clusters)} clusters")

            if len(valid_clusters) == 0:
                logger.info("Still no clusters found, skipping cluster visualization")
                return

            # Calculate cluster statistics
            cluster_stats = []
            for cluster_id in valid_clusters:
                cluster_mask = cluster_labels == cluster_id
                cluster_points = points[cluster_mask]

                # Calculate cluster center and size
                center_x = np.mean(cluster_points[:, 0])
                center_y = np.mean(cluster_points[:, 1])

                # Calculate cluster radius using a very tight approach
                distances = np.sqrt((cluster_points[:, 0] - center_x)**2 +
                                  (cluster_points[:, 1] - center_y)**2)

                # Use minimum distance for extremely tight clustering
                min_distance = np.min(distances[distances > 0]) if len(distances[distances > 0]) > 0 else 0.01

                # Make radius extremely small - just the minimum distance between points
                radius = max(min_distance * 0.5, 0.01)  # Very tight: 50% of min distance, min 0.01 units

                # Calculate tightness metrics for ranking
                avg_distance = np.mean(distances)
                max_distance = np.max(distances)
                std_distance = np.std(distances) if len(distances) > 1 else 0.0

                # Tightness score: lower is better (tighter cluster)
                # Use average distance as primary metric, with std as tiebreaker
                tightness_score = avg_distance + (std_distance * 0.1)

                cluster_stats.append({
                    'id': cluster_id,
                    'center': (center_x, center_y),
                    'radius': radius,
                    'size': len(cluster_points),
                    'points': cluster_points,
                    'avg_distance': avg_distance,
                    'max_distance': max_distance,
                    'std_distance': std_distance,
                    'tightness_score': tightness_score
                })

                logger.info(f"Cluster {cluster_id}: {len(cluster_points)} points, "
                           f"center=({center_x:.2f}, {center_y:.2f}), radius={radius:.2f}, "
                           f"tightness={tightness_score:.3f} (avg_dist={avg_distance:.3f})")

            # Separate clusters into above and below reference price and group by filtered areas
            above_clusters = []
            below_clusters = []
            clusters_by_area = {}

            for cluster in cluster_stats:
                center_y = cluster['center'][1]

                # Determine which filtered area this cluster belongs to
                area = self.get_cluster_filtered_area(center_y, reference_price)
                cluster['area'] = area

                if center_y > reference_price:
                    above_clusters.append(cluster)
                else:
                    below_clusters.append(cluster)

                # Group clusters by area for limiting
                if area not in clusters_by_area:
                    clusters_by_area[area] = []
                clusters_by_area[area].append(cluster)

            # Sort each group by tightness (lower score = tighter cluster) in ascending order
            above_clusters.sort(key=lambda x: x['tightness_score'], reverse=False)
            below_clusters.sort(key=lambda x: x['tightness_score'], reverse=False)

            # Limit to 1 zone per filtered area - keep only the tightest cluster in each area
            limited_clusters = []
            for area, area_clusters in clusters_by_area.items():
                if area_clusters:
                    # Sort by tightness and take the tightest (lowest score)
                    area_clusters.sort(key=lambda x: x['tightness_score'], reverse=False)
                    tightest_cluster = area_clusters[0]
                    limited_clusters.append(tightest_cluster)
                    logger.info(f"Selected tightest cluster in area '{area}': tightness={tightest_cluster['tightness_score']:.3f}")

            # Update the above/below lists to only include limited clusters
            above_clusters = [c for c in limited_clusters if c['center'][1] > reference_price]
            below_clusters = [c for c in limited_clusters if c['center'][1] <= reference_price]

            logger.info(f"Found {len(above_clusters)} clusters above reference price {reference_price:.2f}")
            logger.info(f"Found {len(below_clusters)} clusters below reference price {reference_price:.2f}")

            # Highlight intersection dots with different blue shades and add orange boxes for cluster extremes
            highlighted_dots = 0
            cluster_colors = []
            cluster_extremes = {}  # Store highest and lowest points for each cluster

            # Process top 2 tightest clusters above reference price
            for i, cluster in enumerate(above_clusters[:2]):
                if i == 0:
                    color = (30, 58, 138)  # Dark blue (#1E3A8A) for tightest cluster above
                    color_name = "dark blue (tightest above)"
                else:
                    color = (96, 165, 250)  # Light blue (#60A5FA) for 2nd tightest above
                    color_name = "light blue (2nd tightest above)"
                cluster_colors.append((cluster['id'], color, color_name, f"above #{i+1}"))

                # Find highest and lowest points in this cluster
                cluster_points = cluster['points']
                highest_point = cluster_points[np.argmax(cluster_points[:, 1])]
                lowest_point = cluster_points[np.argmin(cluster_points[:, 1])]
                cluster_extremes[cluster['id']] = {
                    'highest': highest_point,
                    'lowest': lowest_point
                }

            # Process top 2 tightest clusters below reference price
            for i, cluster in enumerate(below_clusters[:2]):
                if i == 0:
                    color = (30, 58, 138)  # Dark blue (#1E3A8A) for tightest cluster below
                    color_name = "dark blue (tightest below)"
                else:
                    color = (96, 165, 250)  # Light blue (#60A5FA) for 2nd tightest below
                    color_name = "light blue (2nd tightest below)"
                cluster_colors.append((cluster['id'], color, color_name, f"below #{i+1}"))

                # Find highest and lowest points in this cluster
                cluster_points = cluster['points']
                highest_point = cluster_points[np.argmax(cluster_points[:, 1])]
                lowest_point = cluster_points[np.argmin(cluster_points[:, 1])]
                cluster_extremes[cluster['id']] = {
                    'highest': highest_point,
                    'lowest': lowest_point
                }

            # Create lookup dictionary for cluster colors
            cluster_color_map = {cluster_id: (color, color_name, position) for cluster_id, color, color_name, position in cluster_colors}

            for i, point in enumerate(points):
                cluster_id = cluster_labels[i]
                if cluster_id in cluster_color_map:
                    # Get the dot reference for this intersection
                    dot_ref = dot_references[i]

                    # Get the appropriate color for this cluster
                    color, color_name, position = cluster_color_map[cluster_id]

                    # Update the dot color
                    dot_ref.setPen(pg.mkPen(color=color, width=2))
                    dot_ref.setBrush(pg.mkBrush(color=color))

                    highlighted_dots += 1

                    logger.info(f"Highlighted intersection #{i+1} at ({point[0]:.2f}, {point[1]:.2f}) "
                               f"with {color_name} as part of {position} cluster")

            # Collect all peaks and troughs from density curves for zone extension
            all_peaks = []
            all_troughs = []
            if all_curves is not None:
                for curve_x, curve_y, _, _, _ in all_curves:
                    if len(curve_x) == 0 or len(curve_y) == 0:
                        continue
                    peaks, troughs = self.detect_curve_peaks_and_troughs(curve_x, curve_y)
                    all_peaks.extend(peaks)
                    all_troughs.extend(troughs)

                logger.info(f"Collected {len(all_peaks)} peaks and {len(all_troughs)} troughs for zone extension")
            else:
                logger.warning("No curve data provided for peak/trough extension")

            # Add single orange box around each cluster encompassing the entire clustered area
            for cluster_id, extremes in cluster_extremes.items():
                if cluster_id in cluster_color_map:
                    # Get cluster data to find the full boundaries
                    cluster_data = None
                    for cluster in (above_clusters + below_clusters):
                        if cluster['id'] == cluster_id:
                            cluster_data = cluster
                            break

                    if cluster_data:
                        # NOTE: Zone creation now handled by priority ranking system
                        # self.add_cluster_boundary_box(cluster_data['points'], all_peaks, all_troughs, reference_price)
                        # boxes_added += 1
                        highest_x, highest_y = extremes['highest']
                        lowest_x, lowest_y = extremes['lowest']
                        logger.info(f"Orange intersection cluster {cluster_id} detected spanning from ({lowest_x:.2f}, {lowest_y:.2f}) to ({highest_x:.2f}, {highest_y:.2f}) - zones handled by priority ranking")

            logger.info(f"Successfully highlighted {highlighted_dots} orange intersection dots in tightest clusters: "
                       f"{len(above_clusters[:2])} above + {len(below_clusters[:2])} below reference price")
            logger.info(f"Orange intersection clusters detected - zones handled by priority ranking system")

            # Store results for priority ranking system
            selected_clusters = above_clusters[:2] + below_clusters[:2]
            non_clustered_intersections = self.get_non_clustered_orange_intersections(intersections, cluster_labels)

            # Store orange intersection data for priority ranking
            self.store_intersection_data_for_priority_ranking(
                orange_clusters=selected_clusters,
                orange_non_clusters=non_clustered_intersections,
                red_clusters=getattr(self, 'red_intersect_clusters', None),
                red_non_clusters=getattr(self, 'red_intersect_non_clusters', None)
            )

        except ImportError:
            logger.error("scikit-learn not available for clustering. Install with: pip install scikit-learn")
        except Exception as e:
            logger.error(f"Error in intersection clustering: {str(e)}", exc_info=True)

    def add_red_intersection_cluster_circles(self, red_intersections, _=None):
        """
        Use DBSCAN clustering to identify the most clustered red intersection areas
        and highlight intersection dots in blue for the top 2 clusters on each side of the reference price.
        Also marks the top and bottom of clustered intersect areas with orange boxes.

        Only analyzes red intersections (those outside red zones/side cross zones).
        Uses the same exact settings as orange intersection clustering but keeps them separate.
        - Top 2 clusters above reference price: Dark purple (#7C3AED) for tightest, Light purple (#A78BFA) for 2nd tightest
        - Top 2 clusters below reference price: Dark purple (#7C3AED) for tightest, Light purple (#A78BFA) for 2nd tightest
        - Orange boxes mark the highest and lowest intersections in each cluster

        Args:
            red_intersections: List of (x, y, original_color, dot_reference) tuples representing red intersection data
            all_curves: List of curve data for peak/trough detection (optional)
        """
        try:
            import numpy as np
            from sklearn.cluster import DBSCAN

            if len(red_intersections) < 3:
                logger.info("Not enough red intersections for clustering")
                return

            # Extract coordinates and dot references from red intersection data
            points = np.array([(x, y) for x, y, _, _ in red_intersections])
            dot_references = [dot_ref for _, _, _, dot_ref in red_intersections]
            logger.info(f"Starting DBSCAN clustering on {len(points)} red intersection points (outside red zones)")

            # Get reference price to separate above/below clusters
            reference_price = self.get_reference_price_for_clustering()
            logger.info(f"Using reference price {reference_price:.2f} to separate above/below clusters")

            # Normalize the data for better clustering
            # Since x and y might have different scales, we normalize them
            x_range = points[:, 0].max() - points[:, 0].min()
            y_range = points[:, 1].max() - points[:, 1].min()

            if x_range == 0 or y_range == 0:
                logger.info("All red intersections are on a line, skipping clustering")
                return

            # Create normalized points for clustering
            normalized_points = points.copy()
            normalized_points[:, 0] = (points[:, 0] - points[:, 0].min()) / x_range
            normalized_points[:, 1] = (points[:, 1] - points[:, 1].min()) / y_range

            # DBSCAN parameters for very tight clustering (same as orange intersections)
            # eps: maximum distance between points in a cluster (in normalized space)
            # min_samples: minimum number of points to form a cluster
            eps = self.intersection_eps_primary  # Use configurable parameter
            min_samples = self.intersection_min_samples_primary  # Use configurable parameter

            # Perform DBSCAN clustering
            dbscan = DBSCAN(eps=eps, min_samples=min_samples)
            cluster_labels = dbscan.fit_predict(normalized_points)

            # Find unique clusters (excluding noise points labeled as -1)
            unique_clusters = np.unique(cluster_labels)
            valid_clusters = unique_clusters[unique_clusters != -1]

            logger.info(f"DBSCAN found {len(valid_clusters)} red intersection clusters from {len(points)} points")

            if len(valid_clusters) == 0:
                logger.info("No red intersection clusters found, trying with more relaxed parameters")
                # Try with more relaxed parameters but still keep them very tight
                eps = self.intersection_eps_fallback  # Use configurable fallback parameter
                min_samples = self.intersection_min_samples_fallback  # Use configurable fallback parameter
                dbscan = DBSCAN(eps=eps, min_samples=min_samples)
                cluster_labels = dbscan.fit_predict(normalized_points)
                unique_clusters = np.unique(cluster_labels)
                valid_clusters = unique_clusters[unique_clusters != -1]
                logger.info(f"Relaxed DBSCAN found {len(valid_clusters)} red intersection clusters")

            if len(valid_clusters) == 0:
                logger.info("Still no red intersection clusters found, skipping cluster visualization")
                return

            # Calculate cluster statistics (same logic as orange intersections)
            cluster_stats = []
            for cluster_id in valid_clusters:
                cluster_mask = cluster_labels == cluster_id
                cluster_points = points[cluster_mask]
                center_x = np.mean(cluster_points[:, 0])
                center_y = np.mean(cluster_points[:, 1])

                # Calculate cluster radius using a very tight approach
                distances = np.sqrt((cluster_points[:, 0] - center_x)**2 +
                                  (cluster_points[:, 1] - center_y)**2)

                # Use minimum distance for extremely tight clustering
                min_distance = np.min(distances[distances > 0]) if len(distances[distances > 0]) > 0 else 0.01

                # Make radius extremely small - just the minimum distance between points
                radius = max(min_distance * 0.5, 0.01)  # Very tight: 50% of min distance, min 0.01 units

                # Calculate tightness metrics for ranking
                avg_distance = np.mean(distances)
                max_distance = np.max(distances)
                std_distance = np.std(distances) if len(distances) > 1 else 0.0

                cluster_stats.append({
                    'id': cluster_id,
                    'center': (center_x, center_y),
                    'radius': radius,
                    'size': len(cluster_points),
                    'points': cluster_points,
                    'avg_distance': avg_distance,
                    'max_distance': max_distance,
                    'std_distance': std_distance,
                    'tightness_score': 1.0 / (avg_distance + 0.001)  # Higher score = tighter cluster
                })

            # Separate clusters above and below reference price
            above_clusters = [c for c in cluster_stats if c['center'][1] > reference_price]
            below_clusters = [c for c in cluster_stats if c['center'][1] <= reference_price]

            # Sort by tightness score (descending - tightest first)
            above_clusters.sort(key=lambda x: x['tightness_score'], reverse=True)
            below_clusters.sort(key=lambda x: x['tightness_score'], reverse=True)

            logger.info(f"Red intersection clusters: {len(above_clusters)} above reference price, {len(below_clusters)} below")

            # Highlight dots in the top 2 clusters on each side
            highlighted_dots = 0

            # Colors for cluster highlighting (different from orange intersections)
            cluster_colors = ['#7C3AED', '#A78BFA']  # Dark purple, Light purple

            # Process above clusters (top 2)
            for i, cluster in enumerate(above_clusters[:2]):
                color = cluster_colors[i] if i < len(cluster_colors) else '#60A5FA'
                cluster_id = cluster['id']
                cluster_mask = cluster_labels == cluster_id

                # Update dot colors for this cluster
                for j, is_in_cluster in enumerate(cluster_mask):
                    if is_in_cluster:
                        dot_ref = dot_references[j]
                        dot_ref.setPen(pg.mkPen(color=color, width=2))
                        dot_ref.setBrush(pg.mkBrush(color=color))
                        highlighted_dots += 1

                # NOTE: Zone creation now handled by priority ranking system
                # extremes = self.add_cluster_boundary_box(cluster['points'], all_curves, reference_price=reference_price)
                # if extremes is not None:
                #     boxes_added += 1
                #     highest_x, highest_y = extremes['highest']
                #     lowest_x, lowest_y = extremes['lowest']
                logger.info(f"Red intersection cluster {cluster_id} detected - zones handled by priority ranking")

            # Process below clusters (top 2)
            for i, cluster in enumerate(below_clusters[:2]):
                color = cluster_colors[i] if i < len(cluster_colors) else '#60A5FA'
                cluster_id = cluster['id']
                cluster_mask = cluster_labels == cluster_id

                # Update dot colors for this cluster
                for j, is_in_cluster in enumerate(cluster_mask):
                    if is_in_cluster:
                        dot_ref = dot_references[j]
                        dot_ref.setPen(pg.mkPen(color=color, width=2))
                        dot_ref.setBrush(pg.mkBrush(color=color))
                        highlighted_dots += 1

                # NOTE: Zone creation now handled by priority ranking system
                # extremes = self.add_cluster_boundary_box(cluster['points'], all_curves, reference_price=reference_price)
                # if extremes is not None:
                #     boxes_added += 1
                #     highest_x, highest_y = extremes['highest']
                #     lowest_x, lowest_y = extremes['lowest']
                logger.info(f"Red intersection cluster {cluster_id} detected - zones handled by priority ranking")

            logger.info(f"Successfully highlighted {highlighted_dots} red intersection dots in tightest clusters (purple colors): "
                       f"{len(above_clusters[:2])} above + {len(below_clusters[:2])} below reference price")
            logger.info(f"Red intersection clusters detected - zones handled by priority ranking system")

            # Store results for priority ranking system
            selected_clusters = above_clusters[:2] + below_clusters[:2]
            non_clustered_intersections = self.get_non_clustered_red_intersections(red_intersections, cluster_labels)

            # Store red intersection data for priority ranking
            self.store_intersection_data_for_priority_ranking(
                orange_clusters=getattr(self, 'orange_intersect_clusters', None),
                orange_non_clusters=getattr(self, 'orange_intersect_non_clusters', None),
                red_clusters=selected_clusters,
                red_non_clusters=non_clustered_intersections
            )

        except ImportError:
            logger.error("scikit-learn not available for clustering. Install with: pip install scikit-learn")
        except Exception as e:
            logger.error(f"Error in red intersection clustering: {str(e)}", exc_info=True)

    def add_cluster_boundary_box(self, cluster_points, all_peaks=None, all_troughs=None, reference_price=None):
        """
        Add a single orange box that encompasses the entire clustered intersection area.
        The top of the box aligns with the highest intersection point and
        the bottom aligns with the lowest intersection point.

        If the zone is below current price, extends the zone down to the nearest trough that is lower than the zone's low.
        If the zone is above current price, extends the zone up to the nearest peak that is higher than the zone's high.

        Args:
            cluster_points: numpy array of (x, y) coordinates for all points in the cluster
            all_peaks: List of (x, y) tuples for all peaks in the density curves
            all_troughs: List of (x, y) tuples for all troughs in the density curves
            reference_price: Current price for determining zone position
        """
        try:
            if len(cluster_points) == 0:
                logger.warning("No cluster points provided for boundary box")
                return

            # Find the y boundaries of the cluster intersections
            min_y = np.min(cluster_points[:, 1])  # Bottom intersection (lowest)
            max_y = np.max(cluster_points[:, 1])  # Top intersection (highest)

            # Use the density gradient x-axis range instead of cluster x boundaries
            # This matches the density gradient range from 1.5 to 3.5
            min_x = 1.5
            max_x = 3.5

            # Determine cluster center for position relative to reference price
            cluster_center_y = (min_y + max_y) / 2

            # Extend the zone based on position relative to current price
            extended_min_y = min_y
            extended_max_y = max_y

            if reference_price is not None:
                if cluster_center_y < reference_price:
                    # Zone is below current price - extend down to nearest lower trough
                    if all_troughs:
                        lower_troughs = [trough_y for _, trough_y in all_troughs if trough_y < min_y]
                        if lower_troughs:
                            nearest_lower_trough = max(lower_troughs)  # Closest trough below the zone
                            extended_min_y = nearest_lower_trough
                            logger.info(f"Extended zone below current price down to trough at {nearest_lower_trough:.2f}")
                        else:
                            logger.info("No lower troughs found for zone extension")

                elif cluster_center_y > reference_price:
                    # Zone is above current price - extend up to nearest higher peak
                    if all_peaks:
                        higher_peaks = [peak_y for _, peak_y in all_peaks if peak_y > max_y]
                        if higher_peaks:
                            nearest_higher_peak = min(higher_peaks)  # Closest peak above the zone
                            extended_max_y = nearest_higher_peak
                            logger.info(f"Extended zone above current price up to peak at {nearest_higher_peak:.2f}")
                        else:
                            logger.info("No higher peaks found for zone extension")

            # Use the full density gradient x-axis range (no horizontal padding needed)
            left = min_x  # Start at the beginning of the density gradient range
            bottom = extended_min_y  # Bottom edge aligns with lowest intersection or extended trough
            box_width = max_x - min_x  # Full width of the density gradient
            box_height = extended_max_y - extended_min_y  # Height spans from extended bottom to extended top

            # Ensure minimum height for visibility when all points are at same Y level
            if box_height < 0.1:
                box_height = 0.1
                bottom = extended_min_y - 0.05  # Center the small box around the intersection level

            # Create orange box using a rectangle
            box_rect = pg.QtWidgets.QGraphicsRectItem(left, bottom, box_width, box_height)

            # Set orange color and styling
            orange_pen = pg.mkPen(color='orange', width=2)
            box_rect.setPen(orange_pen)
            box_rect.setBrush(pg.mkBrush(color=(255, 165, 0, 30)))  # Very light semi-transparent orange fill

            # Add the box to the plot
            self.plot_widget.addItem(box_rect)

            # Log the final boundaries
            if extended_min_y != min_y or extended_max_y != max_y:
                logger.debug(f"Added extended orange boundary box: original ({min_y:.2f} to {max_y:.2f}), extended ({extended_min_y:.2f} to {extended_max_y:.2f})")
            else:
                logger.debug(f"Added orange boundary box with top at {extended_max_y:.2f} and bottom at {extended_min_y:.2f}")

        except Exception as e:
            logger.error(f"Error adding cluster boundary box: {str(e)}", exc_info=True)

    def get_cluster_filtered_area(self, cluster_center_y, reference_price):
        """
        Determine which filtered area a cluster belongs to based on its center Y position.

        Args:
            cluster_center_y: Y-coordinate of the cluster center
            reference_price: Current price for determining position

        Returns:
            str: The filtered area name ('1%-25% above', '25%-50% above', '50%-25% below', '25%-1% below', or 'other')
        """
        try:
            # Get the band values for area determination
            highest_high = getattr(self, 'highest_high', None)
            maxavg_high = getattr(self, 'maxavg_high', None)
            high_median = getattr(self, 'high_median', None)
            low_median = getattr(self, 'low_median', None)
            maxavg_low = getattr(self, 'maxavg_low', None)
            lowest_low = getattr(self, 'lowest_low', None)

            if cluster_center_y > reference_price:
                # Above current price
                if highest_high is not None and maxavg_high is not None:
                    if maxavg_high <= cluster_center_y <= highest_high:
                        return '1%-25% above'
                if maxavg_high is not None and high_median is not None:
                    if high_median <= cluster_center_y <= maxavg_high:
                        return '25%-50% above'
                return 'other above'
            else:
                # Below current price
                if low_median is not None and maxavg_low is not None:
                    if maxavg_low <= cluster_center_y <= low_median:
                        return '50%-25% below'
                if lowest_low is not None and maxavg_low is not None:
                    if lowest_low <= cluster_center_y <= maxavg_low:
                        return '25%-1% below'
                return 'other below'

        except Exception as e:
            logger.error(f"Error determining cluster filtered area: {str(e)}")
            return 'unknown'



    def get_reference_price_for_clustering(self):
        """
        Get the reference price for separating above/below clusters.
        Uses the same logic as the main density graph generation.

        Returns:
            float: Reference price value
        """
        try:
            reference_price_info = self.get_reference_price()
            if reference_price_info is not None:
                reference_price, _, _ = reference_price_info
                return reference_price
            else:
                # Fallback to a reasonable default if reference price not available
                logger.warning("Could not get reference price for clustering, using default 600.0")
                return 600.0
        except Exception as e:
            logger.warning(f"Error getting reference price for clustering: {str(e)}, using default 600.0")
            return 600.0

    def clear_density_zones(self):
        """Clear all density zones from the plot and storage."""
        try:
            # Remove zones from plot
            for zone in self.density_zones:
                if zone.scene():
                    self.plot_widget.removeItem(zone)

            # Clear storage
            self.density_zones.clear()
            self.zone_id_counter = 0

            logger.info("Cleared all density zones")

            # Notify parent about zone changes for save button visibility
            self.notify_parent_zones_updated()

        except Exception as e:
            logger.error(f"Error clearing density zones: {str(e)}")
            # Also notify parent on error
            self.notify_parent_zones_updated()

    def get_confluence_points(self):
        """
        Get all confluence points (peaks, troughs, intersections) for zone navigation.

        Returns:
            list: List of confluence point dictionaries with 'y', 'type', and 'data' keys
        """
        try:
            confluence_points = []

            # Add intersection points
            if hasattr(self, 'orange_intersect_clusters'):
                for cluster in self.orange_intersect_clusters:
                    if 'points' in cluster:
                        for point in cluster['points']:
                            confluence_points.append({
                                'y': point[1],  # y-coordinate
                                'type': 'orange_intersection',
                                'data': point
                            })

            if hasattr(self, 'orange_intersect_non_clusters'):
                for point in self.orange_intersect_non_clusters:
                    confluence_points.append({
                        'y': point[1],
                        'type': 'orange_intersection_single',
                        'data': point
                    })

            if hasattr(self, 'red_intersect_clusters'):
                for cluster in self.red_intersect_clusters:
                    if 'points' in cluster:
                        for point in cluster['points']:
                            confluence_points.append({
                                'y': point[1],
                                'type': 'red_intersection',
                                'data': point
                            })

            if hasattr(self, 'red_intersect_non_clusters'):
                for point in self.red_intersect_non_clusters:
                    confluence_points.append({
                        'y': point[1],
                        'type': 'red_intersection_single',
                        'data': point
                    })

            # Add peak and trough points
            if hasattr(self, 'peak_trough_clusters'):
                for cluster in self.peak_trough_clusters:
                    if 'points' in cluster:
                        for point in cluster['points']:
                            confluence_points.append({
                                'y': point[1],
                                'type': 'peak_trough_cluster',
                                'data': point
                            })

            if hasattr(self, 'peak_trough_non_clusters'):
                for point in self.peak_trough_non_clusters:
                    confluence_points.append({
                        'y': point[1],
                        'type': 'peak_trough_single',
                        'data': point
                    })

            # Sort by y-coordinate for efficient navigation
            confluence_points.sort(key=lambda p: p['y'])

            logger.debug(f"Found {len(confluence_points)} confluence points for navigation")
            return confluence_points

        except Exception as e:
            logger.error(f"Error getting confluence points: {str(e)}")
            return []

    def get_zone_by_id(self, zone_id):
        """
        Get a zone by its ID.

        Args:
            zone_id: The unique identifier of the zone

        Returns:
            EditableDensityZone or None: The zone object if found
        """
        for zone in self.density_zones:
            if zone.zone_id == zone_id:
                return zone
        return None

    def remove_zone(self, zone_id):
        """
        Remove a zone by its ID.

        Args:
            zone_id: The unique identifier of the zone to remove
        """
        try:
            zone = self.get_zone_by_id(zone_id)
            if zone:
                # Remove from plot
                if zone.scene():
                    self.plot_widget.removeItem(zone)

                # Remove from storage
                self.density_zones.remove(zone)

                logger.info(f"Removed zone {zone_id}")
                return True
            else:
                logger.warning(f"Zone {zone_id} not found for removal")
                return False

        except Exception as e:
            logger.error(f"Error removing zone {zone_id}: {str(e)}")
            return False



    def update_manual_zone_button_visibility(self):
        """Update the visibility of the quick zone creation buttons based on zones state."""
        try:
            # Update quick zone buttons
            if hasattr(self, 'quick_support_btn'):
                self.quick_support_btn.setVisible(self.density_zones_active)
            if hasattr(self, 'quick_resistance_btn'):
                self.quick_resistance_btn.setVisible(self.density_zones_active)
            if hasattr(self, 'clear_zones_btn'):
                self.clear_zones_btn.setVisible(self.density_zones_active)
            if hasattr(self, 'reset_edits_btn'):
                self.reset_edits_btn.setVisible(self.density_zones_active)

        except Exception as e:
            logger.error(f"Error updating quick zone button visibility: {str(e)}")





    def create_enhanced_manual_zone(self, center_y):
        """Create an enhanced manual zone using Zone Creator methodology."""
        try:
            # Smart zone height calculation based on context
            zone_height = self.calculate_smart_zone_height(center_y)

            # Calculate zone bounds with smart positioning (same width as other zones)
            min_x = 1.5  # Standard x range for zones
            max_x = 3.5
            min_y = center_y - (zone_height / 2)
            max_y = center_y + (zone_height / 2)

            # Smart snapping to nearby confluence points
            snap_result = self.snap_zone_to_confluence(min_y, max_y, center_y)
            if snap_result:
                min_y, max_y = snap_result
                logger.info(f"Snapped manual zone to confluence points: {min_y:.2f} - {max_y:.2f}")

            # Create zone bounds dictionary
            zone_bounds = {
                'min_x': min_x,
                'max_x': max_x,
                'min_y': min_y,
                'max_y': max_y
            }

            # Smart zone naming and classification
            zone_info = self.classify_manual_zone(center_y, min_y, max_y)
            area_name = zone_info['area_name']
            zone_type = zone_info['zone_type']
            zone_color = zone_info['color']
            description = zone_info['description']

            # Create editable density zone with enhanced properties
            zone_id = self.zone_id_counter
            self.zone_id_counter += 1

            editable_zone = EditableDensityZone(
                zone_id=zone_id,
                zone_bounds=zone_bounds,
                zone_color=zone_color,
                zone_type=zone_type,
                area_name=area_name,
                primary_element={
                    'type': 'manual',
                    'description': description,
                    'created_at': self.get_current_timestamp(),
                    'center_position': center_y
                },
                combining_elements=[],
                density_graph_tab=self
            )

            # Add zone to plot
            self.plot_widget.addItem(editable_zone)

            # Store zone reference for management
            self.density_zones.append(editable_zone)

            logger.info(f"Created enhanced manual zone {zone_id} '{area_name}': "
                       f"({zone_bounds['min_y']:.2f} to {zone_bounds['max_y']:.2f}) - {zone_type}")

            # Enhanced status message
            self.status_label.setText(f"Manual {zone_type} zone created: {min_y:.2f} to {max_y:.2f}")

            # Notify parent about zone creation for save button visibility
            self.notify_parent_zones_updated()

            return editable_zone

        except Exception as e:
            logger.error(f"Error creating enhanced manual zone: {str(e)}")
            return None

    def calculate_smart_zone_height(self, center_y):
        """Calculate smart zone height based on context and nearby elements."""
        try:
            # Default zone height
            default_height = 0.5

            # Get reference price for context
            reference_price_info = self.get_reference_price()
            reference_price = reference_price_info[0] if reference_price_info else center_y

            # Calculate percentage distance from reference price
            if reference_price > 0:
                distance_percent = abs(center_y - reference_price) / reference_price * 100

                # Adjust zone height based on distance from reference price
                if distance_percent < 1:  # Very close to reference price
                    zone_height = 0.3  # Smaller zone
                elif distance_percent < 3:  # Moderately close
                    zone_height = 0.5  # Default zone
                else:  # Far from reference price
                    zone_height = 0.8  # Larger zone
            else:
                zone_height = default_height

            # Check for nearby confluence points to adjust height
            nearby_points = self.get_nearby_confluence_points(center_y, radius=2.0)
            if len(nearby_points) >= 2:
                # If there are multiple confluence points nearby, make zone smaller
                zone_height = min(zone_height, 0.4)

            logger.debug(f"Calculated smart zone height: {zone_height:.2f} for position {center_y:.2f}")
            return zone_height

        except Exception as e:
            logger.error(f"Error calculating smart zone height: {str(e)}")
            return 0.5  # Fallback to default

    def snap_zone_to_confluence(self, min_y, max_y, center_y):
        """Snap zone boundaries to nearby confluence points if beneficial."""
        try:
            snap_distance = 0.3  # Maximum distance to snap

            # Get all available snap points
            snap_points = self.get_all_snap_points()
            if not snap_points:
                return None

            # Find best snap points for top and bottom
            best_top_snap = None
            best_bottom_snap = None
            min_top_distance = float('inf')
            min_bottom_distance = float('inf')

            for point_y in snap_points:
                # Check for top boundary snap
                top_distance = abs(point_y - max_y)
                if top_distance < snap_distance and top_distance < min_top_distance and point_y > center_y:
                    best_top_snap = point_y
                    min_top_distance = top_distance

                # Check for bottom boundary snap
                bottom_distance = abs(point_y - min_y)
                if bottom_distance < snap_distance and bottom_distance < min_bottom_distance and point_y < center_y:
                    best_bottom_snap = point_y
                    min_bottom_distance = bottom_distance

            # Apply snapping if beneficial
            snapped_min_y = best_bottom_snap if best_bottom_snap is not None else min_y
            snapped_max_y = best_top_snap if best_top_snap is not None else max_y

            # Ensure minimum zone height
            if snapped_max_y - snapped_min_y < 0.2:
                return None  # Don't snap if it makes zone too small

            # Only return if at least one boundary was snapped
            if best_top_snap is not None or best_bottom_snap is not None:
                return (snapped_min_y, snapped_max_y)

            return None

        except Exception as e:
            logger.error(f"Error snapping zone to confluence: {str(e)}")
            return None

    def classify_manual_zone(self, center_y, _, __):
        """Classify and name the manual zone based on its position and context."""
        try:
            # Get reference price for classification
            reference_price_info = self.get_reference_price()
            reference_price = reference_price_info[0] if reference_price_info else 0

            # Determine zone position relative to reference price
            if center_y > reference_price:
                position = "above"
                base_color = "#f44336"  # Red for resistance areas
                zone_type = "resistance_zone"
            else:
                position = "below"
                base_color = "#4caf50"  # Green for support areas
                zone_type = "support_zone"

            # Calculate zone strength based on position
            if reference_price > 0:
                distance_percent = abs(center_y - reference_price) / reference_price * 100

                if distance_percent < 1:
                    strength = "Key"
                    color = "#00bcd4"  # Cyan for key levels
                elif distance_percent < 3:
                    strength = "Major"
                    color = base_color
                else:
                    strength = "Minor"
                    color = "#9e9e9e"  # Gray for minor levels
            else:
                strength = "Manual"
                color = "#00bcd4"  # Default cyan

            # Generate descriptive name
            area_name = f"{strength} {zone_type.replace('_', ' ').title()} ({center_y:.2f})"

            # Create description
            description = f"User-created {strength.lower()} {zone_type.replace('_', ' ')} zone {position} reference price"

            return {
                'area_name': area_name,
                'zone_type': zone_type,
                'color': color,
                'description': description,
                'strength': strength,
                'position': position
            }

        except Exception as e:
            logger.error(f"Error classifying manual zone: {str(e)}")
            return {
                'area_name': f"Manual Zone ({center_y:.2f})",
                'zone_type': "manual_zone",
                'color': "#00bcd4",
                'description': "User-created manual zone",
                'strength': "Manual",
                'position': "unknown"
            }

    def get_nearby_confluence_points(self, center_y, radius=2.0):
        """Get confluence points near the specified Y position."""
        try:
            all_points = self.get_all_snap_points()
            if not all_points:
                return []

            nearby_points = []
            for point_y in all_points:
                if abs(point_y - center_y) <= radius:
                    nearby_points.append(point_y)

            return nearby_points

        except Exception as e:
            logger.error(f"Error getting nearby confluence points: {str(e)}")
            return []

    def get_current_timestamp(self):
        """Get current timestamp in ISO format."""
        try:
            from datetime import datetime
            return datetime.now().isoformat()
        except Exception as e:
            logger.error(f"Error getting timestamp: {str(e)}")
            return "unknown"

    def create_quick_support_zone(self):
        """Create a quick support zone around current reference price."""
        try:
            reference_price_info = self.get_reference_price()
            if not reference_price_info:
                logger.warning("Cannot create quick support zone - no reference price available")
                return None

            reference_price = reference_price_info[0]
            margin = reference_price * 0.02  # 2% margin

            # Create support zone below reference price
            center_y = reference_price - margin/2
            self.create_enhanced_manual_zone(center_y)

            logger.info(f"Created quick support zone around {reference_price:.2f}")

        except Exception as e:
            logger.error(f"Error creating quick support zone: {str(e)}")

    def create_quick_resistance_zone(self):
        """Create a quick resistance zone around current reference price."""
        try:
            reference_price_info = self.get_reference_price()
            if not reference_price_info:
                logger.warning("Cannot create quick resistance zone - no reference price available")
                return None

            reference_price = reference_price_info[0]
            margin = reference_price * 0.02  # 2% margin

            # Create resistance zone above reference price
            center_y = reference_price + margin/2
            self.create_enhanced_manual_zone(center_y)

            logger.info(f"Created quick resistance zone around {reference_price:.2f}")

        except Exception as e:
            logger.error(f"Error creating quick resistance zone: {str(e)}")

    def clear_manual_zones(self):
        """Clear all manually created zones (support and resistance zones)."""
        try:
            # Count zones to be removed
            zones_to_remove = []

            # Find all manual zones (zones with support_zone or resistance_zone type)
            for zone in self.density_zones:
                if hasattr(zone, 'zone_type') and zone.zone_type in ['support_zone', 'resistance_zone']:
                    zones_to_remove.append(zone)

            if not zones_to_remove:
                logger.info("No manual zones to clear")
                self.status_label.setText("No manual zones to clear")
                return

            # Confirm deletion with user
            reply = QtWidgets.QMessageBox.question(
                self, "Clear Manual Zones",
                f"Are you sure you want to clear {len(zones_to_remove)} manual zone(s)?",
                QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No,
                QtWidgets.QMessageBox.StandardButton.No
            )

            if reply != QtWidgets.QMessageBox.StandardButton.Yes:
                return

            # Remove zones from plot and list
            removed_count = 0
            for zone in zones_to_remove:
                try:
                    # Remove from plot
                    self.plot_widget.removeItem(zone)

                    # Remove from zones list
                    if zone in self.density_zones:
                        self.density_zones.remove(zone)

                    removed_count += 1
                    logger.debug(f"Removed manual zone {zone.zone_id}: {zone.area_name}")

                except Exception as e:
                    logger.error(f"Error removing zone {zone.zone_id}: {str(e)}")

            logger.info(f"Cleared {removed_count} manual zones")
            self.status_label.setText(f"Cleared {removed_count} manual zone(s)")

            # Notify parent about zone changes for save button visibility
            self.notify_parent_zones_updated()

        except Exception as e:
            logger.error(f"Error clearing manual zones: {str(e)}")
            self.status_label.setText("Error clearing manual zones")
            # Also notify parent on error
            self.notify_parent_zones_updated()

    def eventFilter(self, obj, event):
        """Event filter to handle double-click zone creation without interfering with zone dragging."""
        try:
            # Only handle events for the plot widget
            if obj != self.plot_widget:
                return super().eventFilter(obj, event)

            # Handle double-click events for zone creation
            if event.type() == QtCore.QEvent.Type.MouseButtonDblClick:
                if event.button() == QtCore.Qt.MouseButton.LeftButton:
                    # Only create zones if Density Zones is active
                    if not self.density_zones_active:
                        logger.debug("Density Zones is inactive - ignoring double-click")
                        return False  # Let the event propagate

                    # Check if the double-click is on empty space (not on a zone)
                    # Get the item at the click position
                    scene_pos = self.plot_widget.mapToScene(event.pos())
                    item_at_pos = self.plot_widget.scene().itemAt(scene_pos, self.plot_widget.transform())

                    # If the click is on a zone, let the zone handle it (for edit mode toggle)
                    if isinstance(item_at_pos, EditableDensityZone):
                        return False  # Let the zone handle the double-click

                    # Double-click on empty space - create new zone
                    view_box = self.plot_widget.getPlotItem().getViewBox()
                    plot_pos = view_box.mapSceneToView(event.pos())
                    center_y = plot_pos.y()

                    # Create enhanced manual zone using Zone Creator methodology
                    created_zone = self.create_enhanced_manual_zone(center_y)

                    if created_zone:
                        logger.info(f"Created manual zone via double-click at y={center_y:.2f}")
                        return True  # Event handled
                    else:
                        logger.warning(f"Failed to create manual zone via double-click at y={center_y:.2f}")
                        return False

            # Let other events pass through
            return False

        except Exception as e:
            logger.error(f"Error in event filter: {str(e)}")
            return False

    def reset_zone_edits(self):
        """Reset all edited zones back to their original bounds."""
        try:
            # Find all zones that have been edited
            edited_zones = []

            for zone in self.density_zones:
                if hasattr(zone, 'has_been_edited') and zone.has_been_edited():
                    edited_zones.append(zone)

            if not edited_zones:
                logger.info("No edited zones to reset")
                self.status_label.setText("No edited zones to reset")
                return

            # Confirm reset with user
            reply = QtWidgets.QMessageBox.question(
                self, "Reset Zone Edits",
                f"Are you sure you want to reset {len(edited_zones)} edited zone(s) to their original positions?",
                QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No,
                QtWidgets.QMessageBox.StandardButton.No
            )

            if reply != QtWidgets.QMessageBox.StandardButton.Yes:
                return

            # Reset zones to original bounds
            reset_count = 0
            for zone in edited_zones:
                try:
                    zone.reset_to_original_bounds()
                    reset_count += 1
                    logger.debug(f"Reset zone {zone.zone_id}: {zone.area_name}")

                except Exception as e:
                    logger.error(f"Error resetting zone {zone.zone_id}: {str(e)}")

            logger.info(f"Reset {reset_count} zones to original bounds")
            self.status_label.setText(f"Reset {reset_count} zone(s) to original positions")

        except Exception as e:
            logger.error(f"Error resetting zone edits: {str(e)}")
            self.status_label.setText("Error resetting zone edits")

