import os
import json
import subprocess
import time
import sys
import atexit
import math
import random
from PyQt6 import QtWidgets, QtCore, QtGui

class Particle:
    """A single particle for the particle system animation."""
    def __init__(self, x, y, vx, vy, size, color, life_span=3000):
        self.x = x
        self.y = y
        self.vx = vx  # velocity x
        self.vy = vy  # velocity y
        self.size = size
        self.color = color
        self.life_span = life_span  # in milliseconds
        self.age = 0
        self.opacity = 1.0

    def update(self, dt):
        """Update particle position and properties."""
        self.x += self.vx * dt
        self.y += self.vy * dt
        self.age += dt

        # Fade out over time
        self.opacity = max(0, 1.0 - (self.age / self.life_span))

        # Slow down over time
        self.vx *= 0.99
        self.vy *= 0.99

    def is_alive(self):
        """Check if particle is still alive."""
        return self.age < self.life_span and self.opacity > 0.01

class EnhancedSpinner(QtWidgets.QWidget):
    """Enhanced spinner with multiple rotating elements and glow effects."""

    def __init__(self, size=140, primary_color=None, secondary_color=None):
        super().__init__()

        self.size = size
        self.primary_color = primary_color or QtGui.QColor("#007acc")
        self.secondary_color = secondary_color or QtGui.QColor("#4CAF50")
        self.accent_color = QtGui.QColor("#ff6b6b")

        self.setFixedSize(size, size)

        # Animation properties
        self.rotation_angle = 0
        self.pulse_phase = 0
        self.orbit_angle = 0

        # Timer for updates
        self.animation_timer = QtCore.QTimer(self)
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(16)  # ~60 FPS

    def update_animation(self):
        """Update animation properties."""
        self.rotation_angle = (self.rotation_angle + 3) % 360
        self.pulse_phase = (self.pulse_phase + 0.1) % (2 * math.pi)
        self.orbit_angle = (self.orbit_angle + 2) % 360
        self.update()

    def paintEvent(self, _event):
        """Paint the enhanced spinner."""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)

        center = QtCore.QPointF(self.size / 2, self.size / 2)

        # Draw outer rotating ring
        painter.save()
        painter.translate(center)
        painter.rotate(self.rotation_angle)

        outer_radius = self.size * 0.35
        gradient = QtGui.QConicalGradient(0, 0, 0)
        gradient.setColorAt(0, QtGui.QColor(self.primary_color.red(), self.primary_color.green(), self.primary_color.blue(), 0))
        gradient.setColorAt(0.3, self.primary_color)
        gradient.setColorAt(0.7, self.primary_color.lighter(150))
        gradient.setColorAt(1, QtGui.QColor(self.primary_color.red(), self.primary_color.green(), self.primary_color.blue(), 0))

        pen = QtGui.QPen(QtGui.QBrush(gradient), 4)
        pen.setCapStyle(QtCore.Qt.PenCapStyle.RoundCap)
        painter.setPen(pen)
        painter.drawArc(-outer_radius, -outer_radius, outer_radius * 2, outer_radius * 2, 0, 270 * 16)
        painter.restore()

        # Draw inner counter-rotating ring
        painter.save()
        painter.translate(center)
        painter.rotate(-self.rotation_angle * 1.5)

        inner_radius = self.size * 0.25
        gradient2 = QtGui.QConicalGradient(0, 0, 0)
        gradient2.setColorAt(0, QtGui.QColor(self.secondary_color.red(), self.secondary_color.green(), self.secondary_color.blue(), 0))
        gradient2.setColorAt(0.4, self.secondary_color)
        gradient2.setColorAt(0.8, self.secondary_color.lighter(120))
        gradient2.setColorAt(1, QtGui.QColor(self.secondary_color.red(), self.secondary_color.green(), self.secondary_color.blue(), 0))

        pen2 = QtGui.QPen(QtGui.QBrush(gradient2), 3)
        pen2.setCapStyle(QtCore.Qt.PenCapStyle.RoundCap)
        painter.setPen(pen2)
        painter.drawArc(-inner_radius, -inner_radius, inner_radius * 2, inner_radius * 2, 0, 200 * 16)
        painter.restore()

        # Draw orbiting dots
        for i in range(3):
            angle = (self.orbit_angle + i * 120) * math.pi / 180
            orbit_radius = self.size * 0.3
            dot_x = center.x() + orbit_radius * math.cos(angle)
            dot_y = center.y() + orbit_radius * math.sin(angle)

            # Pulsing size
            pulse_size = 3 + 2 * math.sin(self.pulse_phase + i * math.pi / 3)

            painter.setBrush(self.accent_color)
            painter.setPen(QtCore.Qt.PenStyle.NoPen)
            painter.drawEllipse(QtCore.QPointF(dot_x, dot_y), pulse_size, pulse_size)

        # Draw central glow
        glow_radius = 8 + 4 * math.sin(self.pulse_phase)
        glow_gradient = QtGui.QRadialGradient(center, glow_radius)
        glow_color = QtGui.QColor(self.primary_color)
        glow_color.setAlphaF(0.6)
        glow_gradient.setColorAt(0, glow_color)
        glow_color.setAlphaF(0)
        glow_gradient.setColorAt(1, glow_color)

        painter.setBrush(glow_gradient)
        painter.setPen(QtCore.Qt.PenStyle.NoPen)
        painter.drawEllipse(center, glow_radius, glow_radius)


class ParticleSystem(QtWidgets.QWidget):
    """Advanced particle system for modern loading animations."""

    def __init__(self, size=200, primary_color=None, secondary_color=None):
        super().__init__()

        self.size = size
        self.primary_color = primary_color or QtGui.QColor("#007acc")
        self.secondary_color = secondary_color or QtGui.QColor("#4CAF50")
        self.accent_color = QtGui.QColor("#ff6b6b")

        self.setFixedSize(size, size)

        # Particle system
        self.particles = []
        self.max_particles = 50

        # Animation properties
        self.animation_time = 0
        self.last_update = time.time()

        # Timer for updates
        self.animation_timer = QtCore.QTimer(self)
        self.animation_timer.timeout.connect(self.update_particles)
        self.animation_timer.start(16)  # ~60 FPS

    def create_particle(self):
        """Create a new particle."""
        center_x = self.size / 2
        center_y = self.size / 2

        # Random position around center
        angle = random.uniform(0, 2 * math.pi)
        radius = random.uniform(10, 40)
        x = center_x + radius * math.cos(angle)
        y = center_y + radius * math.sin(angle)

        # Random velocity
        vx = random.uniform(-30, 30)
        vy = random.uniform(-30, 30)

        # Random size and color
        size = random.uniform(2, 6)
        colors = [self.primary_color, self.secondary_color, self.accent_color]
        color = random.choice(colors)

        # Random lifespan
        life_span = random.uniform(2000, 4000)

        return Particle(x, y, vx, vy, size, color, life_span)

    def update_particles(self):
        """Update all particles."""
        current_time = time.time()
        dt = (current_time - self.last_update) * 1000  # Convert to milliseconds
        self.last_update = current_time
        self.animation_time += dt

        # Update existing particles
        self.particles = [p for p in self.particles if p.is_alive()]
        for particle in self.particles:
            particle.update(dt)

        # Create new particles
        while len(self.particles) < self.max_particles:
            if random.random() < 0.3:  # 30% chance per frame
                self.particles.append(self.create_particle())

        self.update()

    def paintEvent(self, _event):
        """Paint the particle system."""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)

        # Draw particles
        for particle in self.particles:
            color = QtGui.QColor(particle.color)
            color.setAlphaF(particle.opacity)

            painter.setBrush(color)
            painter.setPen(QtCore.Qt.PenStyle.NoPen)

            painter.drawEllipse(
                QtCore.QPointF(particle.x, particle.y),
                particle.size, particle.size
            )

        # Draw central glow effect
        center = QtCore.QPointF(self.size / 2, self.size / 2)
        glow_radius = 30 + 10 * math.sin(self.animation_time / 500)

        gradient = QtGui.QRadialGradient(center, glow_radius)
        glow_color = QtGui.QColor(self.primary_color)
        glow_color.setAlphaF(0.3)
        gradient.setColorAt(0, glow_color)
        glow_color.setAlphaF(0)
        gradient.setColorAt(1, glow_color)

        painter.setBrush(gradient)
        painter.setPen(QtCore.Qt.PenStyle.NoPen)
        painter.drawEllipse(center, glow_radius, glow_radius)



class LoadingScreen(QtWidgets.QWidget):
    """
    A comprehensive loading screen that combines all loading screen features:
    - Enhanced spinner with multiple rotating elements and glow effects
    - Particle system with physics simulation
    - Progress tracking with percentage and sub-messages
    - Glassmorphism effects with blur and transparency
    - Skeleton loading placeholders
    - Standalone mode for separate process execution
    - Advanced animations and visual effects

    This class provides a modern, feature-rich loading screen that can be displayed
    over any widget or run as a standalone application.
    """

    def __init__(self, parent=None, message="Loading...", theme_colors=None,
                 show_progress=False, show_particles=True, show_skeleton=False,
                 standalone_mode=False, position=None, size=None):
        """
        Initialize the loading screen.

        Args:
            parent: Parent widget (None for standalone mode)
            message: Message to display
            theme_colors: Dictionary of theme colors
            show_progress: Whether to show progress bar and percentage
            show_particles: Whether to show particle system animation
            show_skeleton: Whether to show skeleton loading placeholders
            standalone_mode: Whether to run as standalone window
            position: Position for standalone mode (x, y)
            size: Size for standalone mode (width, height)
        """
        super().__init__(parent)

        # Store parameters
        self.parent = parent
        self.message = message
        self.show_progress = show_progress
        self.show_particles = show_particles
        self.show_skeleton = show_skeleton
        self.standalone_mode = standalone_mode
        self.position = position
        self.size = size

        # Progress tracking
        self.progress = 0  # Progress value (0-100)
        self.sub_message = ""  # Additional message for progress details

        # Standalone mode setup
        if self.standalone_mode:
            self.setup_standalone_mode()

        # Use provided theme colors with comprehensive defaults
        default_colors = {
            'background': '#1e1e1e',
            'text': '#E0E0E0',
            'primary_accent': '#007acc',
            'secondary_accent': '#4CAF50',
            'accent_color': '#ff6b6b',
            'glass_bg': 'rgba(255, 255, 255, 0.1)',
            'glass_border': 'rgba(255, 255, 255, 0.2)',
            'glow': '#007acc',
            'progress_bg': '#2d2d2d',
            'progress_fill': '#007acc',
            'skeleton_bg': '#2d2d2d',
            'skeleton_highlight': '#404040',
            'bullish': '#4CAF50',  # Common alias for secondary_accent
            'bearish': '#ff6b6b'   # Common alias for accent_color
        }

        # Merge provided theme colors with defaults
        if theme_colors:
            self.theme_colors = {**default_colors, **theme_colors}
            # Ensure secondary_accent exists, fallback to bullish or primary_accent
            if 'secondary_accent' not in self.theme_colors:
                self.theme_colors['secondary_accent'] = self.theme_colors.get('bullish', self.theme_colors['primary_accent'])
            # Ensure accent_color exists, fallback to bearish or a default
            if 'accent_color' not in self.theme_colors:
                self.theme_colors['accent_color'] = self.theme_colors.get('bearish', '#ff6b6b')
        else:
            self.theme_colors = default_colors

        # Particle system
        self.particles = []
        self.max_particles = min(30, max(0, 30 if show_particles else 0))  # Ensure valid range

        # Skeleton elements
        self.skeleton_bars = []
        self.shimmer_position = 0

        # Enhanced spinner animation properties
        self.rotation_angle = 0
        self.orbit_angle = 0

        # General animation properties
        self.angle = 0
        self.pulse_phase = 0
        self.glow_intensity = 0.5
        self.animation_time = 0
        self.last_frame_time = time.time()

        # Glassmorphism properties
        self.glass_opacity = 0.0
        self.blur_radius = 0

        # Set up the UI
        self.init_ui()

        # High-performance animation timer (60 FPS)
        self.animation_timer = QtCore.QTimer(self)
        self.animation_timer.timeout.connect(self.safe_update_animation)
        self.animation_timer.start(16)  # 16ms = ~60 FPS

        # Fade-in animation
        self.fade_in_animation = QtCore.QPropertyAnimation(self, b"windowOpacity")
        self.fade_in_animation.setDuration(300)
        self.fade_in_animation.setStartValue(0.0)
        self.fade_in_animation.setEndValue(1.0)
        self.fade_in_animation.setEasingCurve(QtCore.QEasingCurve.Type.OutCubic)

        # Hide initially
        self.hide()

    def init_ui(self):
        """Initialize the unified user interface with all features."""
        # Set up size
        if self.parent:
            self.setGeometry(self.parent.rect())
            self.parent.installEventFilter(self)

        # Set up appearance for glassmorphism
        self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)

        # Create layout
        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        if self.show_skeleton:
            # Create skeleton loading layout
            self.setup_skeleton_ui(layout)
        else:
            # Create glassmorphism container
            self.container = QtWidgets.QWidget()
            self.container.setObjectName("loading_container")

            # Dynamic container size based on features
            width = 400 if self.show_particles else 350
            height = 320 if self.show_progress else 280
            self.container.setFixedSize(width, height)

            # Modern glassmorphism styling
            self.container.setStyleSheet(f"""
                #loading_container {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.15),
                        stop:0.5 rgba(255, 255, 255, 0.08),
                        stop:1 rgba(255, 255, 255, 0.12));
                    border-radius: 20px;
                    border: 1px solid rgba(255, 255, 255, 0.25);
                    backdrop-filter: blur(10px);
                }}
            """)

            # Container layout
            container_layout = QtWidgets.QVBoxLayout(self.container)
            container_layout.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            container_layout.setSpacing(20)

            # Create unified animation widget (combines enhanced spinner and particles)
            self.animation_widget = QtWidgets.QWidget()
            self.animation_widget.setFixedSize(180, 180)
            container_layout.addWidget(self.animation_widget, 0, QtCore.Qt.AlignmentFlag.AlignCenter)

            # Initialize particles if enabled
            if self.show_particles and self.max_particles > 0:
                self.init_particles()

            # Enhanced message label with glow effect
            self.message_label = QtWidgets.QLabel(self.message)
            self.message_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            self.message_label.setStyleSheet(f"""
                color: {self.theme_colors['text']};
                font-size: 18px;
                font-weight: 600;
                font-family: 'Segoe UI', Arial, sans-serif;
                text-shadow: 0 0 10px rgba(0, 122, 204, 0.5);
                margin: 10px;
            """)
            container_layout.addWidget(self.message_label)

            # Add progress bar if enabled
            if self.show_progress:
                self.setup_progress_ui(container_layout)

            # Add subtle progress indicator dots
            self.progress_dots = QtWidgets.QLabel("●●●")
            self.progress_dots.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            self.progress_dots.setStyleSheet(f"""
                color: {self.theme_colors['primary_accent']};
                font-size: 24px;
                margin: 5px;
            """)
            container_layout.addWidget(self.progress_dots)

            # Add container to main layout
            layout.addWidget(self.container, 0, QtCore.Qt.AlignmentFlag.AlignCenter)

    def setup_standalone_mode(self):
        """Set up the loading screen for standalone mode."""
        # Set window flags for standalone mode
        self.setWindowFlags(
            QtCore.Qt.WindowType.FramelessWindowHint |  # No window frame
            QtCore.Qt.WindowType.WindowStaysOnTopHint |  # Stay on top
            QtCore.Qt.WindowType.Tool  # Don't show in taskbar
        )

        # Set window opacity
        self.setWindowOpacity(1.0)

        # Set position and size
        if self.position and self.size:
            self.setGeometry(self.position[0], self.position[1], self.size[0], self.size[1])
        else:
            # Default size for standalone mode
            self.resize(500, 400)
            # Center on screen
            self.center_on_screen()

        # Set up status file monitoring for standalone mode
        self.status_file = "loading_status.json"

        # Add status file checking to animation timer
        self.status_check_timer = QtCore.QTimer(self)
        self.status_check_timer.timeout.connect(self.check_status_file)
        self.status_check_timer.start(100)  # Check every 100ms

    def center_on_screen(self):
        """Center the window on the screen."""
        screen_geometry = QtWidgets.QApplication.primaryScreen().geometry()
        window_geometry = self.geometry()

        x = (screen_geometry.width() - window_geometry.width()) // 2
        y = (screen_geometry.height() - window_geometry.height()) // 2

        self.move(x, y)

    def check_status_file(self):
        """Check the status file to see if loading is complete."""
        if not self.standalone_mode:
            return

        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r') as f:
                    status = json.load(f)

                # Update message if provided
                if 'message' in status:
                    self.set_message(status['message'])

                # Update progress if provided
                if 'progress' in status and self.show_progress:
                    self.set_progress(status['progress'])

                # Update sub-message if provided
                if 'sub_message' in status and self.show_progress:
                    self.set_sub_message(status['sub_message'])

                # Check if loading is complete
                if status.get('status') == 'complete':
                    # Close the application
                    self.close()
                    if QtWidgets.QApplication.instance():
                        QtWidgets.QApplication.quit()
        except Exception as e:
            print(f"Error checking status file: {str(e)}")

    def setup_progress_ui(self, layout):
        """Set up progress bar and sub-message UI elements."""
        # Progress bar
        self.progress_bar = QtWidgets.QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                background-color: {self.theme_colors['progress_bg']};
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                text-align: center;
                color: {self.theme_colors['text']};
                height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {self.theme_colors['progress_fill']};
                border-radius: 3px;
            }}
        """)
        layout.addWidget(self.progress_bar)

        # Sub-message label (for detailed progress info)
        self.sub_message_label = QtWidgets.QLabel(self.sub_message)
        self.sub_message_label.setStyleSheet(f"color: {self.theme_colors['text']}; font-size: 12px;")
        self.sub_message_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.sub_message_label)

    def setup_skeleton_ui(self, layout):
        """Set up skeleton loading UI elements."""
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(20)

        # Create skeleton elements
        for i in range(5):
            skeleton_bar = QtWidgets.QWidget()
            height = 60 if i == 0 else 40  # First bar is taller (title)
            skeleton_bar.setFixedHeight(height)
            skeleton_bar.setStyleSheet(f"""
                background-color: {self.theme_colors['skeleton_bg']};
                border-radius: {height // 4}px;
            """)
            layout.addWidget(skeleton_bar)
            self.skeleton_bars.append(skeleton_bar)

        layout.addStretch()

    def init_particles(self):
        """Initialize the particle system."""
        for _ in range(self.max_particles):
            if random.random() < 0.3:  # 30% chance to create particle
                self.particles.append(self.create_particle())

    def create_particle(self):
        """Create a new particle."""
        try:
            if not hasattr(self, 'animation_widget') or not self.animation_widget:
                return None

            center_x = self.animation_widget.width() / 2
            center_y = self.animation_widget.height() / 2

            # Random position around center
            angle = random.uniform(0, 2 * math.pi)
            radius = random.uniform(10, 40)
            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)

            # Random velocity
            vx = random.uniform(-30, 30)
            vy = random.uniform(-30, 30)

            # Random size and color
            size = random.uniform(2, 6)
            colors = [
                QtGui.QColor(self.theme_colors.get('primary_accent', '#007acc')),
                QtGui.QColor(self.theme_colors.get('secondary_accent', '#4CAF50')),
                QtGui.QColor(self.theme_colors.get('accent_color', '#ff6b6b'))
            ]
            color = random.choice(colors)

            # Random lifespan
            life_span = random.uniform(2000, 4000)

            return Particle(x, y, vx, vy, size, color, life_span)
        except Exception as e:
            print(f"Error creating particle: {e}")
            return None

    def eventFilter(self, obj, event):
        """Handle parent resize events."""
        try:
            if (obj == self.parent and
                event and
                hasattr(event, 'type') and
                event.type() == QtCore.QEvent.Type.Resize):
                self.adjust_size(event)
        except Exception as e:
            print(f"Error in eventFilter: {e}")
        return super().eventFilter(obj, event)

    def adjust_size(self, event):
        """Adjust size when parent is resized."""
        try:
            if self.parent and hasattr(self.parent, 'rect'):
                self.setGeometry(self.parent.rect())
            if event and hasattr(event, 'accept'):
                event.accept()
        except Exception as e:
            print(f"Error in adjust_size: {e}")

    def set_message(self, message):
        """Update the loading message with smooth animation."""
        self.set_message_with_animation(message)

    def set_progress(self, value):
        """Set the progress value (0-100)."""
        if self.show_progress and hasattr(self, 'progress_bar'):
            self.progress = max(0, min(100, value))
            self.progress_bar.setValue(self.progress)

    def set_sub_message(self, message):
        """Set the sub-message text for detailed progress info."""
        if self.show_progress and hasattr(self, 'sub_message_label'):
            self.sub_message = message
            self.sub_message_label.setText(message)

    def update_progress(self, value, message=None):
        """Update both progress value and sub-message."""
        self.set_progress(value)
        if message:
            self.set_sub_message(message)

    def safe_update_animation(self):
        """Safe wrapper for update_animation to prevent crashes."""
        try:
            self.update_animation()
        except Exception as e:
            print(f"Error in animation update: {e}")
            # Stop the timer if there are repeated errors
            if hasattr(self, '_animation_error_count'):
                self._animation_error_count += 1
                if self._animation_error_count > 10:
                    self.animation_timer.stop()
                    print("Animation timer stopped due to repeated errors")
            else:
                self._animation_error_count = 1

    def update_animation(self):
        """Update the unified animations with smooth timing."""
        current_time = time.time()
        dt = current_time - self.last_frame_time
        self.last_frame_time = current_time
        self.animation_time += dt * 1000  # Convert to milliseconds

        # Update enhanced spinner rotation
        self.rotation_angle = (self.rotation_angle + 3) % 360
        self.orbit_angle = (self.orbit_angle + 2) % 360

        # Update general rotation
        self.angle = (self.angle + 8) % 360

        # Update pulse phase for glow effects
        self.pulse_phase = (self.pulse_phase + dt * 2) % (2 * math.pi)
        self.glow_intensity = 0.3 + 0.4 * (math.sin(self.pulse_phase) + 1) / 2

        # Update glassmorphism opacity
        if self.isVisible():
            self.glass_opacity = min(1.0, self.glass_opacity + dt * 3)
            self.blur_radius = min(15, self.blur_radius + dt * 30)

        # Update particles if enabled
        if self.show_particles:
            self.update_particles(dt)

        # Update skeleton shimmer if enabled
        if self.show_skeleton:
            self.shimmer_position = (self.shimmer_position + 5) % (self.width() + 200)

        # Animate progress dots
        if hasattr(self, 'progress_dots'):
            dot_phase = int(self.animation_time / 300) % 4
            if dot_phase == 0:
                self.progress_dots.setText("●○○")
            elif dot_phase == 1:
                self.progress_dots.setText("○●○")
            elif dot_phase == 2:
                self.progress_dots.setText("○○●")
            else:
                self.progress_dots.setText("●●●")

        # Update animation widget
        if hasattr(self, 'animation_widget'):
            self.animation_widget.update()

        # Update the entire widget
        self.update()

    def update_particles(self, dt):
        """Update particle system."""
        if not self.show_particles or self.max_particles <= 0:
            return

        dt_ms = dt * 1000  # Convert to milliseconds

        # Update existing particles
        self.particles = [p for p in self.particles if p.is_alive()]
        for particle in self.particles:
            particle.update(dt_ms)

        # Create new particles (with safety limit to prevent infinite loops)
        attempts = 0
        max_attempts = 10  # Prevent infinite loop
        while len(self.particles) < self.max_particles and attempts < max_attempts:
            if random.random() < 0.3:  # 30% chance per frame
                try:
                    new_particle = self.create_particle()
                    if new_particle:
                        self.particles.append(new_particle)
                except Exception as e:
                    print(f"Error creating particle: {e}")
                    break
            attempts += 1

    def paintEvent(self, _event):
        """Unified paint event combining all visual effects."""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)
        painter.setRenderHint(QtGui.QPainter.RenderHint.SmoothPixmapTransform)

        if self.show_skeleton:
            # Draw skeleton loading with shimmer
            self.draw_skeleton_background(painter)
            self.draw_skeleton_shimmer(painter)
        else:
            # Draw sophisticated background with gradient
            bg_gradient = QtGui.QLinearGradient(0, 0, self.width(), self.height())
            bg_gradient.setColorAt(0, QtGui.QColor(0, 0, 0, 160))
            bg_gradient.setColorAt(0.5, QtGui.QColor(20, 20, 30, 180))
            bg_gradient.setColorAt(1, QtGui.QColor(0, 0, 0, 160))
            painter.fillRect(self.rect(), bg_gradient)

            # Draw unified animation on the animation widget
            if hasattr(self, 'animation_widget') and self.animation_widget.isVisible():
                self.draw_unified_animation(painter)

            # Draw particles if enabled
            if self.show_particles:
                self.draw_particles(painter)

            # Add subtle ambient glow around container
            if hasattr(self, 'container'):
                self.draw_ambient_glow(painter)

    def draw_unified_animation(self, painter):
        """Draw the unified animation combining enhanced spinner and effects."""
        if not hasattr(self, 'animation_widget'):
            return

        # Get animation widget geometry
        widget_rect = self.animation_widget.geometry()
        center = QtCore.QPointF(
            widget_rect.x() + widget_rect.width() / 2,
            widget_rect.y() + widget_rect.height() / 2
        )

        painter.save()
        painter.translate(center)

        # Draw outer rotating ring (enhanced spinner style)
        painter.save()
        painter.rotate(self.rotation_angle)

        outer_radius = 70
        gradient = QtGui.QConicalGradient(0, 0, 0)
        primary_color = QtGui.QColor(self.theme_colors.get('primary_accent', '#007acc'))
        gradient.setColorAt(0, QtGui.QColor(primary_color.red(), primary_color.green(), primary_color.blue(), 0))
        gradient.setColorAt(0.3, primary_color)
        gradient.setColorAt(0.7, primary_color.lighter(150))
        gradient.setColorAt(1, QtGui.QColor(primary_color.red(), primary_color.green(), primary_color.blue(), 0))

        pen = QtGui.QPen(QtGui.QBrush(gradient), 4)
        pen.setCapStyle(QtCore.Qt.PenCapStyle.RoundCap)
        painter.setPen(pen)
        painter.drawArc(-outer_radius, -outer_radius, outer_radius * 2, outer_radius * 2, 0, 270 * 16)
        painter.restore()

        # Draw inner counter-rotating ring
        painter.save()
        painter.rotate(-self.rotation_angle * 1.5)

        inner_radius = 50
        gradient2 = QtGui.QConicalGradient(0, 0, 0)
        secondary_color = QtGui.QColor(self.theme_colors.get('secondary_accent', '#4CAF50'))
        gradient2.setColorAt(0, QtGui.QColor(secondary_color.red(), secondary_color.green(), secondary_color.blue(), 0))
        gradient2.setColorAt(0.4, secondary_color)
        gradient2.setColorAt(0.8, secondary_color.lighter(120))
        gradient2.setColorAt(1, QtGui.QColor(secondary_color.red(), secondary_color.green(), secondary_color.blue(), 0))

        pen2 = QtGui.QPen(QtGui.QBrush(gradient2), 3)
        pen2.setCapStyle(QtCore.Qt.PenCapStyle.RoundCap)
        painter.setPen(pen2)
        painter.drawArc(-inner_radius, -inner_radius, inner_radius * 2, inner_radius * 2, 0, 200 * 16)
        painter.restore()

        # Draw orbiting dots
        for i in range(3):
            angle = (self.orbit_angle + i * 120) * math.pi / 180
            orbit_radius = 60
            dot_x = orbit_radius * math.cos(angle)
            dot_y = orbit_radius * math.sin(angle)

            # Pulsing size
            pulse_size = 3 + 2 * math.sin(self.pulse_phase + i * math.pi / 3)

            accent_color = QtGui.QColor(self.theme_colors.get('accent_color', '#ff6b6b'))
            painter.setBrush(accent_color)
            painter.setPen(QtCore.Qt.PenStyle.NoPen)
            painter.drawEllipse(QtCore.QPointF(dot_x, dot_y), pulse_size, pulse_size)

        # Draw central glow
        glow_radius = 8 + 4 * math.sin(self.pulse_phase)
        glow_gradient = QtGui.QRadialGradient(QtCore.QPointF(0, 0), glow_radius)
        glow_color = QtGui.QColor(self.theme_colors.get('primary_accent', '#007acc'))
        glow_color.setAlphaF(0.6)
        glow_gradient.setColorAt(0, glow_color)
        glow_color.setAlphaF(0)
        glow_gradient.setColorAt(1, glow_color)

        painter.setBrush(glow_gradient)
        painter.setPen(QtCore.Qt.PenStyle.NoPen)
        painter.drawEllipse(QtCore.QPointF(0, 0), glow_radius, glow_radius)

        painter.restore()

    def draw_particles(self, painter):
        """Draw the particle system."""
        if not hasattr(self, 'animation_widget'):
            return

        widget_rect = self.animation_widget.geometry()

        painter.save()
        painter.translate(widget_rect.topLeft())

        # Draw particles
        for particle in self.particles:
            color = QtGui.QColor(particle.color)
            color.setAlphaF(particle.opacity)

            painter.setBrush(color)
            painter.setPen(QtCore.Qt.PenStyle.NoPen)

            painter.drawEllipse(
                QtCore.QPointF(particle.x, particle.y),
                particle.size, particle.size
            )

        painter.restore()

    def draw_skeleton_background(self, painter):
        """Draw skeleton loading background."""
        painter.fillRect(self.rect(), QtGui.QColor(self.theme_colors['background']))

    def draw_skeleton_shimmer(self, painter):
        """Draw skeleton shimmer effect."""
        shimmer_gradient = QtGui.QLinearGradient(
            self.shimmer_position - 100, 0,
            self.shimmer_position + 100, 0
        )

        transparent = QtGui.QColor(255, 255, 255, 0)
        highlight = QtGui.QColor(255, 255, 255, 30)

        shimmer_gradient.setColorAt(0, transparent)
        shimmer_gradient.setColorAt(0.5, highlight)
        shimmer_gradient.setColorAt(1, transparent)

        painter.setBrush(shimmer_gradient)
        painter.setPen(QtCore.Qt.PenStyle.NoPen)
        painter.drawRect(self.rect())

    def draw_modern_spinner(self, painter):
        """Draw a modern, sophisticated spinner animation."""
        if not hasattr(self, 'animation_widget'):
            return

        # Get spinner widget geometry
        spinner_rect = self.animation_widget.geometry()
        center = QtCore.QPointF(
            spinner_rect.x() + spinner_rect.width() / 2,
            spinner_rect.y() + spinner_rect.height() / 2
        )

        painter.save()
        painter.translate(center)

        # Draw multiple rotating rings with different speeds
        for ring in range(3):
            painter.save()

            ring_angle = (self.angle * (1 + ring * 0.3)) % 360
            painter.rotate(ring_angle)

            radius = 35 + ring * 15
            thickness = 4 - ring

            # Create gradient for each ring
            gradient = QtGui.QConicalGradient(0, 0, -ring_angle)

            primary = QtGui.QColor(self.theme_colors['primary_accent'])
            primary.setAlphaF(0.8 - ring * 0.2)

            transparent = QtGui.QColor(primary)
            transparent.setAlphaF(0)

            gradient.setColorAt(0, transparent)
            gradient.setColorAt(0.3, primary)
            gradient.setColorAt(0.7, primary.lighter(120))
            gradient.setColorAt(1, transparent)

            pen = QtGui.QPen(QtGui.QBrush(gradient), thickness)
            pen.setCapStyle(QtCore.Qt.PenCapStyle.RoundCap)
            painter.setPen(pen)

            # Draw arc with glow effect
            arc_length = 280 - ring * 40
            painter.drawArc(-radius, -radius, radius * 2, radius * 2, 0, arc_length * 16)

            painter.restore()

        painter.restore()

    def draw_ambient_glow(self, painter):
        """Draw ambient glow effect around the container."""
        if not hasattr(self, 'container'):
            return

        container_rect = self.container.geometry()
        center = QtCore.QPointF(
            container_rect.x() + container_rect.width() / 2,
            container_rect.y() + container_rect.height() / 2
        )

        # Create radial gradient for glow
        glow_radius = max(container_rect.width(), container_rect.height()) * 0.8
        gradient = QtGui.QRadialGradient(center, glow_radius)

        glow_color = QtGui.QColor(self.theme_colors['primary_accent'])
        glow_color.setAlphaF(self.glow_intensity * 0.15)
        gradient.setColorAt(0, glow_color)

        glow_color.setAlphaF(0)
        gradient.setColorAt(1, glow_color)

        painter.setBrush(gradient)
        painter.setPen(QtCore.Qt.PenStyle.NoPen)
        painter.drawEllipse(center, glow_radius, glow_radius)

    def show(self):
        """Show the loading screen with smooth fade-in animation."""
        super().show()
        self.fade_in_animation.start()

    def hide_with_animation(self):
        """Hide the loading screen with smooth fade-out animation."""
        fade_out = QtCore.QPropertyAnimation(self, b"windowOpacity")
        fade_out.setDuration(200)
        fade_out.setStartValue(1.0)
        fade_out.setEndValue(0.0)
        fade_out.setEasingCurve(QtCore.QEasingCurve.Type.InCubic)
        fade_out.finished.connect(self.hide)
        fade_out.start()

        # Store reference to prevent garbage collection
        self._fade_out_animation = fade_out

    def showEvent(self, event):
        """Start animation when shown with enhanced initialization."""
        self.animation_timer.start()
        self.last_frame_time = time.time()
        self.animation_time = 0
        self.glass_opacity = 0.0
        self.blur_radius = 0
        super().showEvent(event)

    def hideEvent(self, event):
        """Stop animation when hidden."""
        self.animation_timer.stop()
        super().hideEvent(event)

    def set_loading_style(self, style):
        """Change the loading animation style dynamically."""
        if style != self.loading_style:
            self.loading_style = style
            # Reinitialize UI with new style
            self.init_ui()

    def closeEvent(self, event):
        """Handle close event for standalone mode."""
        if self.standalone_mode:
            # Clean up status file
            if hasattr(self, 'status_file') and os.path.exists(self.status_file):
                try:
                    os.remove(self.status_file)
                except:
                    pass
        event.accept()

    def hideEvent(self, event):
        """Handle hide event - stop animations to save resources."""
        if hasattr(self, 'animation_timer'):
            self.animation_timer.stop()
        if hasattr(self, 'status_check_timer'):
            self.status_check_timer.stop()
        super().hideEvent(event)

    def showEvent(self, event):
        """Handle show event - restart animations."""
        if hasattr(self, 'animation_timer'):
            self.animation_timer.start(16)
        if hasattr(self, 'status_check_timer') and self.standalone_mode:
            self.status_check_timer.start(100)
        super().showEvent(event)

    def set_message_with_animation(self, message):
        """Update message with smooth transition animation."""
        self.message = message

        # Create fade animation for message
        fade_out = QtCore.QPropertyAnimation(self.message_label, b"windowOpacity")
        fade_out.setDuration(150)
        fade_out.setStartValue(1.0)
        fade_out.setEndValue(0.0)

        def update_text():
            self.message_label.setText(message)
            fade_in = QtCore.QPropertyAnimation(self.message_label, b"windowOpacity")
            fade_in.setDuration(150)
            fade_in.setStartValue(0.0)
            fade_in.setEndValue(1.0)
            fade_in.start()
            # Store reference
            self._fade_in_animation = fade_in

        fade_out.finished.connect(update_text)
        fade_out.start()
        # Store reference
        self._fade_out_animation = fade_out




def create_loading_screen(parent=None, message="Loading...", style="modern", theme_colors=None):
    """
    Factory function to create different types of loading screens.

    Args:
        parent: Parent widget
        message: Loading message
        style: Loading style ("modern", "skeleton", "progress", "particles", "enhanced", "standalone")
        theme_colors: Theme color dictionary

    Returns:
        LoadingScreen instance configured with the specified style
    """
    if style == "skeleton":
        return LoadingScreen(parent, message, theme_colors, show_skeleton=True, show_particles=False)
    elif style == "progress":
        return LoadingScreen(parent, message, theme_colors, show_progress=True, show_particles=False)
    elif style == "particles":
        return LoadingScreen(parent, message, theme_colors, show_particles=True, show_progress=False)
    elif style == "enhanced":
        return LoadingScreen(parent, message, theme_colors, show_particles=True, show_progress=True)
    elif style == "standalone":
        return LoadingScreen(None, message, theme_colors, show_particles=True, standalone_mode=True)
    else:
        # Default to modern style with particles
        return LoadingScreen(parent, message, theme_colors, show_particles=True, show_progress=False)


# Legacy aliases for backward compatibility
ProgressLoadingScreen = LoadingScreen
SkeletonLoadingScreen = LoadingScreen


class LoadingScreenManager:
    """
    Utility class to manage the standalone loading screen process.
    """

    def __init__(self, use_background_mode=True):
        """Initialize the loading screen manager.

        Args:
            use_background_mode: If True, disable external loading screens entirely
        """
        self.process = None
        self.status_file = "loading_status.json"
        self.use_background_mode = use_background_mode

        # Register cleanup function
        atexit.register(self.cleanup)




    def start(self, message="Loading chart data...", position=None, size=None):
        """
        Start the loading screen process.

        Args:
            message: Message to display
            position: Position of the window (x, y)
            size: Size of the window (width, height)
        """
        # If background mode is enabled, skip external loading screen
        if self.use_background_mode:
            return True

        # Create status file
        self._update_status("loading", message)

        # Build command
        cmd = [sys.executable, "loading_screen.py"]

        # Add message
        cmd.extend(["--message", message])

        # Add position if provided
        if position:
            cmd.extend(["--position", str(position[0]), str(position[1])])

        # Add size if provided
        if size:
            # Use provided size without minimum restrictions
            width, height = size
            cmd.extend(["--size", str(width), str(height)])

        # Start process
        try:
            # Use comprehensive flags to hide the process completely
            creation_flags = (
                subprocess.DETACHED_PROCESS |           # Run independently
                subprocess.CREATE_NEW_PROCESS_GROUP |   # New process group
                subprocess.CREATE_NO_WINDOW |           # No console window
                0x08000000                              # CREATE_NO_WINDOW (additional flag)
            )

            self.process = subprocess.Popen(
                cmd,
                creationflags=creation_flags,
                stdout=subprocess.DEVNULL,              # Redirect stdout to null
                stderr=subprocess.DEVNULL,              # Redirect stderr to null
                stdin=subprocess.DEVNULL                # Redirect stdin to null
            )

            # Give the process time to start
            time.sleep(0.1)

            return True
        except Exception as e:
            print(f"Error starting loading screen: {str(e)}")
            return False

    def update_message(self, message):
        """
        Update the loading message.

        Args:
            message: New message to display
        """
        # If background mode is enabled, skip updating external loading screen
        if self.use_background_mode:
            return

        self._update_status("loading", message)

    def stop(self):
        """Stop the loading screen process."""
        # If background mode is enabled, skip stopping external loading screen
        if self.use_background_mode:
            return

        # Update status file to indicate loading is complete
        self._update_status("complete")

        # Give the process time to read the status file and close itself
        time.sleep(0.2)

        # If process is still running, terminate it
        if self.process and self.process.poll() is None:
            try:
                # Since we're on Windows, use Windows-specific termination
                self.process.terminate()
            except:
                pass

        self.process = None

    def _update_status(self, status, message=None):
        """
        Update the status file.

        Args:
            status: Status to set ('loading' or 'complete')
            message: Message to display (optional)
        """
        try:
            data = {"status": status}
            if message:
                data["message"] = message

            with open(self.status_file, 'w') as f:
                json.dump(data, f)
        except Exception as e:
            print(f"Error updating status file: {str(e)}")

    def cleanup(self):
        """Clean up resources."""
        # Stop the loading screen if it's running
        self.stop()

        # Remove the status file
        if os.path.exists(self.status_file):
            try:
                os.remove(self.status_file)
            except:
                pass


def main():
    """Main function for standalone loading screen execution."""
    # Hide console window on Windows
    if os.name == 'nt':
        try:
            import ctypes
            ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
        except:
            pass

    app = QtWidgets.QApplication(sys.argv)

    # Get command line arguments
    message = "Loading chart data..."
    position = None
    size = None

    # Parse command line arguments
    for i, arg in enumerate(sys.argv):
        if arg == "--message" and i + 1 < len(sys.argv):
            message = sys.argv[i + 1]
        elif arg == "--position" and i + 2 < len(sys.argv):
            try:
                x = int(sys.argv[i + 1])
                y = int(sys.argv[i + 2])
                position = (x, y)
            except:
                pass
        elif arg == "--size" and i + 2 < len(sys.argv):
            try:
                width = int(sys.argv[i + 1])
                height = int(sys.argv[i + 2])
                size = (width, height)
            except:
                pass

    # Create and show the loading screen in standalone mode
    loading_screen = LoadingScreen(
        parent=None,
        message=message,
        standalone_mode=True,
        position=position,
        size=size,
        show_particles=True
    )
    loading_screen.show()

    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
