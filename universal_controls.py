"""
Universal Controls Widget

This module provides a universal control panel that appears at the top of the main window
and controls parameters that are shared across all tabs.
"""

from PyQt6 import QtWidgets, QtCore
from parameter_registry import default_registry
from data_dispatcher import DataDispatcher
from ticker_converter import ticker_converter
# Loading screen functionality removed

class UniversalControlPanel(QtWidgets.QWidget):
    """
    A widget that provides universal controls for timeframe, vector length, and days to load.
    These controls affect all tabs in the application.
    """

    # Signal to notify when data is fetched
    data_fetched = QtCore.pyqtSignal(str, object)

    def __init__(self, parent=None):
        """Initialize the universal control panel."""
        super().__init__(parent)

        # Define presets for quick configuration
        self.presets = [
            {"name": "Short-term (1m)", "vector_length": 50, "timeframe": "1m", "days_to_load": 5},
            {"name": "Medium-term (5m)", "vector_length": 20, "timeframe": "5m", "days_to_load": 20},
            {"name": "Medium-term (15m)", "vector_length": 10, "timeframe": "15m", "days_to_load": 20},
            {"name": "Long-term (60m)", "vector_length": 7, "timeframe": "60m", "days_to_load": 50},
            {"name": "Very Long-term (Daily)", "vector_length": 1, "timeframe": "1d", "days_to_load": 200},
            {"name": "Extended History (Daily)", "vector_length": 1, "timeframe": "1d", "days_to_load": 1500},
        ]

        # Initialize ticker templates (will be populated dynamically)
        self.ticker_templates = []
        self._generate_ticker_templates()

        # Get the data dispatcher instance
        self.data_dispatcher = DataDispatcher.get_instance()

        # Connect to data dispatcher signals
        self.data_dispatcher.data_fetched.connect(self.on_data_fetched)
        self.data_dispatcher.progress.connect(self.on_progress_update)
        self.data_dispatcher.error.connect(self.on_error)
        self.data_dispatcher.data_source_changed.connect(self.on_data_source_changed)

        # Create loading screen (initially hidden)
        self.loading_screen = None

        # Set up the UI (using lazy loading)
        self.init_ui()

        # Connect to parameter registry signals
        default_registry.ui_parameter_changed.connect(self.on_parameter_changed)

        # Store the parent for access to tabs
        self.parent = parent

        # Initialize data source status and sync UI with current data source
        QtCore.QTimer.singleShot(200, self.update_data_source_status)
        QtCore.QTimer.singleShot(300, self.sync_data_source_ui)

    def init_ui(self):
        """Initialize the essential user interface components."""
        # Create a horizontal layout for the control panel
        layout = QtWidgets.QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)

        # Add a stretch to center the controls
        layout.addStretch()

        # Universal Controls label removed for cleaner interface

        # Symbol input field
        symbol_label = QtWidgets.QLabel("Symbol:")
        layout.addWidget(symbol_label)

        self.symbol_input = QtWidgets.QLineEdit()
        self.symbol_input.setPlaceholderText("Enter symbol...")
        self.symbol_input.setFixedWidth(100)
        layout.addWidget(self.symbol_input)

        # Ticker templates dropdown
        ticker_template_label = QtWidgets.QLabel("Templates:")
        layout.addWidget(ticker_template_label)

        self.ticker_template_combo = QtWidgets.QComboBox()
        self.ticker_template_combo.setFixedWidth(150)
        self.ticker_template_combo.currentIndexChanged.connect(self.apply_ticker_template)
        layout.addWidget(self.ticker_template_combo)

        # Populate the ticker templates dropdown
        self._update_ticker_templates_dropdown()

        # Data source selector
        data_source_label = QtWidgets.QLabel("OHLCV Data:")
        layout.addWidget(data_source_label)

        self.data_source_combo = QtWidgets.QComboBox()
        self.data_source_combo.addItems(["TradingView", "Yahoo Finance"])
        self.data_source_combo.setCurrentText("TradingView")  # Default to TradingView
        self.data_source_combo.setFixedWidth(120)
        self.data_source_combo.currentTextChanged.connect(self.on_data_source_selection_changed)
        layout.addWidget(self.data_source_combo)

        # Timeframe selector
        timeframe_label = QtWidgets.QLabel("Timeframe:")
        layout.addWidget(timeframe_label)

        self.timeframe_combo = QtWidgets.QComboBox()
        self.timeframe_combo.addItems(["1m", "2m", "5m", "15m", "30m", "60m", "1h", "1d", "1wk", "1mo"])
        self.timeframe_combo.setCurrentText(default_registry.get_value('timeframe'))
        self.timeframe_combo.setFixedWidth(80)
        self.timeframe_combo.currentTextChanged.connect(self.on_timeframe_changed)
        layout.addWidget(self.timeframe_combo)

        # Schwab Login button moved to right panel in market_odds.py

        # Vector length selector
        vector_length_label = QtWidgets.QLabel("The Line Length:")
        layout.addWidget(vector_length_label)

        self.vector_length_spin = QtWidgets.QSpinBox()
        self.vector_length_spin.setRange(1, 1000)
        self.vector_length_spin.setValue(default_registry.get_value('vector_length'))
        self.vector_length_spin.setFixedWidth(80)
        self.vector_length_spin.valueChanged.connect(self.on_vector_length_changed)
        layout.addWidget(self.vector_length_spin)

        # Days to load selector
        days_label = QtWidgets.QLabel("Days to Load:")
        layout.addWidget(days_label)

        self.days_spin = QtWidgets.QSpinBox()
        self.days_spin.setRange(1, 20000)  # Increased to allow 15k+ days
        self.days_spin.setValue(default_registry.get_value('days_to_load'))
        self.days_spin.setFixedWidth(80)
        self.days_spin.setSuffix(" days")
        self.days_spin.valueChanged.connect(self.on_days_to_load_changed)
        layout.addWidget(self.days_spin)

        # Presets dropdown
        preset_label = QtWidgets.QLabel("Presets:")
        layout.addWidget(preset_label)

        self.preset_combo = QtWidgets.QComboBox()
        self.preset_combo.addItem("Select Preset...")
        for preset in self.presets:
            self.preset_combo.addItem(preset["name"])
        self.preset_combo.setFixedWidth(180)
        self.preset_combo.currentIndexChanged.connect(self.apply_preset)
        layout.addWidget(self.preset_combo)

        # Fetch Data button
        self.fetch_button = QtWidgets.QPushButton("Fetch Data")
        self.fetch_button.setFixedWidth(100)
        self.fetch_button.clicked.connect(self.fetch_data)
        layout.addWidget(self.fetch_button)

        # Status label (hidden since status is now shown in bottom status bar)
        self.status_label = QtWidgets.QLabel("")
        self.status_label.setVisible(False)
        layout.addWidget(self.status_label)

        # Add a stretch to center the controls
        layout.addStretch()

        # Set a fixed height for the control panel
        self.setFixedHeight(60)

        # Apply styling
        self.setStyleSheet("""
            QLabel {
                color: #e0e0e0;
            }
            QComboBox, QSpinBox, QLineEdit {
                background-color: #2d2d2d;
                color: #e0e0e0;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 4px;
            }
            QComboBox:hover, QSpinBox:hover, QLineEdit:hover {
                border: 1px solid #000000;
            }
            QComboBox:focus, QSpinBox:focus, QLineEdit:focus {
                border: 1px solid #000000;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 15px;
                border-left-width: 1px;
                border-left-color: #3e3e3e;
                border-left-style: solid;
            }
            QPushButton {
                background-color: #2d2d2d;
                color: #e0e0e0;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #3d3d3d;
                border: 1px solid #000000;
            }
            QPushButton:pressed {
                background-color: #1d1d1d;
            }
        """)



    def on_timeframe_changed(self, timeframe):
        """Handle timeframe changes and update the parameter registry."""
        default_registry.set_value('timeframe', timeframe)

        # Auto-fetch data if we have a symbol loaded
        current_symbol = default_registry.get_value('symbol')
        if current_symbol and current_symbol.strip():
            self.status_label.setText(f"Timeframe changed to {timeframe}, refetching data...")
            self.status_label.setStyleSheet("color: #ffaa00;")

            # Clean up any existing threads to prevent crashes and memory leaks
            self.data_dispatcher.cleanup_all_threads()

            # Clear cache to force fresh data fetch
            self.data_dispatcher.clear_cache()

            # Update the symbol input to match the current symbol and fetch data
            self.symbol_input.setText(current_symbol)
            self.fetch_data()

    def on_vector_length_changed(self, value):
        """Handle vector length changes and update the parameter registry."""
        default_registry.set_value('vector_length', value)

    def on_days_to_load_changed(self, value):
        """Handle days to load changes and update the parameter registry."""
        default_registry.set_value('days_to_load', value)

    def on_data_source_selection_changed(self, source_text):
        """Handle data source selection changes from the UI combo box."""
        # Map UI text to data source names
        if source_text == "TradingView":
            data_source = "tradingview"
        elif source_text == "Yahoo Finance":
            data_source = "yfinance"
        else:
            data_source = "tradingview"  # Default fallback

        # Set the data source in the dispatcher
        self.data_dispatcher.set_data_source(data_source)

        # Regenerate ticker templates for the new data source
        self._generate_ticker_templates()
        self._update_ticker_templates_dropdown()

        # Update status to show current data source
        self.update_data_source_status()

    def on_data_source_changed_ui(self, source_text):
        """Handle data source changes from UI."""
        # Use TradingView for OHLCV data - All historical data uses TradingView scraper
        self.data_dispatcher.set_data_source("tradingview")

        # Update status to show current data source
        self.update_data_source_status()

    def sync_data_source_ui(self):
        """Sync the data source combo box with the current data source."""
        if hasattr(self, 'data_source_combo'):
            current_source = self.data_dispatcher.get_data_source()
            if current_source == "tradingview":
                self.data_source_combo.setCurrentText("TradingView")
            elif current_source == "yfinance":
                self.data_source_combo.setCurrentText("Yahoo Finance")

    def on_data_source_changed(self, source):
        """Handle data source changes from the dispatcher."""
        # Sync UI when data source changes externally
        self.sync_data_source_ui()

        # Regenerate ticker templates for the new data source
        self._generate_ticker_templates()
        self._update_ticker_templates_dropdown()

        self.update_data_source_status()

    def update_data_source_status(self):
        """Update the status to show current data source availability."""
        available_sources = self.data_dispatcher.get_available_sources()
        current_source = self.data_dispatcher.get_data_source()

        # Get the selected OHLCV data source from the UI
        selected_ohlcv_source = "TradingView" if current_source == "tradingview" else "Yahoo Finance"

        # Build status text based on availability and selection
        if "tradingview" in available_sources:
            status_text = f"Data: OHLCV→{selected_ohlcv_source}, Options→Yahoo Finance"
            status_color = "#55ff55"
        else:
            # If TradingView is not available but user selected it, show fallback
            if current_source == "tradingview":
                status_text = "Data: OHLCV→Yahoo Finance (TradingView unavailable), Options→Yahoo Finance"
            else:
                status_text = f"Data: OHLCV→{selected_ohlcv_source}, Options→Yahoo Finance"
            status_color = "#ffaa00"

        # Only update if we're not currently showing other status messages
        if not self.status_label.text() or "Data" in self.status_label.text():
            self.status_label.setText(status_text)
            self.status_label.setStyleSheet(f"color: {status_color};")



    def apply_preset(self, index):
        """Apply the selected preset configuration."""
        # Skip if "Select Preset..." is selected (index 0)
        if index == 0:
            return

        # Get the selected preset (index - 1 because we added "Select Preset..." at index 0)
        preset = self.presets[index - 1]

        # Update the UI controls (block signals to prevent auto-fetch)
        self.timeframe_combo.blockSignals(True)
        self.vector_length_spin.blockSignals(True)
        self.days_spin.blockSignals(True)

        self.timeframe_combo.setCurrentText(preset["timeframe"])
        self.vector_length_spin.setValue(preset["vector_length"])
        self.days_spin.setValue(preset["days_to_load"])

        # Manually update the parameter registry since signals are blocked
        default_registry.set_value('timeframe', preset["timeframe"])
        default_registry.set_value('vector_length', preset["vector_length"])
        default_registry.set_value('days_to_load', preset["days_to_load"])

        # Re-enable signals
        self.timeframe_combo.blockSignals(False)
        self.vector_length_spin.blockSignals(False)
        self.days_spin.blockSignals(False)

        # Update the status label
        self.status_label.setText(f"Applied preset: {preset['name']}")
        self.status_label.setStyleSheet("color: #55aaff;")

        # Turn off clusters and reversal signals
        # First try to directly access the market_odds_tab through the parent
        if self.parent and hasattr(self.parent, 'market_odds_tab'):
            market_odds_tab = self.parent.market_odds_tab

            # Reset extrema_prices when switching presets to fix ray price calculation issues
            if hasattr(market_odds_tab, 'extrema_prices'):
                market_odds_tab.extrema_prices = {}
                print("Reset extrema_prices when switching presets to fix ray price calculations")

            # Check if the market_odds_tab has a settings dialog
            if hasattr(market_odds_tab, 'settings_dialog'):
                # Turn off clusters and reversal signals
                if hasattr(market_odds_tab.settings_dialog, 'show_clusters_check'):
                    market_odds_tab.settings_dialog.show_clusters_check.setChecked(False)
                if hasattr(market_odds_tab.settings_dialog, 'show_reversals_check'):
                    market_odds_tab.settings_dialog.show_reversals_check.setChecked(False)

                # Also turn off the enable_clusters checkbox if it exists
                if hasattr(market_odds_tab.settings_dialog, 'enable_clusters'):
                    market_odds_tab.settings_dialog.enable_clusters.setChecked(False)

                # Update the display options to reflect these changes
                if hasattr(market_odds_tab, 'show_clusters'):
                    market_odds_tab.show_clusters.setChecked(False)
                # Signal display settings removed - pullback and reversal signals disabled

                # Save the settings (without showing dialog for presets)
                if hasattr(market_odds_tab.settings_dialog, 'save_settings'):
                    market_odds_tab.settings_dialog.save_settings(show_dialog=False)

                # Apply the visual settings to ensure UI is updated (without fetch dialog for presets)
                if hasattr(market_odds_tab, 'apply_visual_settings'):
                    market_odds_tab.apply_visual_settings(show_fetch_dialog=False)

                # Force a redraw of the chart
                if hasattr(market_odds_tab, 'plot_data_until') and hasattr(market_odds_tab, 'current_idx'):
                    market_odds_tab.plot_data_until(market_odds_tab.current_idx)
                elif hasattr(market_odds_tab, 'update_chart'):
                    market_odds_tab.update_chart()

        # Fallback to the old method if market_odds_tab is not directly accessible
        elif self.parent and hasattr(self.parent, 'tab_widget'):
            # Look for the market_odds tab
            for i in range(self.parent.tab_widget.count()):
                tab = self.parent.tab_widget.widget(i)

                # Reset extrema_prices when switching presets to fix ray price calculation issues
                if hasattr(tab, 'extrema_prices'):
                    tab.extrema_prices = {}
                    print("Reset extrema_prices when switching presets to fix ray price calculations (fallback method)")

                if hasattr(tab, 'settings_dialog'):
                    # Found a tab with settings_dialog, check if it has the relevant checkboxes
                    if hasattr(tab.settings_dialog, 'show_clusters_check'):

                        # Turn off clusters
                        tab.settings_dialog.show_clusters_check.setChecked(False)

                        # Also turn off the enable_clusters checkbox if it exists
                        if hasattr(tab.settings_dialog, 'enable_clusters'):
                            tab.settings_dialog.enable_clusters.setChecked(False)

                        # Update the display options to reflect these changes
                        if hasattr(tab, 'show_clusters'):
                            tab.show_clusters.setChecked(False)
                        # Signal display settings removed - pullback and reversal signals disabled

                        # Save the settings (without showing dialog for presets)
                        if hasattr(tab.settings_dialog, 'save_settings'):
                            tab.settings_dialog.save_settings(show_dialog=False)

                        # Apply the visual settings to ensure UI is updated (without fetch dialog for presets)
                        if hasattr(tab, 'apply_visual_settings'):
                            tab.apply_visual_settings(show_fetch_dialog=False)

                        # Force a redraw of the chart
                        if hasattr(tab, 'plot_data_until') and hasattr(tab, 'current_idx'):
                            tab.plot_data_until(tab.current_idx)
                        elif hasattr(tab, 'update_chart'):
                            tab.update_chart()

        # Dialog removed - preset applied silently

        # Reset the combo box to "Select Preset..." after applying
        self.preset_combo.blockSignals(True)
        self.preset_combo.setCurrentIndex(0)
        self.preset_combo.blockSignals(False)

    def apply_ticker_template(self, index):
        """Apply the selected ticker template."""
        # Skip if invalid index (negative or 0 for "Select Template...")
        if index <= 0:
            return

        # Get the selected template (index - 1 because we added "Select Template..." at index 0)
        template = self.ticker_templates[index - 1]

        # Update the symbol input field
        self.symbol_input.setText(template["symbol"])

        # Update status
        self.status_label.setText(f"Applied template: {template['name']} ({template['description']}). Press 'Fetch Data' to load.")
        self.status_label.setStyleSheet("color: #55aaff;")

        # Reset the combo box to "Select Template..." after applying
        self.ticker_template_combo.blockSignals(True)
        self.ticker_template_combo.setCurrentIndex(0)
        self.ticker_template_combo.blockSignals(False)

    def _generate_ticker_templates(self):
        """Generate ticker templates based on the current data source."""
        import datetime

        # Get current data source
        current_source = self.data_dispatcher.get_data_source() if hasattr(self, 'data_dispatcher') else "tradingview"
        is_tradingview = current_source == "tradingview"

        # Generate contract information
        current_year = datetime.datetime.now().year
        next_year = current_year + 1
        current_month = datetime.datetime.now().month

        # Futures contract months: H=Mar, M=Jun, U=Sep, Z=Dec
        contract_months = ['H', 'M', 'U', 'Z']
        contract_month_names = ['Mar', 'Jun', 'Sep', 'Dec']
        contract_month_nums = [3, 6, 9, 12]  # Mar, Jun, Sep, Dec

        # Find next contract month
        next_contract_idx = 0
        contract_year = current_year

        for i, month_num in enumerate(contract_month_nums):
            if current_month <= month_num:
                next_contract_idx = i
                break
        else:
            # If we're past December, use March of next year
            next_contract_idx = 0
            contract_year = next_year

        next_contract = contract_months[next_contract_idx]
        next_contract_name = contract_month_names[next_contract_idx]

        # Generate templates based on data source
        if is_tradingview:
            # TradingView format
            self.ticker_templates = [
                {"name": f"MES {next_contract_name}{contract_year}", "symbol": f"MES{next_contract}{contract_year}", "description": "Micro E-mini S&P 500 Futures"},
                {"name": f"NQ {next_contract_name}{contract_year}", "symbol": f"NQ{next_contract}{contract_year}", "description": "E-mini NASDAQ-100 Futures"},
                {"name": "MES Continuous", "symbol": "MES1!", "description": "Micro E-mini S&P 500 Continuous Contract"},
                {"name": "NQ Continuous", "symbol": "NQ1!", "description": "E-mini NASDAQ-100 Continuous Contract"},
                {"name": "SPY", "symbol": "SPY", "description": "SPDR S&P 500 ETF"},
                {"name": "QQQ", "symbol": "QQQ", "description": "Invesco QQQ Trust ETF"},
            ]
        else:
            # Yahoo Finance format - use 2-digit year and .CME suffix for specific contracts
            contract_year_2digit = str(contract_year)[-2:]  # Get last 2 digits of year
            self.ticker_templates = [
                {"name": f"MES {next_contract_name}{contract_year}", "symbol": f"MES{next_contract}{contract_year_2digit}.CME", "description": "Micro E-mini S&P 500 Futures"},
                {"name": f"NQ {next_contract_name}{contract_year}", "symbol": f"NQ{next_contract}{contract_year_2digit}.CME", "description": "E-mini NASDAQ-100 Futures"},
                {"name": "ES Continuous", "symbol": "ES=F", "description": "E-mini S&P 500 Continuous Contract"},
                {"name": "NQ Continuous", "symbol": "NQ=F", "description": "E-mini NASDAQ-100 Continuous Contract"},
                {"name": "SPY", "symbol": "SPY", "description": "SPDR S&P 500 ETF"},
                {"name": "QQQ", "symbol": "QQQ", "description": "Invesco QQQ Trust ETF"},
            ]

    def _update_ticker_templates_dropdown(self):
        """Update the ticker templates dropdown with current templates."""
        if hasattr(self, 'ticker_template_combo'):
            # Clear existing items
            self.ticker_template_combo.clear()

            # Add default item
            self.ticker_template_combo.addItem("Select Template...")

            # Add current templates
            for template in self.ticker_templates:
                self.ticker_template_combo.addItem(template["name"])

    def on_parameter_changed(self, name, value):
        """Handle parameter changes from other parts of the application."""
        if name == 'timeframe' and self.timeframe_combo.currentText() != value:
            self.timeframe_combo.setCurrentText(value)
        elif name == 'vector_length' and hasattr(self, 'vector_length_spin') and self.vector_length_spin.value() != value:
            self.vector_length_spin.setValue(value)
        elif name == 'days_to_load' and hasattr(self, 'days_spin') and self.days_spin.value() != value:
            self.days_spin.setValue(value)

    def fetch_data(self):
        """Fetch data for the specified symbol using the data dispatcher."""
        symbol = self.symbol_input.text().strip().upper()
        if not symbol:
            self.status_label.setText("Please enter a symbol")
            self.status_label.setStyleSheet("color: #ff5555;")
            return

        # Update status
        self.status_label.setText(f"Preparing to fetch data for {symbol}...")
        self.status_label.setStyleSheet("color: #ffaa00;")

        # Loading screen functionality removed - no longer showing loading screen

        # Get parameters from controls
        try:
            timeframe = self.timeframe_combo.currentText()
            days_to_load = self.days_spin.value()

            # Store the symbol in the registry
            default_registry.set_value('symbol', symbol)

            # Clean up any existing threads before starting new fetch to prevent crashes
            self.data_dispatcher.cleanup_all_threads()

            # Use the selected data source for OHLCV data (options will always use Yahoo Finance)
            selected_text = self.data_source_combo.currentText()
            if selected_text == "TradingView":
                data_source = "tradingview"
            elif selected_text == "Yahoo Finance":
                data_source = "yfinance"
            else:
                data_source = "tradingview"  # Default fallback

            self.data_dispatcher.set_data_source(data_source)

            # Use the data dispatcher to fetch data asynchronously
            self.data_dispatcher.fetch_data(symbol, timeframe, days_to_load)

        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")
            self.status_label.setStyleSheet("color: #ff5555;")

    def on_data_fetched(self, symbol, data):
        """Handle data fetched signal from the data dispatcher."""
        # Update status
        self.status_label.setText(f"Data fetched for {symbol}")
        self.status_label.setStyleSheet("color: #55ff55;")

        # Loading screen functionality removed

        # Forward the data to our own signal
        self.data_fetched.emit(symbol, data)

        # If a preset was recently applied, make sure the settings are properly applied
        # This is needed because some tabs might recreate their UI components when new data is loaded

        # First try to directly access the market_odds_tab through the parent
        if self.parent and hasattr(self.parent, 'market_odds_tab'):
            market_odds_tab = self.parent.market_odds_tab

            # Ensure extrema_prices is reset when new data is fetched
            # This is already done in the process_data method, but we'll add it here as a safeguard
            if hasattr(market_odds_tab, 'extrema_prices'):
                market_odds_tab.extrema_prices = {}
                print("Reset extrema_prices when fetching new data to ensure correct ray price calculations")

            # Check if the market_odds_tab has a settings dialog
            if hasattr(market_odds_tab, 'settings_dialog'):
                # Check if the tab has the relevant settings
                if hasattr(market_odds_tab.settings_dialog, 'show_clusters_check'):

                    # Make sure the settings are applied to the UI
                    if hasattr(market_odds_tab, 'show_clusters'):
                        market_odds_tab.show_clusters.setChecked(market_odds_tab.settings_dialog.show_clusters_check.isChecked())
                    # Signal display settings removed - pullback and reversal signals disabled

                    # Apply the visual settings to ensure UI is updated (without fetch dialog)
                    if hasattr(market_odds_tab, 'apply_visual_settings'):
                        market_odds_tab.apply_visual_settings(show_fetch_dialog=False)

        # Fallback to the old method if market_odds_tab is not directly accessible
        elif self.parent and hasattr(self.parent, 'tab_widget'):
            for i in range(self.parent.tab_widget.count()):
                tab = self.parent.tab_widget.widget(i)

                # Ensure extrema_prices is reset when new data is fetched
                if hasattr(tab, 'extrema_prices'):
                    tab.extrema_prices = {}
                    print("Reset extrema_prices when fetching new data (fallback method)")

                if hasattr(tab, 'settings_dialog'):
                    # Check if the tab has the relevant settings
                    if hasattr(tab.settings_dialog, 'show_clusters_check'):

                        # Make sure the settings are applied to the UI
                        if hasattr(tab, 'show_clusters'):
                            tab.show_clusters.setChecked(tab.settings_dialog.show_clusters_check.isChecked())
                        # Signal display settings removed - pullback and reversal signals disabled

                        # Apply the visual settings to ensure UI is updated (without fetch dialog)
                        if hasattr(tab, 'apply_visual_settings'):
                            tab.apply_visual_settings(show_fetch_dialog=False)

    def on_progress_update(self, value, message):
        """Handle progress updates from the data dispatcher."""
        # Update status label
        self.status_label.setText(message)

        # Loading screen functionality removed

    def on_error(self, error_message):
        """Handle error signal from the data dispatcher."""
        self.status_label.setText(error_message)
        self.status_label.setStyleSheet("color: #ff5555;")

        # Loading screen functionality removed
