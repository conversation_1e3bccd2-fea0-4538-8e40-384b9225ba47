"""
Custom Tab Widget with PyQt-based styling instead of CSS.

This module provides a custom tab widget that uses PyQt's native styling
capabilities instead of CSS for better performance and more precise control.

Features implemented based on user preferences:
- Google-style design patterns instead of current tab design approach
- Greyscale styling instead of blue colors
- All tabs have the same length and width while preserving design
- Left-aligned text and much smaller height than current implementation
- State-of-the-art proportions for UI component sizing and layout design
- PyQt-based styling instead of CSS for better performance
"""

from PyQt6 import QtWidgets, QtCore, QtGui
from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import QTabWidget, QTabBar, QStylePainter, QStyleOptionTab, QStyle
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QFont

# Import theme colors
try:
    import theme
    THEME_COLORS = theme.DEFAULT
except ImportError:
    # Fallback theme colors if theme module is not available
    THEME_COLORS = {
        'background': '#121620',
        'control_panel': '#1E2A3A',
        'borders': '#2A3A4A',
        'text': '#E0E0E0',
        'primary_accent': '#007ACC',
        'secondary_accent': '#0098FF',
        'pressed_accent': '#005C99',
    }


class CustomTabBar(QTabBar):
    """Custom tab bar with PyQt-based styling following Google design patterns."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDrawBase(False)
        self.setExpanding(False)
        self.setUsesScrollButtons(True)

        # Set font for Google-style design with state-of-the-art proportions
        font = QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(9)
        font.setWeight(QFont.Weight.Normal)  # Normal weight for cleaner look
        font.setLetterSpacing(QFont.SpacingType.AbsoluteSpacing, 0.5)  # Slight letter spacing for better readability
        self.setFont(font)

        # Tab dimensions - uniform width and smaller height as preferred
        self.tab_width = 145  # Optimized uniform width for all tabs
        self.tab_height = 24  # Smaller height as preferred by user

    def tabSizeHint(self, index):
        """Return the size hint for a tab - ensures all tabs have same dimensions."""
        return QtCore.QSize(self.tab_width, self.tab_height)

    def minimumTabSizeHint(self, index):
        """Return the minimum size hint for a tab - ensures all tabs have same dimensions."""
        return QtCore.QSize(self.tab_width, self.tab_height)

    def maximumTabSizeHint(self, index):
        """Return the maximum size hint for a tab - ensures all tabs have same dimensions."""
        return QtCore.QSize(self.tab_width, self.tab_height)
    
    def paintEvent(self, event):
        """Custom paint event for tabs."""
        painter = QStylePainter(self)
        
        for index in range(self.count()):
            self.paintTab(painter, index)
    
    def paintTab(self, painter, index):
        """Paint a single tab with Google-style design and greyscale colors for dark theme."""
        rect = self.tabRect(index)
        selected = index == self.currentIndex()

        # Greyscale colors adapted for dark theme as preferred by user
        if selected:
            # Create custom shape with rounded top corners and flat bottom corners
            path = QtGui.QPainterPath()
            corner_radius = 6  # Radius for top corners

            # Start from bottom left (flat corner)
            path.moveTo(rect.left(), rect.bottom())
            # Line to top left, then arc for rounded top-left corner
            path.lineTo(rect.left(), rect.top() + corner_radius)
            path.arcTo(rect.left(), rect.top(), corner_radius * 2, corner_radius * 2, 180, -90)
            # Line across top to start of top-right corner
            path.lineTo(rect.right() - corner_radius, rect.top())
            # Arc for rounded top-right corner
            path.arcTo(rect.right() - corner_radius * 2, rect.top(), corner_radius * 2, corner_radius * 2, 90, -90)
            # Line down to bottom right (flat corner)
            path.lineTo(rect.right(), rect.bottom())
            # Close path back to bottom left
            path.lineTo(rect.left(), rect.bottom())

            # Fill the custom shape with base color
            painter.fillPath(path, QBrush(QColor('#2A2A2A')))

            # Apply gradient within the clipped region (top and corners only)
            painter.setClipPath(path)

            # Create vertical gradient for top fade only (keeping top and corner gradients)
            v_gradient = QtGui.QLinearGradient(0, rect.top(), 0, rect.bottom())
            v_gradient.setColorAt(0.0, QColor(30, 30, 30, 180))   # Semi-transparent dark at top
            v_gradient.setColorAt(0.2, QColor(30, 30, 30, 0))     # Transparent (no effect)
            v_gradient.setColorAt(1.0, QColor(30, 30, 30, 0))     # Transparent at bottom (no fade)
            painter.fillRect(rect, QBrush(v_gradient))

            # Reset clipping
            painter.setClipping(False)

            bg_brush = None  # We've already drawn the background
            text_color = QColor('#e0e0e0')  # Light grey text
            border_color = QColor('#555555')  # Medium border
        else:
            bg_brush = QBrush(QColor('#1E1E1E'))  # Dark grey for unselected
            text_color = QColor('#b0b0b0')  # Medium light grey text
            border_color = QColor('#404040')  # Dark border

        # Mouse hover effect with greyscale
        if self.tabAt(self.mapFromGlobal(QtGui.QCursor.pos())) == index and not selected:
            bg_brush = QBrush(QColor('#353535'))  # Slightly lighter grey on hover
            text_color = QColor('#d0d0d0')  # Slightly lighter text on hover

        # Draw tab background with Google-style flat design
        if bg_brush is not None:  # Only draw if we haven't already drawn the background
            painter.fillRect(rect, bg_brush)

        # Draw minimal borders for clean Google-style look
        pen = QPen(border_color, 1)
        painter.setPen(pen)

        if selected:
            # Selected tab - draw bottom accent line only (Google style)
            accent_pen = QPen(QColor('#3E3E3E'), 2)  # User specified color for glow/accent line
            painter.setPen(accent_pen)
            painter.drawLine(rect.bottomLeft(), rect.bottomRight())
        else:
            # Unselected tab - minimal bottom border
            painter.drawLine(rect.bottomLeft(), rect.bottomRight())

        # Draw text with left alignment as preferred
        painter.setPen(text_color)
        text_rect = rect.adjusted(12, 0, -12, 0)  # More padding for better spacing
        painter.drawText(text_rect, Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft,
                        self.tabText(index))

        # Draw separator line between unselected tabs (centered, 30% to 70% of tab height)
        if not selected and index < self.count() - 1:  # Don't draw separator after the last tab
            next_selected = (index + 1) == self.currentIndex()
            if not next_selected:  # Only draw separator if next tab is also unselected
                # Calculate separator position and dimensions
                separator_color = QColor('#404040')  # Subtle separator color
                separator_pen = QPen(separator_color, 1)
                painter.setPen(separator_pen)

                # Position separator at right edge of current tab
                separator_x = rect.right()

                # Calculate vertical position (centered, spanning 30% to 70% of height)
                tab_height = rect.height()
                separator_start_y = rect.top() + int(tab_height * 0.3)
                separator_end_y = rect.top() + int(tab_height * 0.7)

                # Draw the separator line
                painter.drawLine(separator_x, separator_start_y, separator_x, separator_end_y)


class CustomTabWidget(QTabWidget):
    """Custom tab widget with PyQt-based styling and uniform tab dimensions."""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Set custom tab bar
        custom_tab_bar = CustomTabBar()
        self.setTabBar(custom_tab_bar)

        # Configure tab widget
        self.setTabsClosable(False)
        self.setMovable(True)
        self.setUsesScrollButtons(True)

        # Set background color
        self.setAutoFillBackground(True)
        palette = self.palette()
        palette.setColor(self.backgroundRole(), QColor(THEME_COLORS['background']))
        self.setPalette(palette)

        # Apply minimal styling for the pane only
        self.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {THEME_COLORS['borders']};
                background-color: {THEME_COLORS['background']};
                border-radius: 4px;
                margin-top: 2px;
            }}
        """)

    def addTab(self, widget, label):
        """Override addTab to ensure consistent tab sizing."""
        index = super().addTab(widget, label)
        # Force tab bar to update its size hints
        self.tabBar().update()
        return index
    
    def resizeEvent(self, event):
        """Handle resize events."""
        super().resizeEvent(event)
        # Ensure tabs maintain consistent size
        tab_bar = self.tabBar()
        if isinstance(tab_bar, CustomTabBar):
            tab_bar.update()


class DetachableCustomTabWidget(CustomTabWidget):
    """Custom tab widget with detachable functionality."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Initialize drag state
        self.drag_start_pos = None
        self.dragging = False
        self.drag_tab_index = -1
        self.drag_initiated = False
        
        # Set the tab bar to accept drops
        self.tabBar().setAcceptDrops(True)
        
        # Install event filter on tab bar to handle mouse events
        self.tabBar().installEventFilter(self)
        
        # Track detached tabs
        self.detached_tabs = {}
    
    def eventFilter(self, source, event):
        """Filter events for drag and drop functionality."""
        if source == self.tabBar():
            if event.type() == QtCore.QEvent.Type.MouseButtonPress:
                if event.button() == Qt.MouseButton.LeftButton:
                    self.drag_start_pos = event.pos()
                    self.drag_tab_index = self.tabBar().tabAt(event.pos())
                    self.drag_initiated = False
                    
            elif event.type() == QtCore.QEvent.Type.MouseMove:
                if (self.drag_start_pos is not None and 
                    event.buttons() == Qt.MouseButton.LeftButton and
                    not self.drag_initiated):
                    
                    # Check if we've moved far enough to start a drag
                    if ((event.pos() - self.drag_start_pos).manhattanLength() >= 
                        QtWidgets.QApplication.startDragDistance()):
                        self.drag_initiated = True
                        # Could implement detach logic here if needed
                        
            elif event.type() == QtCore.QEvent.Type.MouseButtonRelease:
                self.drag_start_pos = None
                self.drag_initiated = False
                self.drag_tab_index = -1
        
        return super().eventFilter(source, event)
