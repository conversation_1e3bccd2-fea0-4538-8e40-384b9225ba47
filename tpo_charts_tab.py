"""
TPO Charts Tab for DataDriven Application
"""

import pandas as pd
import numpy as np
from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg
import logging

# Set up logging
logger = logging.getLogger(__name__)

# Import theme colors
try:
    import theme
    THEME_COLORS = theme.DEFAULT
except ImportError:
    # Fallback colors if theme module is not available
    THEME_COLORS = {
        'background': '#2b2b2b',
        'control_panel': '#3c3c3c',
        'text': '#ffffff',
        'bullish': '#4CAF50',
        'bearish': '#F44336',
        'borders': '#555555',
        'highlight': '#FFC107'
    }


class TPOChartsTab(QtWidgets.QWidget):
    """
    TPO Charts tab with symbol/timeframe input controls and plain chart.
    """

    def __init__(self, parent=None):
        """
        Initialize the TPO charts tab.

        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        self.parent = parent

        # Initialize data storage
        self.data = pd.DataFrame()

        # Initialize default chart settings
        self.chart_settings = {
            'show_single_print': False,
            'extend_single_print': False,
            'show_poor_high_low': False,
            'extend_poor_high_low': False,
            'show_va_lines': True,
            'extend_va_lines': False,
            'show_poc_line': True,
            'extend_poc_lines': False,
            'mark_initial_balance': False,
            'mark_open': False,
            'mark_close': False,
            'show_100_ib_stdev': False,
            'show_50_ib_stdev': False,
            'split_sessions': False,
            'va_percentage': 70
        }

        # Store profile data for extension calculations
        self.profile_data = []  # List of profile info for extension logic

        # Initialize UI
        self.init_ui()

        # Initialize data dispatcher for fetching data
        self.init_data_dispatcher()

        # Load saved chart settings
        self.load_chart_settings()

    def init_ui(self):
        """Initialize the user interface."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Input controls panel
        self.create_input_controls(main_layout)

        # Chart widget
        self.create_chart_widget(main_layout)

    def create_input_controls(self, main_layout):
        """Create the input controls panel."""
        # Controls panel
        controls_panel = QtWidgets.QFrame()
        controls_panel.setStyleSheet(f"""
            QFrame {{
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 5px;
                padding: 10px;
            }}
        """)
        controls_layout = QtWidgets.QHBoxLayout(controls_panel)
        controls_layout.setSpacing(15)

        # Symbol input
        symbol_label = QtWidgets.QLabel("Symbol:")
        symbol_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-weight: bold;")
        self.symbol_input = QtWidgets.QLineEdit()
        self.symbol_input.setPlaceholderText("Enter symbol (e.g., SPY)")
        self.symbol_input.setMaximumWidth(120)
        self.symbol_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 5px;
            }}
        """)
        self.symbol_input.returnPressed.connect(self.fetch_data)

        # Timeframe selection
        timeframe_label = QtWidgets.QLabel("Timeframe:")
        timeframe_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-weight: bold;")
        self.timeframe_combo = QtWidgets.QComboBox()
        self.timeframe_combo.addItems(["1m", "5m", "15m", "30m", "60m", "1d"])
        self.timeframe_combo.setCurrentText("30m")
        self.timeframe_combo.setMaximumWidth(80)
        self.timeframe_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 5px;
            }}
            QComboBox::drop-down {{
                border: none;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {THEME_COLORS['text']};
            }}
        """)

        # Days to load
        days_label = QtWidgets.QLabel("Days to Load:")
        days_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-weight: bold;")
        self.days_spin = QtWidgets.QSpinBox()
        self.days_spin.setRange(1, 365)
        self.days_spin.setValue(30)
        self.days_spin.setMaximumWidth(80)
        self.days_spin.setStyleSheet(f"""
            QSpinBox {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 5px;
            }}
        """)

        # Tick increment input
        tick_label = QtWidgets.QLabel("Tick Size:")
        tick_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-weight: bold;")
        self.tick_increment_input = QtWidgets.QDoubleSpinBox()
        self.tick_increment_input.setRange(0.01, 100.0)
        self.tick_increment_input.setValue(0.25)  # Default quarter tick
        self.tick_increment_input.setSingleStep(0.01)
        self.tick_increment_input.setDecimals(2)
        self.tick_increment_input.setMaximumWidth(80)
        self.tick_increment_input.setStyleSheet(f"""
            QDoubleSpinBox {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 5px;
            }}
        """)
        self.tick_increment_input.valueChanged.connect(self.on_tick_increment_changed)

        # Fetch button
        self.fetch_button = QtWidgets.QPushButton("Fetch Data")
        self.fetch_button.setMaximumWidth(100)
        self.fetch_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['highlight']};
                color: {THEME_COLORS['background']};
                border: none;
                border-radius: 3px;
                padding: 8px 15px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #FFD54F;
            }}
            QPushButton:pressed {{
                background-color: #FF8F00;
            }}
            QPushButton:disabled {{
                background-color: {THEME_COLORS['borders']};
                color: {THEME_COLORS['text']};
            }}
        """)
        self.fetch_button.clicked.connect(self.fetch_data)

        # Profile button with dropdown
        self.profile_button = QtWidgets.QComboBox()
        self.profile_button.addItems(["Profile", "Daily Profile", "Weekly Profile"])
        self.profile_button.setCurrentText("Profile")
        self.profile_button.setMaximumWidth(120)
        self.profile_button.setStyleSheet(f"""
            QComboBox {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 8px 15px;
                font-weight: bold;
            }}
            QComboBox::drop-down {{
                border: none;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {THEME_COLORS['text']};
            }}
        """)
        self.profile_button.currentTextChanged.connect(self.on_profile_changed)

        # Chart Settings button
        self.settings_button = QtWidgets.QPushButton("Chart Settings")
        self.settings_button.setMaximumWidth(120)
        self.settings_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 8px 15px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['borders']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['background']};
            }}
        """)
        self.settings_button.clicked.connect(self.show_chart_settings)

        # Status label
        self.status_label = QtWidgets.QLabel("Ready - Enter symbol and fetch data")
        self.status_label.setStyleSheet(f"color: {THEME_COLORS['text']};")

        # Add widgets to layout
        controls_layout.addWidget(symbol_label)
        controls_layout.addWidget(self.symbol_input)
        controls_layout.addWidget(timeframe_label)
        controls_layout.addWidget(self.timeframe_combo)
        controls_layout.addWidget(days_label)
        controls_layout.addWidget(self.days_spin)
        controls_layout.addWidget(tick_label)
        controls_layout.addWidget(self.tick_increment_input)
        controls_layout.addWidget(self.fetch_button)
        controls_layout.addWidget(self.profile_button)
        controls_layout.addWidget(self.settings_button)
        controls_layout.addStretch()
        controls_layout.addWidget(self.status_label)

        main_layout.addWidget(controls_panel)

    def create_chart_widget(self, main_layout):
        """Create the chart widget."""
        # Chart widget
        self.chart_widget = pg.PlotWidget()
        self.chart_widget.setBackground(THEME_COLORS['background'])
        self.chart_widget.setLabel('left', 'Price', color=THEME_COLORS['text'])
        self.chart_widget.setLabel('bottom', 'Time', color=THEME_COLORS['text'])
        self.chart_widget.showGrid(x=False, y=False)  # Disable grid

        # Style the chart
        self.chart_widget.getAxis('left').setPen(color=THEME_COLORS['text'])
        self.chart_widget.getAxis('bottom').setPen(color=THEME_COLORS['text'])
        self.chart_widget.getAxis('left').setTextPen(color=THEME_COLORS['text'])
        self.chart_widget.getAxis('bottom').setTextPen(color=THEME_COLORS['text'])

        main_layout.addWidget(self.chart_widget, 1)  # Give chart most of the space

    def init_data_dispatcher(self):
        """Initialize yfinance for data fetching."""
        # We'll use yfinance directly instead of the data dispatcher
        pass

    def fetch_data(self):
        """Fetch data using yfinance directly."""
        try:
            # Get values from local controls
            symbol = self.symbol_input.text().strip().upper()
            timeframe = self.timeframe_combo.currentText()
            days_to_load = self.days_spin.value()

            if not symbol:
                self.status_label.setText("Please enter a symbol")
                return

            # Disable fetch button during data loading
            self.fetch_button.setEnabled(False)
            self.fetch_button.setText("Fetching...")

            # Update status
            self.status_label.setText(f"Fetching {timeframe} data for {symbol} ({days_to_load} days)...")

            # Fetch data using yfinance directly
            self.fetch_yfinance_data(symbol, timeframe, days_to_load)

        except Exception as e:
            logger.error(f"Error in fetch_data: {str(e)}")
            self.status_label.setText(f"Error: {str(e)}")
            self.fetch_button.setEnabled(True)
            self.fetch_button.setText("Fetch Data")

    def fetch_yfinance_data(self, symbol, timeframe, days_to_load):
        """Fetch data using yfinance directly with proper date calculation."""
        try:
            import yfinance as yf
            from datetime import datetime, timedelta

            # Calculate proper date range accounting for weekends and market hours
            end_date = datetime.now()

            # For intraday data, we need to account for weekends and holidays
            # Add extra days to ensure we get enough trading days
            if timeframe in ['1m', '2m', '5m', '15m', '30m', '60m', '90m']:
                # For intraday data, multiply by ~1.4 to account for weekends
                # and add some buffer for holidays
                calendar_days = int(days_to_load * 1.5) + 10
            else:
                # For daily data, multiply by ~1.4 to account for weekends
                calendar_days = int(days_to_load * 1.4) + 5

            start_date = end_date - timedelta(days=calendar_days)

            # Fetch data with extended date range
            ticker = yf.Ticker(symbol)
            data = ticker.history(start=start_date, end=end_date, interval=timeframe)

            if not data.empty:
                # For intraday data, filter to get approximately the requested number of trading days
                if timeframe in ['1m', '2m', '5m', '15m', '30m', '60m', '90m']:
                    data = self.filter_to_trading_days(data, days_to_load)
                else:
                    # For daily data, just take the last N days
                    data = data.tail(days_to_load)

                self.on_data_fetched(symbol, data)
            else:
                self.on_data_error("No data returned for symbol")

        except Exception as e:
            self.on_data_error(f"Error fetching data: {str(e)}")

    def filter_to_trading_days(self, data, days_requested):
        """Filter intraday data to get approximately the requested number of trading days."""
        try:
            if data.empty:
                return data

            # Get unique trading dates
            trading_dates = data.index.date
            unique_dates = sorted(set(trading_dates), reverse=True)

            # Take the last N trading days
            if len(unique_dates) >= days_requested:
                cutoff_date = unique_dates[days_requested - 1]
                filtered_data = data[data.index.date >= cutoff_date]
            else:
                # If we don't have enough trading days, return all data
                filtered_data = data

            return filtered_data

        except Exception as e:
            logger.error(f"Error filtering trading days: {str(e)}")
            # If filtering fails, return the original data
            return data

    def on_data_fetched(self, symbol, data):
        """Handle successful data fetch."""
        try:
            self.data = data

            # Calculate trading days and bars per day for status
            if not data.empty:
                trading_dates = len(set(data.index.date))
                bars_per_day = len(data) / trading_dates if trading_dates > 0 else 0

                # Get date range
                start_date = data.index[0].strftime('%Y-%m-%d')
                end_date = data.index[-1].strftime('%Y-%m-%d')

                self.status_label.setText(
                    f"Loaded: {symbol} - {len(data)} bars, {trading_dates} trading days "
                    f"({bars_per_day:.1f} bars/day) from {start_date} to {end_date}"
                )
            else:
                self.status_label.setText(f"Data loaded: {symbol} - No data")

            # Update chart
            self.update_chart()

        except Exception as e:
            logger.error(f"Error processing fetched data: {str(e)}")
            self.status_label.setText(f"Error processing data: {str(e)}")
        finally:
            # Re-enable fetch button
            self.fetch_button.setEnabled(True)
            self.fetch_button.setText("Fetch Data")

    def on_data_error(self, error_message):
        """Handle data fetch error."""
        self.status_label.setText(f"Error: {error_message}")
        self.fetch_button.setEnabled(True)
        self.fetch_button.setText("Fetch Data")

    def update_chart(self):
        """Update the chart with high-low bars."""
        try:
            if self.data.empty:
                return

            # Clear previous plot
            self.chart_widget.clear()

            # Create high-low bars
            self.create_high_low_bars()

            # Set chart title
            symbol = self.symbol_input.text().strip().upper()
            timeframe = self.timeframe_combo.currentText()
            self.chart_widget.setTitle(f"{symbol} - {timeframe} High-Low Bars", color=THEME_COLORS['text'])

            # Auto-range the view
            self.chart_widget.autoRange()

        except Exception as e:
            logger.error(f"Error updating chart: {str(e)}")
            self.status_label.setText(f"Error updating chart: {str(e)}")

    def create_high_low_bars(self):
        """Create high-low rectangles from the data."""
        try:
            # Get high and low values as numpy arrays
            highs = np.array(self.data['High'].values)
            lows = np.array(self.data['Low'].values)
            times = np.array(range(len(self.data)))

            # Create rectangles for each high-low range
            for time, high, low in zip(times, highs, lows):
                # Create a rectangle from low to high
                rect_item = pg.QtWidgets.QGraphicsRectItem(
                    time - 0.4,  # Left edge (bar width)
                    low,         # Bottom edge (low price)
                    0.8,         # Width of rectangle
                    high - low   # Height (high - low)
                )

                # Set rectangle appearance
                rect_item.setPen(pg.mkPen(color=THEME_COLORS['text'], width=1))
                rect_item.setBrush(pg.mkBrush(color=THEME_COLORS['text'], alpha=50))

                # Add rectangle to the plot
                self.chart_widget.addItem(rect_item)

        except Exception as e:
            logger.error(f"Error creating high-low rectangles: {str(e)}")
            raise

    def on_data_fetched_universal(self, symbol, data):
        """Handle data fetched from universal controls."""
        # This method allows the tab to receive data from universal controls
        # if connected in the main application
        self.symbol_input.setText(symbol)
        self.on_data_fetched(symbol, data)

    def on_tick_increment_changed(self):
        """Handle tick increment change - refresh current view."""
        current_profile = self.profile_button.currentText()
        if current_profile == "Daily Profile":
            self.create_daily_profile()
        elif current_profile == "Weekly Profile":
            self.create_weekly_profile()
        elif current_profile == "Profile":
            self.update_chart()

    def on_profile_changed(self, profile_type):
        """Handle profile type change."""
        if profile_type == "Profile":
            # Default view - show regular rectangles
            self.update_chart()
        elif profile_type == "Daily Profile":
            self.create_daily_profile()
        elif profile_type == "Weekly Profile":
            self.create_weekly_profile()

    def create_daily_profile(self):
        """Create TPO-style daily profiles."""
        try:
            if self.data.empty:
                return

            # Clear previous plot
            self.chart_widget.clear()

            # Clear profile data for new chart
            self.profile_data = []

            # Group data by trading session (auto-detect instrument type)
            daily_groups = self.group_by_trading_session(self.data)

            # First pass: calculate the maximum width needed for each profile
            profile_widths = {}
            profile_positions = {}
            dates_list = []

            for date, day_data in daily_groups:
                if not day_data.empty:
                    max_width = self.calculate_profile_width(day_data)
                    profile_widths[date] = max_width
                    dates_list.append(date)

            # First pass: Create profiles with dynamic spacing and store data
            profile_x_position = 0
            min_spacing = 0.2  # Minimum gap between profiles

            for date, day_data in daily_groups:
                if not day_data.empty:
                    self.create_tpo_profile(day_data, profile_x_position)

                    # Store the center position for axis labeling
                    profile_width = profile_widths[date]
                    center_position = profile_x_position + profile_width/2
                    profile_positions[date] = center_position

                    # Move to next position based on current profile width plus spacing
                    profile_x_position += profile_width + min_spacing

            # Second pass: Add extensions now that all profiles are created
            self.add_extensions()

            # Set up custom X-axis with date labels
            self.setup_date_axis(profile_positions, dates_list, is_weekly=False)

            # Set chart title and configure axes
            symbol = self.symbol_input.text().strip().upper()
            self.chart_widget.setTitle(f"{symbol} - Daily TPO Profiles", color=THEME_COLORS['text'])
            self.chart_widget.setLabel('left', 'Price', color=THEME_COLORS['text'])
            self.chart_widget.setLabel('bottom', 'Trading Days', color=THEME_COLORS['text'])
            self.chart_widget.autoRange()

        except Exception as e:
            logger.error(f"Error creating daily profile: {str(e)}")
            self.status_label.setText(f"Error creating daily profile: {str(e)}")

    def detect_instrument_type(self, symbol):
        """Detect instrument type based on symbol."""
        try:
            if not symbol:
                return 'equity'  # Default to equity

            symbol = symbol.upper().strip()

            # Futures patterns
            futures_patterns = [
                'ES', 'NQ', 'YM', 'RTY',  # US Index futures
                'CL', 'NG', 'GC', 'SI',   # Energy/Metals futures
                'ZB', 'ZN', 'ZF', 'ZT',   # Bond futures
                'ZC', 'ZS', 'ZW', 'ZL',   # Agricultural futures
                'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF'  # Currency futures
            ]

            # Check if symbol starts with futures patterns
            for pattern in futures_patterns:
                if symbol.startswith(pattern):
                    return 'futures'

            # Forex patterns (usually pairs like EURUSD, GBPJPY)
            if len(symbol) == 6 and symbol.isalpha():
                return 'forex'

            # Options patterns (usually contain PUT/CALL or have specific format)
            if any(word in symbol for word in ['PUT', 'CALL', 'P', 'C']) and any(char.isdigit() for char in symbol):
                return 'options'

            # Default to equity
            return 'equity'

        except Exception as e:
            logger.error(f"Error detecting instrument type: {str(e)}")
            return 'equity'

    def group_by_trading_session(self, data):
        """Group data by trading session based on instrument type."""
        try:
            if data.empty:
                return []

            # Convert to DataFrame if it's a Series
            if hasattr(data, 'to_frame'):
                data = data.to_frame()

            # Ensure we have datetime index
            if not isinstance(data.index, pd.DatetimeIndex):
                return []

            # Check if split sessions is enabled
            if self.chart_settings.get('split_sessions', False):
                return self.group_by_split_sessions(data)

            # Detect instrument type
            symbol = self.symbol_input.text().strip()
            instrument_type = self.detect_instrument_type(symbol)

            # Create a copy to avoid modifying original data
            df = data.copy()

            # Use configurable session times from settings
            return self.group_by_configurable_session(df)

        except Exception as e:
            logger.error(f"Error grouping by trading session: {str(e)}")
            return []

    def group_by_overnight_session(self, df, session_start_hour):
        """Group data by overnight trading sessions (futures/forex)."""
        try:
            session_dates = []

            for timestamp in df.index:
                # If time is before session start hour, it belongs to current day's session
                # If time is at or after session start hour, it belongs to next day's session
                if timestamp.hour < session_start_hour:
                    session_date = timestamp.date()
                else:
                    session_date = (timestamp + pd.Timedelta(days=1)).date()

                session_dates.append(session_date)

            df['session_date'] = session_dates

            # Group by session date
            grouped = df.groupby('session_date')

            # Return as list of tuples (session_date, session_data)
            session_groups = []
            for session_date, session_data in grouped:
                # Remove the session_date column from the data
                session_data = session_data.drop('session_date', axis=1)
                session_groups.append((session_date, session_data))

            return session_groups

        except Exception as e:
            logger.error(f"Error grouping by overnight session: {str(e)}")
            return []

    def group_by_regular_hours(self, df):
        """Group data by regular trading hours (equity/options)."""
        try:
            # For regular hours, use calendar days
            daily_groups = df.groupby(df.index.date)

            # Return as list of tuples (date, day_data)
            session_groups = []
            for date, day_data in daily_groups:
                session_groups.append((date, day_data))

            return session_groups

        except Exception as e:
            logger.error(f"Error grouping by regular hours: {str(e)}")
            return []

    def group_by_split_sessions(self, df):
        """Group data by split sessions (ETH and RTH)."""
        try:
            # ETH (Extended Trading Hours): 6:00 PM previous day to 9:29 AM current day
            # RTH (Regular Trading Hours): 9:30 AM to 5:00 PM current day

            session_groups = []

            # Sort data by timestamp
            df_sorted = df.sort_index()

            # Group sessions by trading day (based on RTH date)
            current_session_data = {'eth': [], 'rth': []}
            current_rth_date = None

            for timestamp, row in df_sorted.iterrows():
                hour = timestamp.hour
                minute = timestamp.minute
                date = timestamp.date()

                # Determine if this is RTH or ETH
                # RTH: 9:30 AM to 5:00 PM
                if (hour == 9 and minute >= 30) or (10 <= hour < 17) or (hour == 17 and minute == 0):
                    # This is RTH data
                    if current_rth_date != date:
                        # New RTH session starting - save previous session if exists
                        if current_rth_date is not None:
                            self._save_split_session(session_groups, current_rth_date, current_session_data)

                        # Start new session
                        current_rth_date = date
                        current_session_data = {'eth': [], 'rth': []}

                    current_session_data['rth'].append((timestamp, row))

                else:
                    # This is ETH data (6 PM to 9:29 AM)
                    # ETH belongs to the next RTH session
                    if hour >= 18:  # 6 PM or later - belongs to next day's session
                        next_date = (timestamp + pd.Timedelta(days=1)).date()
                        if current_rth_date != next_date:
                            # Save previous session if exists
                            if current_rth_date is not None:
                                self._save_split_session(session_groups, current_rth_date, current_session_data)

                            # Start new session for next day
                            current_rth_date = next_date
                            current_session_data = {'eth': [], 'rth': []}

                    current_session_data['eth'].append((timestamp, row))

            # Save the last session
            if current_rth_date is not None:
                self._save_split_session(session_groups, current_rth_date, current_session_data)

            return session_groups

        except Exception as e:
            logger.error(f"Error grouping by split sessions: {str(e)}")
            return []

    def _save_split_session(self, session_groups, rth_date, session_data):
        """Helper method to save ETH and RTH sessions."""
        try:
            # Create ETH session if we have data
            if session_data['eth']:
                eth_df = pd.DataFrame([row for _, row in session_data['eth']],
                                    index=[ts for ts, _ in session_data['eth']])
                session_groups.append((f"{rth_date}_ETH", eth_df))

            # Create RTH session if we have data
            if session_data['rth']:
                rth_df = pd.DataFrame([row for _, row in session_data['rth']],
                                    index=[ts for ts, _ in session_data['rth']])
                session_groups.append((f"{rth_date}_RTH", rth_df))

        except Exception as e:
            logger.error(f"Error saving split session: {str(e)}")

    def group_by_configurable_session(self, df):
        """Group data by configurable session times from settings."""
        try:
            # Get session times from settings (with fallbacks)
            session_start_str = getattr(self, 'session_start_time', None)
            session_end_str = getattr(self, 'session_end_time', None)

            if session_start_str and hasattr(session_start_str, 'time'):
                session_start_hour = session_start_str.time().hour()
            else:
                session_start_hour = 18  # Default 6 PM

            if session_end_str and hasattr(session_end_str, 'time'):
                session_end_hour = session_end_str.time().hour()
            else:
                session_end_hour = 17  # Default 5 PM

            # Use overnight session logic if start time is after end time
            # (e.g., 18:00 start, 17:00 end = overnight session)
            if session_start_hour > session_end_hour:
                return self.group_by_overnight_session(df, session_start_hour)
            else:
                # Regular hours session (start time before end time)
                return self.group_by_regular_hours(df)

        except Exception as e:
            logger.error(f"Error grouping by configurable session: {str(e)}")
            return self.group_by_overnight_session(df, 18)  # Fallback to 6 PM start

    def group_sessions_by_week(self, session_groups):
        """Group futures trading sessions by week."""
        try:
            if not session_groups:
                return []

            # Group sessions by week
            weekly_data = {}

            for session_date, session_data in session_groups:
                # Get the week period for this session date
                week_period = pd.Timestamp(session_date).to_period('W-MON')

                if week_period not in weekly_data:
                    weekly_data[week_period] = []

                weekly_data[week_period].append(session_data)

            # Combine all sessions within each week
            weekly_groups = []
            for week_period, week_sessions in weekly_data.items():
                # Concatenate all session data for this week
                if week_sessions:
                    combined_week_data = pd.concat(week_sessions, ignore_index=False)
                    combined_week_data = combined_week_data.sort_index()
                    weekly_groups.append((week_period, combined_week_data))

            return weekly_groups

        except Exception as e:
            logger.error(f"Error grouping sessions by week: {str(e)}")
            return []

    def create_weekly_profile(self):
        """Create TPO-style weekly profiles."""
        try:
            if self.data.empty:
                return

            # Clear previous plot
            self.chart_widget.clear()

            # Clear profile data for new chart
            self.profile_data = []

            # Group data by trading sessions first (auto-detect instrument type), then by week
            session_groups = self.group_by_trading_session(self.data)

            # Now group sessions by week
            weekly_groups = self.group_sessions_by_week(session_groups)

            # First pass: calculate the maximum width needed for each profile
            profile_widths = {}
            profile_positions = {}
            weeks_list = []

            for week_period, week_data in weekly_groups:
                if not week_data.empty:
                    max_width = self.calculate_profile_width(week_data)
                    profile_widths[week_period] = max_width
                    weeks_list.append(week_period)

            # First pass: Create profiles with dynamic spacing and store data
            profile_x_position = 0
            min_spacing = 0.2  # Minimum gap between profiles

            for week_period, week_data in weekly_groups:
                if not week_data.empty:
                    self.create_tpo_profile(week_data, profile_x_position)

                    # Store the center position for axis labeling
                    profile_width = profile_widths[week_period]
                    center_position = profile_x_position + profile_width/2
                    profile_positions[week_period] = center_position

                    # Move to next position based on current profile width plus spacing
                    profile_x_position += profile_width + min_spacing

            # Second pass: Add extensions now that all profiles are created
            self.add_extensions()

            # Set up custom X-axis with week labels
            self.setup_date_axis(profile_positions, weeks_list, is_weekly=True)

            # Set chart title and configure axes
            symbol = self.symbol_input.text().strip().upper()
            self.chart_widget.setTitle(f"{symbol} - Weekly TPO Profiles", color=THEME_COLORS['text'])
            self.chart_widget.setLabel('left', 'Price', color=THEME_COLORS['text'])
            self.chart_widget.setLabel('bottom', 'Trading Weeks', color=THEME_COLORS['text'])
            self.chart_widget.autoRange()

        except Exception as e:
            logger.error(f"Error creating weekly profile: {str(e)}")
            self.status_label.setText(f"Error creating weekly profile: {str(e)}")

    def calculate_profile_width(self, period_data):
        """Calculate the maximum width needed for a TPO profile."""
        try:
            # Use user-defined tick increment
            tick_size = self.tick_increment_input.value()

            # Calculate price range
            min_price = period_data['Low'].min()
            max_price = period_data['High'].max()

            # Create price levels
            start_price = np.floor(min_price / tick_size) * tick_size
            end_price = np.ceil(max_price / tick_size) * tick_size
            price_levels = np.arange(start_price, end_price + tick_size, tick_size)

            # Count time periods at each price level
            level_counts = {}
            for _, row in period_data.iterrows():
                low, high = row['Low'], row['High']
                for price_level in price_levels:
                    if low <= price_level <= high:
                        level_counts[price_level] = level_counts.get(price_level, 0) + 1

            if not level_counts:
                return 0.5  # Default minimum width

            # Calculate maximum width needed
            max_count = max(level_counts.values())
            block_width = 0.08  # Same as in create_tpo_profile
            max_width = max_count * block_width

            return max_width

        except Exception as e:
            logger.error(f"Error calculating profile width: {str(e)}")
            return 0.5  # Default fallback width

    def setup_date_axis(self, profile_positions, dates_list, is_weekly=False):
        """Set up custom X-axis with date/week labels."""
        try:
            # Get the bottom axis
            bottom_axis = self.chart_widget.getAxis('bottom')

            # Create tick positions and labels
            tick_positions = []
            tick_labels = []

            for date_or_week in dates_list:
                if date_or_week in profile_positions:
                    position = profile_positions[date_or_week]
                    tick_positions.append(position)

                    if is_weekly:
                        # Format week labels
                        label = f"W{date_or_week.week}"
                    else:
                        # Format date labels
                        label = date_or_week.strftime('%m/%d')

                    tick_labels.append(label)

            # Set custom ticks
            if tick_positions and tick_labels:
                ticks = list(zip(tick_positions, tick_labels))
                bottom_axis.setTicks([ticks])

        except Exception as e:
            logger.error(f"Error setting up date axis: {str(e)}")

    def create_tpo_profile(self, period_data, x_position):
        """Create a professional TPO profile with individual blocks, VA, and POC."""
        try:
            # Calculate price range
            min_price = period_data['Low'].min()
            max_price = period_data['High'].max()
            price_range = max_price - min_price

            if price_range <= 0:
                return

            # Use user-defined tick increment
            tick_size = self.tick_increment_input.value()

            # Create price levels based on user-defined tick size
            start_price = np.floor(min_price / tick_size) * tick_size
            end_price = np.ceil(max_price / tick_size) * tick_size
            price_levels = np.arange(start_price, end_price + tick_size, tick_size)

            # Count time periods at each price level
            level_counts = {}
            for _, row in period_data.iterrows():
                low, high = row['Low'], row['High']
                for price_level in price_levels:
                    if low <= price_level <= high:
                        level_counts[price_level] = level_counts.get(price_level, 0) + 1

            if not level_counts:
                return

            # Calculate Value Area and POC
            total_tpos = sum(level_counts.values())
            poc_price = max(level_counts.keys(), key=lambda k: level_counts[k])
            poc_count = level_counts[poc_price]

            # Calculate 70% Value Area
            va_threshold = total_tpos * 0.70
            sorted_levels = sorted(level_counts.items(), key=lambda x: x[1], reverse=True)

            va_levels = set()
            va_tpos = 0
            for price, count in sorted_levels:
                va_levels.add(price)
                va_tpos += count
                if va_tpos >= va_threshold:
                    break

            va_high = max(va_levels) if va_levels else poc_price
            va_low = min(va_levels) if va_levels else poc_price

            # Create individual TPO blocks
            block_width = 0.08  # Width of each individual block
            block_height = tick_size * 0.8  # Height slightly less than tick size

            for price_level, count in level_counts.items():
                if count > 0:
                    for block_num in range(count):
                        # Calculate block position
                        block_x = x_position + (block_num * block_width)
                        block_y = price_level - (block_height / 2)

                        # Determine block color based on VA and POC using settings
                        if price_level == poc_price:
                            # POC - use POC color from settings
                            color = self.chart_settings.get('poc_color', '#FFFFFF')
                            alpha = 255
                        elif va_low <= price_level <= va_high:
                            # Inside Value Area - use VA color from settings
                            color = self.chart_settings.get('va_color', '#CCCCCC')
                            alpha = 200
                        else:
                            # Outside Value Area - use outside VA color from settings
                            color = self.chart_settings.get('outside_va_color', '#666666')
                            alpha = 150

                        # Create individual block
                        block_item = pg.QtWidgets.QGraphicsRectItem(
                            block_x,
                            block_y,
                            block_width,
                            block_height
                        )

                        # Set block appearance
                        block_item.setPen(pg.mkPen(color='#333333', width=0.5))
                        block_item.setBrush(pg.mkBrush(color=color, alpha=alpha))

                        # Add to chart
                        self.chart_widget.addItem(block_item)

            # Store profile data for extension calculations
            profile_info = {
                'x_position': x_position,
                'x_end': x_position + (max(level_counts.values()) * block_width),
                'level_counts': level_counts.copy(),
                'va_high': va_high if va_levels else None,
                'va_low': va_low if va_levels else None,
                'poc_price': poc_price,
                'poc_count': poc_count,
                'block_width': block_width
            }
            self.profile_data.append(profile_info)

            # Add basic Value Area lines (starting from end of blocks, like single prints)
            if va_levels and self.chart_settings.get('show_va_lines', True):
                # Calculate where VA high blocks end
                va_high_blocks_end = x_position + (level_counts.get(va_high, 0) * block_width)
                # Calculate where VA low blocks end
                va_low_blocks_end = x_position + (level_counts.get(va_low, 0) * block_width)

                # VA High line (starts from end of VA high blocks)
                va_line_color = self.chart_settings.get('va_line_color', '#FF6B6B')
                va_high_line = pg.PlotDataItem(
                    [va_high_blocks_end, x_position + (max(level_counts.values()) * block_width) + 0.02],
                    [va_high, va_high],
                    pen=pg.mkPen(color=va_line_color, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.chart_widget.addItem(va_high_line)

                # VA Low line (starts from end of VA low blocks)
                va_low_line = pg.PlotDataItem(
                    [va_low_blocks_end, x_position + (max(level_counts.values()) * block_width) + 0.02],
                    [va_low, va_low],
                    pen=pg.mkPen(color=va_line_color, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.chart_widget.addItem(va_low_line)

            # Add basic POC line (starting from end of POC blocks, like single prints)
            if self.chart_settings.get('show_poc_line', True):
                # POC line starts from end of POC blocks
                poc_blocks_end = x_position + (poc_count * block_width)
                poc_line_color = self.chart_settings.get('poc_line_color', '#FFD700')
                poc_line = pg.PlotDataItem(
                    [poc_blocks_end, x_position + (max(level_counts.values()) * block_width) + 0.02],
                    [poc_price, poc_price],
                    pen=pg.mkPen(color=poc_line_color, width=2)
                )
                self.chart_widget.addItem(poc_line)

            # Add Single Print and Poor High/Low analysis if settings are enabled
            if hasattr(self, 'chart_settings'):
                self.add_single_prints(level_counts, price_levels, x_position, block_width)
                self.add_poor_high_low(level_counts, price_levels, x_position, block_width)

                # Add Initial Balance, Open, and Close markers if enabled
                self.add_initial_balance(period_data, x_position, block_width)
                self.add_open_close_markers(period_data, x_position, block_width, level_counts)

        except Exception as e:
            logger.error(f"Error creating TPO profile: {str(e)}")
            raise

    def calculate_line_extension(self, price_level, current_x_position, block_width, current_blocks):
        """Calculate how far a line should extend until it hits a block in future profiles."""
        try:
            # Start extension from the end of current profile
            extension_start = current_x_position + (current_blocks * block_width)

            # Look through future profiles to find where this price level gets hit
            for profile in self.profile_data:
                # Only check profiles that are to the right of current position
                if profile['x_position'] > current_x_position:
                    # Check if this price level has blocks in the future profile
                    if price_level in profile['level_counts'] and profile['level_counts'][price_level] > 0:
                        # Found a hit! Return the x position where the hit occurs
                        return profile['x_position']

            # If no hit found, extend to a reasonable distance
            return extension_start + 3.0  # Default extension

        except Exception as e:
            logger.error(f"Error calculating line extension: {str(e)}")
            return current_x_position + (current_blocks * block_width) + 0.5  # Fallback

    def add_single_prints(self, level_counts, price_levels, x_position, block_width):
        """Add Single Print zones to the chart."""
        try:
            if not hasattr(self, 'chart_settings') or not self.chart_settings.get('show_single_print', False):
                return

            # Find the highest and lowest prices with blocks
            prices_with_blocks = sorted([price for price, count in level_counts.items() if count > 0])
            if len(prices_with_blocks) < 5:  # Need at least 5 levels for proper single print detection
                return

            highest_price = max(prices_with_blocks)
            lowest_price = min(prices_with_blocks)

            # Find where single print detection should start from the high
            # Start from high and go down until we find a level with 2+ blocks
            single_print_zone_start_from_high = None
            for price in sorted(prices_with_blocks, reverse=True):  # Start from high, go down
                if level_counts[price] >= 2:  # Found first level with 2+ blocks from high
                    single_print_zone_start_from_high = price
                    break

            # Find where single print detection should start from the low
            # Start from low and go up until we find a level with 2+ blocks
            single_print_zone_start_from_low = None
            for price in sorted(prices_with_blocks):  # Start from low, go up
                if level_counts[price] >= 2:  # Found first level with 2+ blocks from low
                    single_print_zone_start_from_low = price
                    break

            # Define the valid zone for single print detection
            # Only detect single prints between the first 2+ block levels from each extreme
            if (single_print_zone_start_from_high is None or
                single_print_zone_start_from_low is None or
                single_print_zone_start_from_high <= single_print_zone_start_from_low):
                return  # No valid zone for single print detection

            # Find single prints (count = 1) within the valid detection zone
            single_prints = []
            for price, count in level_counts.items():
                if (count == 1 and
                    price < single_print_zone_start_from_high and
                    price > single_print_zone_start_from_low):
                    single_prints.append(price)

            # Draw basic zones for single prints (extensions will be added later)
            for single_print_price in single_prints:
                # Single print zone starts from the right edge of the single block
                single_print_block_end = x_position + (1 * block_width)  # End of the single block
                zone_end_x = x_position + (max(level_counts.values()) * block_width)  # End of current profile
                zone_extension_length = zone_end_x - single_print_block_end
                zone_height = self.tick_increment_input.value() * 0.8

                # Create zone rectangle starting from end of single print block
                zone_rect = pg.QtWidgets.QGraphicsRectItem(
                    single_print_block_end,  # Start from end of single block
                    single_print_price - (zone_height / 2),
                    zone_extension_length,  # Basic extension to end of profile
                    zone_height
                )

                # Set zone appearance - use single print color from settings
                single_print_color = self.chart_settings.get('single_print_color', '#00FF00')
                zone_rect.setPen(pg.mkPen(color=single_print_color, width=2))
                zone_rect.setBrush(pg.mkBrush(color=single_print_color, alpha=80))

                self.chart_widget.addItem(zone_rect)

        except Exception as e:
            logger.error(f"Error adding single prints: {str(e)}")

    def add_poor_high_low(self, level_counts, price_levels, x_position, block_width):
        """Add Poor High/Low lines to the chart."""
        try:
            if not hasattr(self, 'chart_settings') or not self.chart_settings.get('show_poor_high_low', False):
                return

            # Find the highest and lowest prices with blocks
            prices_with_blocks = [price for price, count in level_counts.items() if count > 0]
            if not prices_with_blocks:
                return

            highest_price = max(prices_with_blocks)
            lowest_price = min(prices_with_blocks)

            # Check for poor high (high with 2+ blocks) - basic lines only
            if level_counts.get(highest_price, 0) >= 2:
                # Draw horizontal line from end of poor high blocks to end of profile
                poor_high_blocks_end = x_position + (level_counts[highest_price] * block_width)
                line_end_x = x_position + (max(level_counts.values()) * block_width)
                poor_high_low_color = self.chart_settings.get('poor_high_low_color', '#FF9900')

                poor_high_line = pg.PlotDataItem(
                    [poor_high_blocks_end, line_end_x],
                    [highest_price, highest_price],
                    pen=pg.mkPen(color=poor_high_low_color, width=2, style=QtCore.Qt.PenStyle.SolidLine)
                )
                self.chart_widget.addItem(poor_high_line)

                # Add arrow or marker at the end
                arrow_size = 0.02
                arrow_item = pg.ArrowItem(
                    angle=0,  # Pointing right
                    tipAngle=30,
                    baseAngle=20,
                    headLen=arrow_size,
                    tailLen=arrow_size,
                    pen=pg.mkPen(color=poor_high_low_color, width=2),
                    brush=pg.mkBrush(color=poor_high_low_color)
                )
                arrow_item.setPos(line_end_x, highest_price)
                self.chart_widget.addItem(arrow_item)

            # Check for poor low (low with 2+ blocks) - basic lines only
            if level_counts.get(lowest_price, 0) >= 2:
                # Draw horizontal line from end of poor low blocks to end of profile
                poor_low_blocks_end = x_position + (level_counts[lowest_price] * block_width)
                line_end_x = x_position + (max(level_counts.values()) * block_width)
                poor_high_low_color = self.chart_settings.get('poor_high_low_color', '#FF9900')

                poor_low_line = pg.PlotDataItem(
                    [poor_low_blocks_end, line_end_x],
                    [lowest_price, lowest_price],
                    pen=pg.mkPen(color=poor_high_low_color, width=2, style=QtCore.Qt.PenStyle.SolidLine)
                )
                self.chart_widget.addItem(poor_low_line)

                # Add arrow or marker at the end
                arrow_size = 0.02
                arrow_item = pg.ArrowItem(
                    angle=0,  # Pointing right
                    tipAngle=30,
                    baseAngle=20,
                    headLen=arrow_size,
                    tailLen=arrow_size,
                    pen=pg.mkPen(color=poor_high_low_color, width=2),
                    brush=pg.mkBrush(color=poor_high_low_color)
                )
                arrow_item.setPos(line_end_x, lowest_price)
                self.chart_widget.addItem(arrow_item)

        except Exception as e:
            logger.error(f"Error adding poor high/low: {str(e)}")

    def add_initial_balance(self, period_data, x_position, block_width):
        """Add Initial Balance bar to the left of the TPO profile."""
        try:
            if not self.chart_settings.get('mark_initial_balance', False):
                return

            # Calculate Initial Balance based on instrument type
            symbol = self.symbol_input.text().strip()
            instrument_type = self.detect_instrument_type(symbol)

            # Sort data by time to get session start
            period_data_sorted = period_data.sort_index()

            if len(period_data_sorted) == 0:
                return

            # Calculate IB based on instrument type
            if instrument_type in ['futures', 'forex']:
                # For futures/forex: First hour after session start (6 PM or 5 PM)
                session_start = period_data_sorted.index[0]
                session_start_hour = session_start.hour

                # Find the first hour of actual trading after session start
                first_hour_end = session_start + pd.Timedelta(hours=1)
                first_hour_data = period_data_sorted[
                    (period_data_sorted.index >= session_start) &
                    (period_data_sorted.index <= first_hour_end)
                ]

            else:  # equity/options
                # For equity/options: First hour after market open (9:30 AM)
                # Find 9:30 AM or closest time after
                market_open_time = None
                for timestamp in period_data_sorted.index:
                    if timestamp.hour >= 9 and (timestamp.hour > 9 or timestamp.minute >= 30):
                        market_open_time = timestamp
                        break

                if market_open_time is None:
                    # Fallback to first available data
                    market_open_time = period_data_sorted.index[0]

                # Get first hour after market open
                first_hour_end = market_open_time + pd.Timedelta(hours=1)
                first_hour_data = period_data_sorted[
                    (period_data_sorted.index >= market_open_time) &
                    (period_data_sorted.index <= first_hour_end)
                ]

            if len(first_hour_data) == 0:
                return

            # Calculate IB high and low from first hour
            ib_high = first_hour_data['High'].max()
            ib_low = first_hour_data['Low'].min()

            # Draw Initial Balance bar to the left of the profile (narrow)
            ib_bar_x = x_position - 0.06  # Position to the left
            ib_bar_width = 0.015  # Very narrow bar
            ib_bar_height = ib_high - ib_low

            # Create Initial Balance rectangle
            ib_rect = pg.QtWidgets.QGraphicsRectItem(
                ib_bar_x,
                ib_low,
                ib_bar_width,
                ib_bar_height
            )

            # Set IB appearance
            ib_color = self.chart_settings.get('initial_balance_color', '#00FFFF')
            ib_rect.setPen(pg.mkPen(color=ib_color, width=2))
            ib_rect.setBrush(pg.mkBrush(color=ib_color, alpha=180))

            self.chart_widget.addItem(ib_rect)

            # Add IB Standard Deviation lines if enabled
            self.add_ib_stdev_lines(first_hour_data, ib_high, ib_low, x_position)

        except Exception as e:
            logger.error(f"Error adding initial balance: {str(e)}")

    def add_ib_stdev_lines(self, first_hour_data, ib_high, ib_low, x_position):
        """Add Initial Balance Standard Deviation lines based on IB range."""
        try:
            if len(first_hour_data) == 0:
                return

            # Calculate IB range
            ib_range = ib_high - ib_low

            if ib_range <= 0:
                return

            # Add 100% IB StDev lines (1 full IB range above/below)
            if self.chart_settings.get('show_100_ib_stdev', False):
                # 100% = full IB range above IB high and below IB low
                stdev_100_high = ib_high + ib_range  # IB high + full range
                stdev_100_low = ib_low - ib_range    # IB low - full range

                ib_100_stdev_color = self.chart_settings.get('ib_100_stdev_color', '#9370DB')

                # 100% StDev High line
                stdev_100_high_line = pg.PlotDataItem(
                    [x_position - 0.05, x_position + 2.0],  # Extend across chart
                    [stdev_100_high, stdev_100_high],
                    pen=pg.mkPen(color=ib_100_stdev_color, width=1, style=QtCore.Qt.PenStyle.DotLine)
                )
                self.chart_widget.addItem(stdev_100_high_line)

                # 100% StDev Low line
                stdev_100_low_line = pg.PlotDataItem(
                    [x_position - 0.05, x_position + 2.0],  # Extend across chart
                    [stdev_100_low, stdev_100_low],
                    pen=pg.mkPen(color=ib_100_stdev_color, width=1, style=QtCore.Qt.PenStyle.DotLine)
                )
                self.chart_widget.addItem(stdev_100_low_line)

            # Add 50% IB StDev lines (0.5 IB range above/below)
            if self.chart_settings.get('show_50_ib_stdev', False):
                # 50% = half IB range above IB high and below IB low
                half_range = ib_range * 0.5
                stdev_50_high = ib_high + half_range  # IB high + half range
                stdev_50_low = ib_low - half_range    # IB low - half range

                ib_50_stdev_color = self.chart_settings.get('ib_50_stdev_color', '#DDA0DD')

                # 50% StDev High line
                stdev_50_high_line = pg.PlotDataItem(
                    [x_position - 0.05, x_position + 2.0],  # Extend across chart
                    [stdev_50_high, stdev_50_high],
                    pen=pg.mkPen(color=ib_50_stdev_color, width=1, style=QtCore.Qt.PenStyle.DashDotLine)
                )
                self.chart_widget.addItem(stdev_50_high_line)

                # 50% StDev Low line
                stdev_50_low_line = pg.PlotDataItem(
                    [x_position - 0.05, x_position + 2.0],  # Extend across chart
                    [stdev_50_low, stdev_50_low],
                    pen=pg.mkPen(color=ib_50_stdev_color, width=1, style=QtCore.Qt.PenStyle.DashDotLine)
                )
                self.chart_widget.addItem(stdev_50_low_line)

        except Exception as e:
            logger.error(f"Error adding IB standard deviation lines: {str(e)}")

    def add_open_close_markers(self, period_data, x_position, block_width, level_counts):
        """Add Open and Close price markers by coloring the actual TPO blocks."""
        try:
            if len(period_data) == 0:
                return

            # Get opening and closing prices
            period_data_sorted = period_data.sort_index()
            open_price = period_data_sorted.iloc[0]['Open']
            close_price = period_data_sorted.iloc[-1]['Close']

            tick_size = self.tick_increment_input.value()

            # Mark Open block if enabled
            if self.chart_settings.get('mark_open', False):
                # Find the price level closest to open price
                open_level = round(open_price / tick_size) * tick_size

                if open_level in level_counts and level_counts[open_level] > 0:
                    # Color the first block at open level
                    open_color = self.chart_settings.get('open_color', '#FFFF00')
                    block_height = tick_size * 0.8

                    # Create colored block for open price (first block at this level)
                    open_block = pg.QtWidgets.QGraphicsRectItem(
                        x_position,  # First block position
                        open_level - (block_height / 2),
                        block_width,
                        block_height
                    )

                    open_block.setPen(pg.mkPen(color='#333333', width=0.5))
                    open_block.setBrush(pg.mkBrush(color=open_color, alpha=255))
                    self.chart_widget.addItem(open_block)

            # Mark Close block if enabled
            if self.chart_settings.get('mark_close', False):
                # Find the price level closest to close price
                close_level = round(close_price / tick_size) * tick_size

                if close_level in level_counts and level_counts[close_level] > 0:
                    # Color the last block at close level
                    close_color = self.chart_settings.get('close_color', '#FF00FF')
                    block_height = tick_size * 0.8
                    close_blocks = level_counts[close_level]

                    # Create colored block for close price (last block at this level)
                    close_block_x = x_position + ((close_blocks - 1) * block_width)
                    close_block = pg.QtWidgets.QGraphicsRectItem(
                        close_block_x,  # Last block position at this level
                        close_level - (block_height / 2),
                        block_width,
                        block_height
                    )

                    close_block.setPen(pg.mkPen(color='#333333', width=0.5))
                    close_block.setBrush(pg.mkBrush(color=close_color, alpha=255))
                    self.chart_widget.addItem(close_block)

        except Exception as e:
            logger.error(f"Error adding open/close markers: {str(e)}")

    def add_extensions(self):
        """Add extension lines/zones after all profiles are created."""
        try:
            if not self.profile_data:
                return

            # Process each profile for extensions
            for i, profile in enumerate(self.profile_data):
                # Add VA line extensions
                if (self.chart_settings.get('show_va_lines', True) and
                    self.chart_settings.get('extend_va_lines', False) and
                    profile['va_high'] is not None):

                    self.add_va_extensions(profile, i)

                # Add POC line extensions
                if (self.chart_settings.get('show_poc_line', True) and
                    self.chart_settings.get('extend_poc_lines', False)):

                    self.add_poc_extensions(profile, i)

                # Add single print extensions
                if (self.chart_settings.get('show_single_print', False) and
                    self.chart_settings.get('extend_single_print', False)):

                    self.add_single_print_extensions(profile, i)

                # Add poor high/low extensions
                if (self.chart_settings.get('show_poor_high_low', False) and
                    self.chart_settings.get('extend_poor_high_low', False)):

                    self.add_poor_high_low_extensions(profile, i)

        except Exception as e:
            logger.error(f"Error adding extensions: {str(e)}")

    def find_line_intersection(self, price_level, start_profile_index):
        """Find where a price level intersects with blocks in future profiles."""
        try:
            # Look through profiles after the current one
            for i in range(start_profile_index + 1, len(self.profile_data)):
                future_profile = self.profile_data[i]

                # Check if this price level has blocks in the future profile
                if price_level in future_profile['level_counts'] and future_profile['level_counts'][price_level] > 0:
                    # Found intersection! Return the X position where it hits
                    return future_profile['x_position']

            # No intersection found - extend to a reasonable distance
            if self.profile_data:
                last_profile = self.profile_data[-1]
                return last_profile['x_end'] + 1.0

            return None

        except Exception as e:
            logger.error(f"Error finding line intersection: {str(e)}")
            return None

    def add_va_extensions(self, profile, profile_index):
        """Add Value Area line extensions."""
        try:
            va_high = profile['va_high']
            va_low = profile['va_low']
            level_counts = profile['level_counts']

            # Find intersections for VA high and low
            va_high_intersection = self.find_line_intersection(va_high, profile_index)
            va_low_intersection = self.find_line_intersection(va_low, profile_index)

            if va_high_intersection:
                # Calculate where VA high blocks end (start extension from there)
                va_high_blocks_end = profile['x_position'] + (level_counts.get(va_high, 0) * profile['block_width'])

                # Draw extended VA high line from end of blocks
                va_line_color = self.chart_settings.get('va_line_color', '#FF6B6B')
                va_high_ext_line = pg.PlotDataItem(
                    [va_high_blocks_end, va_high_intersection],
                    [va_high, va_high],
                    pen=pg.mkPen(color=va_line_color, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.chart_widget.addItem(va_high_ext_line)

            if va_low_intersection:
                # Calculate where VA low blocks end (start extension from there)
                va_low_blocks_end = profile['x_position'] + (level_counts.get(va_low, 0) * profile['block_width'])

                # Draw extended VA low line from end of blocks
                va_line_color = self.chart_settings.get('va_line_color', '#FF6B6B')
                va_low_ext_line = pg.PlotDataItem(
                    [va_low_blocks_end, va_low_intersection],
                    [va_low, va_low],
                    pen=pg.mkPen(color=va_line_color, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.chart_widget.addItem(va_low_ext_line)

        except Exception as e:
            logger.error(f"Error adding VA extensions: {str(e)}")

    def add_poc_extensions(self, profile, profile_index):
        """Add POC line extensions."""
        try:
            poc_price = profile['poc_price']
            poc_intersection = self.find_line_intersection(poc_price, profile_index)

            if poc_intersection:
                # Calculate where POC blocks end (start extension from there)
                poc_blocks_end = profile['x_position'] + (profile['poc_count'] * profile['block_width'])

                # Draw extended POC line from end of POC blocks
                poc_line_color = self.chart_settings.get('poc_line_color', '#FFD700')
                poc_ext_line = pg.PlotDataItem(
                    [poc_blocks_end, poc_intersection],
                    [poc_price, poc_price],
                    pen=pg.mkPen(color=poc_line_color, width=2)
                )
                self.chart_widget.addItem(poc_ext_line)

        except Exception as e:
            logger.error(f"Error adding POC extensions: {str(e)}")

    def add_single_print_extensions(self, profile, profile_index):
        """Add single print zone extensions."""
        try:
            # Find single prints in this profile
            level_counts = profile['level_counts']
            prices_with_blocks = sorted([price for price, count in level_counts.items() if count > 0])

            if len(prices_with_blocks) < 5:
                return

            # Use same logic as add_single_prints to find single prints
            single_print_zone_start_from_high = None
            for price in sorted(prices_with_blocks, reverse=True):
                if level_counts[price] >= 2:
                    single_print_zone_start_from_high = price
                    break

            single_print_zone_start_from_low = None
            for price in sorted(prices_with_blocks):
                if level_counts[price] >= 2:
                    single_print_zone_start_from_low = price
                    break

            if (single_print_zone_start_from_high is None or
                single_print_zone_start_from_low is None or
                single_print_zone_start_from_high <= single_print_zone_start_from_low):
                return

            # Find single prints within the valid zone
            single_prints = []
            for price, count in level_counts.items():
                if (count == 1 and
                    price < single_print_zone_start_from_high and
                    price > single_print_zone_start_from_low):
                    single_prints.append(price)

            # Add extensions for each single print
            for single_print_price in single_prints:
                intersection = self.find_line_intersection(single_print_price, profile_index)

                if intersection:
                    # Calculate where single print zone currently ends
                    zone_start = profile['x_position'] + (1 * profile['block_width'])
                    zone_height = 0.8  # Approximate tick size

                    # Draw extended single print zone
                    zone_extension_length = intersection - profile['x_end']
                    if zone_extension_length > 0:
                        zone_rect = pg.QtWidgets.QGraphicsRectItem(
                            profile['x_end'],
                            single_print_price - (zone_height / 2),
                            zone_extension_length,
                            zone_height
                        )

                        single_print_color = self.chart_settings.get('single_print_color', '#00FF00')
                        zone_rect.setPen(pg.mkPen(color=single_print_color, width=2))
                        zone_rect.setBrush(pg.mkBrush(color=single_print_color, alpha=80))
                        self.chart_widget.addItem(zone_rect)

        except Exception as e:
            logger.error(f"Error adding single print extensions: {str(e)}")

    def add_poor_high_low_extensions(self, profile, profile_index):
        """Add poor high/low line extensions."""
        try:
            level_counts = profile['level_counts']
            prices_with_blocks = [price for price, count in level_counts.items() if count > 0]

            if not prices_with_blocks:
                return

            highest_price = max(prices_with_blocks)
            lowest_price = min(prices_with_blocks)

            # Extend poor high if it has 2+ blocks
            if level_counts.get(highest_price, 0) >= 2:
                intersection = self.find_line_intersection(highest_price, profile_index)

                if intersection:
                    # Draw extended poor high line
                    poor_high_low_color = self.chart_settings.get('poor_high_low_color', '#FF9900')
                    poor_high_ext_line = pg.PlotDataItem(
                        [profile['x_end'], intersection],
                        [highest_price, highest_price],
                        pen=pg.mkPen(color=poor_high_low_color, width=2, style=QtCore.Qt.PenStyle.SolidLine)
                    )
                    self.chart_widget.addItem(poor_high_ext_line)

                    # Add arrow at intersection point
                    arrow_size = 0.02
                    arrow_item = pg.ArrowItem(
                        angle=0,
                        tipAngle=30,
                        baseAngle=20,
                        headLen=arrow_size,
                        tailLen=arrow_size,
                        pen=pg.mkPen(color=poor_high_low_color, width=2),
                        brush=pg.mkBrush(color=poor_high_low_color)
                    )
                    arrow_item.setPos(intersection, highest_price)
                    self.chart_widget.addItem(arrow_item)

            # Extend poor low if it has 2+ blocks
            if level_counts.get(lowest_price, 0) >= 2:
                intersection = self.find_line_intersection(lowest_price, profile_index)

                if intersection:
                    # Draw extended poor low line
                    poor_high_low_color = self.chart_settings.get('poor_high_low_color', '#FF9900')
                    poor_low_ext_line = pg.PlotDataItem(
                        [profile['x_end'], intersection],
                        [lowest_price, lowest_price],
                        pen=pg.mkPen(color=poor_high_low_color, width=2, style=QtCore.Qt.PenStyle.SolidLine)
                    )
                    self.chart_widget.addItem(poor_low_ext_line)

                    # Add arrow at intersection point
                    arrow_size = 0.02
                    arrow_item = pg.ArrowItem(
                        angle=0,
                        tipAngle=30,
                        baseAngle=20,
                        headLen=arrow_size,
                        tailLen=arrow_size,
                        pen=pg.mkPen(color=poor_high_low_color, width=2),
                        brush=pg.mkBrush(color=poor_high_low_color)
                    )
                    arrow_item.setPos(intersection, lowest_price)
                    self.chart_widget.addItem(arrow_item)

        except Exception as e:
            logger.error(f"Error adding poor high/low extensions: {str(e)}")

    def show_chart_settings(self):
        """Show the chart settings dialog."""
        try:
            dialog = TPOChartSettingsDialog(self)
            if dialog.exec() == QtWidgets.QDialog.DialogCode.Accepted:
                # Apply settings if user clicked OK
                settings = dialog.get_settings()
                self.apply_chart_settings(settings)
                # Also load the settings into the main tab for immediate use
                self.load_chart_settings()
        except Exception as e:
            logger.error(f"Error showing chart settings: {str(e)}")

    def apply_chart_settings(self, settings):
        """Apply chart settings and refresh the current view."""
        try:
            # Store settings for future use
            self.chart_settings = settings

            # Refresh current view based on profile type
            current_profile = self.profile_button.currentText()
            if current_profile == "Daily Profile":
                self.create_daily_profile()
            elif current_profile == "Weekly Profile":
                self.create_weekly_profile()
            elif current_profile == "Profile":
                self.update_chart()

        except Exception as e:
            logger.error(f"Error applying chart settings: {str(e)}")

    def load_chart_settings(self):
        """Load chart settings from QSettings into the main tab."""
        try:
            settings = QtCore.QSettings('TPOChartsApp', 'TPOChartSettings')

            # Update the chart_settings dictionary with saved values
            self.chart_settings.update({
                'show_va_lines': settings.value('show_va_lines', True, type=bool),
                'extend_va_lines': settings.value('extend_va_lines', False, type=bool),
                'show_poc_line': settings.value('show_poc_line', True, type=bool),
                'extend_poc_lines': settings.value('extend_poc_lines', False, type=bool),
                'show_single_print': settings.value('show_single_print', False, type=bool),
                'extend_single_print': settings.value('extend_single_print', False, type=bool),
                'show_poor_high_low': settings.value('show_poor_high_low', False, type=bool),
                'extend_poor_high_low': settings.value('extend_poor_high_low', False, type=bool),
                'mark_initial_balance': settings.value('mark_initial_balance', False, type=bool),
                'mark_open': settings.value('mark_open', False, type=bool),
                'mark_close': settings.value('mark_close', False, type=bool),
                'show_100_ib_stdev': settings.value('show_100_ib_stdev', False, type=bool),
                'show_50_ib_stdev': settings.value('show_50_ib_stdev', False, type=bool),
                'split_sessions': settings.value('split_sessions', False, type=bool),
                'va_percentage': settings.value('va_percentage', 70, type=int)
            })

        except Exception as e:
            logger.error(f"Error loading chart settings: {str(e)}")


# Style sheet for TPO Chart Settings Dialog (same as market_odds.py)
TPO_STYLE_SHEET = """
/* Overall Dialog & Widgets */
QDialog {
    background-color: #2b2b2b;
    color: #e0e0e0;
    font-family: "Segoe UI", sans-serif;
}

QGroupBox {
    border: 1px solid #555;
    border-radius: 6px;
    padding: 8px;
    margin-top: 10px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    font-size: 10pt;
    font-weight: bold;
}

QLabel {
    font-size: 10pt;
}

/* Buttons */
QPushButton {
    background-color: #4a4a4a;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
}
QPushButton:hover {
    background-color: #5a5a5a;
}

/* Input Fields */
QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: #3a3a3a;
    color: #e0e0e0;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 3px;
}

/* Scroll area */
QScrollArea {
    border: none;
}

/* Checkbox Style */
QCheckBox {
    spacing: 8px;
    font-size: 10pt;
}

QCheckBox::indicator {
    width: 20px;
    height: 20px;
    border: 2px solid #000000;
    border-radius: 4px;
    background-color: #808080;
}

QCheckBox::indicator:unchecked:hover {
    border: 2px solid #000000;
    background-color: #909090;
}

QCheckBox::indicator:checked {
    background-color: #FFFFFF;
    border: 2px solid #000000;
}

QCheckBox::indicator:checked:hover {
    background-color: #F0F0F0;
    border: 2px solid #000000;
}

/* Tab Widget */
QTabWidget::pane {
    border: 1px solid #555;
    background-color: #2b2b2b;
}

QTabBar::tab {
    background-color: #3a3a3a;
    color: #e0e0e0;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #4a4a4a;
}

QTabBar::tab:hover {
    background-color: #5a5a5a;
}
"""


class TPOChartSettingsDialog(QtWidgets.QDialog):
    """Dialog for configuring TPO chart settings."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet(TPO_STYLE_SHEET)
        self.setWindowTitle("TPO Chart Settings")
        self.setModal(True)
        # Make the dialog resizable
        self.setWindowFlags(self.windowFlags() | QtCore.Qt.WindowType.WindowMaximizeButtonHint | QtCore.Qt.WindowType.WindowMinimizeButtonHint)

        # Default colors for TPO settings
        self.default_colors = {
            'poc_color': '#FFFFFF',
            'va_color': '#CCCCCC',
            'outside_va_color': '#666666',
            'va_line_color': '#FF6B6B',
            'poc_line_color': '#FFD700',
            'single_print_color': '#00FF00',
            'poor_high_low_color': '#FF9900',
            'initial_balance_color': '#00FFFF',
            'open_color': '#FFFF00',
            'close_color': '#FF00FF',
            'ib_100_stdev_color': '#9370DB',
            'ib_50_stdev_color': '#DDA0DD'
        }

        self.init_ui()
        self.load_settings()

    def init_ui(self):
        """Initialize the dialog UI using market_odds.py structure."""
        main_layout = QtWidgets.QVBoxLayout(self)

        # Create tab widget for different settings categories
        tab_widget = QtWidgets.QTabWidget()

        # Tab 1: TPO Settings
        tpo_settings_tab = QtWidgets.QWidget()
        tpo_settings_layout = QtWidgets.QVBoxLayout(tpo_settings_tab)

        # Create color settings group
        color_group = QtWidgets.QGroupBox("TPO Colors")
        color_layout = QtWidgets.QFormLayout(color_group)

        # Create color buttons
        self.poc_color_btn = self.createColorButton(self.default_colors['poc_color'])
        self.va_color_btn = self.createColorButton(self.default_colors['va_color'])
        self.outside_va_color_btn = self.createColorButton(self.default_colors['outside_va_color'])
        self.va_line_color_btn = self.createColorButton(self.default_colors['va_line_color'])
        self.poc_line_color_btn = self.createColorButton(self.default_colors['poc_line_color'])
        self.single_print_color_btn = self.createColorButton(self.default_colors['single_print_color'])
        self.poor_high_low_color_btn = self.createColorButton(self.default_colors['poor_high_low_color'])
        self.initial_balance_color_btn = self.createColorButton(self.default_colors['initial_balance_color'])
        self.open_color_btn = self.createColorButton(self.default_colors['open_color'])
        self.close_color_btn = self.createColorButton(self.default_colors['close_color'])
        self.ib_100_stdev_color_btn = self.createColorButton(self.default_colors['ib_100_stdev_color'])
        self.ib_50_stdev_color_btn = self.createColorButton(self.default_colors['ib_50_stdev_color'])

        # Add color buttons to layout
        color_layout.addRow("POC Color:", self.poc_color_btn)
        color_layout.addRow("Value Area Color:", self.va_color_btn)
        color_layout.addRow("Outside VA Color:", self.outside_va_color_btn)
        color_layout.addRow("VA Line Color:", self.va_line_color_btn)
        color_layout.addRow("POC Line Color:", self.poc_line_color_btn)
        color_layout.addRow("Single Print Color:", self.single_print_color_btn)
        color_layout.addRow("Poor High/Low Color:", self.poor_high_low_color_btn)
        color_layout.addRow("Initial Balance Color:", self.initial_balance_color_btn)
        color_layout.addRow("Open Color:", self.open_color_btn)
        color_layout.addRow("Close Color:", self.close_color_btn)
        color_layout.addRow("IB 100% StDev Color:", self.ib_100_stdev_color_btn)
        color_layout.addRow("IB 50% StDev Color:", self.ib_50_stdev_color_btn)

        # Add reset button
        reset_btn = QtWidgets.QPushButton("Reset Colors")
        reset_btn.clicked.connect(self.reset_colors)
        color_layout.addRow(reset_btn)

        # Create horizontal layout for side-by-side groups
        groups_layout = QtWidgets.QHBoxLayout()

        # Add color group to left side
        groups_layout.addWidget(color_group)

        # Add Display Options group to right side
        display_group = QtWidgets.QGroupBox("Display Options")
        display_layout = QtWidgets.QVBoxLayout(display_group)

        # Show VA Lines checkbox with extend option
        self.show_va_lines_check = QtWidgets.QCheckBox("Show Value Area Lines")
        self.show_va_lines_check.setChecked(True)
        self.show_va_lines_check.setToolTip("Show Value Area boundary lines")
        display_layout.addWidget(self.show_va_lines_check)

        # Extend VA Lines checkbox (indented)
        self.extend_va_lines_check = QtWidgets.QCheckBox("      Extend Lines")
        self.extend_va_lines_check.setChecked(False)
        self.extend_va_lines_check.setToolTip("Extend VA lines until they hit a block in future profiles")
        display_layout.addWidget(self.extend_va_lines_check)

        # Show POC Line checkbox with extend option
        self.show_poc_line_check = QtWidgets.QCheckBox("Show POC Line")
        self.show_poc_line_check.setChecked(True)
        self.show_poc_line_check.setToolTip("Show Point of Control line")
        display_layout.addWidget(self.show_poc_line_check)

        # Extend POC Lines checkbox (indented)
        self.extend_poc_lines_check = QtWidgets.QCheckBox("      Extend Lines")
        self.extend_poc_lines_check.setChecked(False)
        self.extend_poc_lines_check.setToolTip("Extend POC lines until they hit a block in future profiles")
        display_layout.addWidget(self.extend_poc_lines_check)

        # Show Single Print checkbox with extend option
        self.show_single_print_check = QtWidgets.QCheckBox("Show Single Print")
        self.show_single_print_check.setChecked(False)
        self.show_single_print_check.setToolTip("Highlight single print zones (areas with only 1 block inside the TPO)")
        display_layout.addWidget(self.show_single_print_check)

        # Extend Single Print checkbox (indented)
        self.extend_single_print_check = QtWidgets.QCheckBox("      Extend Lines")
        self.extend_single_print_check.setChecked(False)
        self.extend_single_print_check.setToolTip("Extend single print zones until they hit a block in future profiles")
        display_layout.addWidget(self.extend_single_print_check)

        # Show Poor High/Low checkbox with extend option
        self.show_poor_high_low_check = QtWidgets.QCheckBox("Show Poor High/Low")
        self.show_poor_high_low_check.setChecked(False)
        self.show_poor_high_low_check.setToolTip("Show poor highs/lows (extremes with 2+ blocks)")
        display_layout.addWidget(self.show_poor_high_low_check)

        # Extend Poor High/Low checkbox (indented)
        self.extend_poor_high_low_check = QtWidgets.QCheckBox("      Extend Lines")
        self.extend_poor_high_low_check.setChecked(False)
        self.extend_poor_high_low_check.setToolTip("Extend poor high/low lines until they hit a block in future profiles")
        display_layout.addWidget(self.extend_poor_high_low_check)

        # Mark Initial Balance checkbox
        self.mark_initial_balance_check = QtWidgets.QCheckBox("Mark Initial Balance")
        self.mark_initial_balance_check.setChecked(False)
        self.mark_initial_balance_check.setToolTip("Show Initial Balance (first hour opening range) as a bar to the left of TPO")
        display_layout.addWidget(self.mark_initial_balance_check)

        # Mark Open checkbox
        self.mark_open_check = QtWidgets.QCheckBox("Mark Open")
        self.mark_open_check.setChecked(False)
        self.mark_open_check.setToolTip("Highlight the opening price block")
        display_layout.addWidget(self.mark_open_check)

        # Mark Close checkbox
        self.mark_close_check = QtWidgets.QCheckBox("Mark Close")
        self.mark_close_check.setChecked(False)
        self.mark_close_check.setToolTip("Highlight the closing price block")
        display_layout.addWidget(self.mark_close_check)

        # Show 100% IB StDev checkbox
        self.show_100_ib_stdev_check = QtWidgets.QCheckBox("Show 100% IB StDev")
        self.show_100_ib_stdev_check.setChecked(False)
        self.show_100_ib_stdev_check.setToolTip("Show 1 standard deviation lines above and below Initial Balance")
        display_layout.addWidget(self.show_100_ib_stdev_check)

        # Show 50% IB StDev checkbox
        self.show_50_ib_stdev_check = QtWidgets.QCheckBox("Show 50% IB StDev")
        self.show_50_ib_stdev_check.setChecked(False)
        self.show_50_ib_stdev_check.setToolTip("Show 0.5 standard deviation lines above and below Initial Balance")
        display_layout.addWidget(self.show_50_ib_stdev_check)

        # VA Percentage
        va_percentage_layout = QtWidgets.QHBoxLayout()
        va_percentage_label = QtWidgets.QLabel("Value Area Percentage:")
        self.va_percentage_spin = QtWidgets.QSpinBox()
        self.va_percentage_spin.setRange(50, 90)
        self.va_percentage_spin.setValue(70)
        self.va_percentage_spin.setSuffix("%")
        self.va_percentage_spin.setToolTip("Percentage of trading activity included in Value Area")
        va_percentage_layout.addWidget(va_percentage_label)
        va_percentage_layout.addWidget(self.va_percentage_spin)
        va_percentage_layout.addStretch()
        display_layout.addLayout(va_percentage_layout)

        # Add display group to right side of horizontal layout
        groups_layout.addWidget(display_group)

        # Add the horizontal layout to the TPO settings tab
        tpo_settings_layout.addLayout(groups_layout)

        # Add Time Settings section
        time_settings_group = QtWidgets.QGroupBox("Time Settings")
        time_settings_layout = QtWidgets.QFormLayout(time_settings_group)

        # Session Start Time
        self.session_start_time = QtWidgets.QTimeEdit()
        self.session_start_time.setTime(QtCore.QTime(18, 0))  # Default 6:00 PM
        self.session_start_time.setToolTip("Session start time for futures/forex (default: 6:00 PM)")
        time_settings_layout.addRow("Session Start Time:", self.session_start_time)

        # Session End Time
        self.session_end_time = QtWidgets.QTimeEdit()
        self.session_end_time.setTime(QtCore.QTime(17, 0))  # Default 5:00 PM
        self.session_end_time.setToolTip("Session end time for TPO day (default: 5:00 PM)")
        time_settings_layout.addRow("Session End Time:", self.session_end_time)

        # Time Zone
        self.timezone_combo = QtWidgets.QComboBox()
        self.timezone_combo.addItems([
            "Eastern Time (ET)",
            "Central Time (CT)",
            "Mountain Time (MT)",
            "Pacific Time (PT)",
            "UTC",
            "London (GMT)",
            "Tokyo (JST)"
        ])
        self.timezone_combo.setCurrentText("Eastern Time (ET)")
        self.timezone_combo.setToolTip("Time zone for session times")
        time_settings_layout.addRow("Time Zone:", self.timezone_combo)

        # Auto-detect instrument checkbox
        self.auto_detect_instrument_check = QtWidgets.QCheckBox("Auto-detect Instrument Type")
        self.auto_detect_instrument_check.setChecked(True)
        self.auto_detect_instrument_check.setToolTip("Automatically detect instrument type and apply appropriate session logic")
        time_settings_layout.addRow(self.auto_detect_instrument_check)

        # Split Sessions checkbox
        self.split_sessions_check = QtWidgets.QCheckBox("Split Sessions")
        self.split_sessions_check.setChecked(False)
        self.split_sessions_check.setToolTip("Create separate TPO profiles for ETH (Extended Trading Hours) and RTH (Regular Trading Hours)")
        time_settings_layout.addRow(self.split_sessions_check)

        # Add time settings group to the layout
        tpo_settings_layout.addWidget(time_settings_group)
        tpo_settings_layout.addStretch()

        # Add TPO settings tab to tab widget
        tab_widget.addTab(tpo_settings_tab, "TPO Settings")

        # Add tab widget to main layout
        main_layout.addWidget(tab_widget)

        # Bottom buttons
        bottom_layout = QtWidgets.QHBoxLayout()
        bottom_layout.addStretch()

        ok_btn = QtWidgets.QPushButton("OK")
        ok_btn.clicked.connect(self.on_ok_clicked)
        cancel_btn = QtWidgets.QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        bottom_layout.addWidget(ok_btn)
        bottom_layout.addWidget(cancel_btn)
        main_layout.addLayout(bottom_layout)

    def on_ok_clicked(self):
        """Handle OK button click - save settings and accept dialog."""
        self.save_settings()
        self.accept()

    def createColorButton(self, initial_color):
        """Create a color picker button (same as market_odds.py)."""
        btn = QtWidgets.QPushButton()
        btn.setFixedSize(100, 30)
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {initial_color};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        btn.clicked.connect(lambda: self.choose_color(btn))
        return btn

    def choose_color(self, button):
        """Open color picker dialog (same as market_odds.py)."""
        current_color = button.styleSheet().split("background-color:")[1].split(";")[0].strip()
        color = QtWidgets.QColorDialog.getColor(QtGui.QColor(current_color), self)
        if color.isValid():
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color.name()};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

    def get_chart_colors(self):
        """Get current chart colors (same structure as market_odds.py)."""
        def extract_color(button):
            """Extract color from button stylesheet safely."""
            try:
                style = button.styleSheet()
                if "background-color:" in style:
                    return style.split("background-color:")[1].split(";")[0].strip()
                return "#FFFFFF"  # Default fallback
            except:
                return "#FFFFFF"  # Default fallback

        return {
            'poc_color': extract_color(self.poc_color_btn),
            'va_color': extract_color(self.va_color_btn),
            'outside_va_color': extract_color(self.outside_va_color_btn),
            'va_line_color': extract_color(self.va_line_color_btn),
            'poc_line_color': extract_color(self.poc_line_color_btn),
            'single_print_color': extract_color(self.single_print_color_btn),
            'poor_high_low_color': extract_color(self.poor_high_low_color_btn),
            'initial_balance_color': extract_color(self.initial_balance_color_btn),
            'open_color': extract_color(self.open_color_btn),
            'close_color': extract_color(self.close_color_btn),
            'ib_100_stdev_color': extract_color(self.ib_100_stdev_color_btn),
            'ib_50_stdev_color': extract_color(self.ib_50_stdev_color_btn)
        }

    def reset_colors(self):
        """Reset all colors to defaults (same structure as market_odds.py)."""
        self.poc_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['poc_color']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.va_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['va_color']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.outside_va_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['outside_va_color']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.va_line_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['va_line_color']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.poc_line_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['poc_line_color']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.single_print_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['single_print_color']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.poor_high_low_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['poor_high_low_color']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.initial_balance_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['initial_balance_color']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.open_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['open_color']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.close_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['close_color']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.ib_100_stdev_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['ib_100_stdev_color']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)
        self.ib_50_stdev_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.default_colors['ib_50_stdev_color']};
                border: 1px solid #555;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #777;
            }}
        """)

    def get_settings(self):
        """Get current settings from the dialog."""
        colors = self.get_chart_colors()
        return {
            'poc_color': colors['poc_color'],
            'va_color': colors['va_color'],
            'outside_va_color': colors['outside_va_color'],
            'va_line_color': colors['va_line_color'],
            'poc_line_color': colors['poc_line_color'],
            'single_print_color': colors['single_print_color'],
            'poor_high_low_color': colors['poor_high_low_color'],
            'initial_balance_color': colors['initial_balance_color'],
            'open_color': colors['open_color'],
            'close_color': colors['close_color'],
            'ib_100_stdev_color': colors['ib_100_stdev_color'],
            'ib_50_stdev_color': colors['ib_50_stdev_color'],
            'show_va_lines': self.show_va_lines_check.isChecked(),
            'extend_va_lines': self.extend_va_lines_check.isChecked(),
            'show_poc_line': self.show_poc_line_check.isChecked(),
            'extend_poc_lines': self.extend_poc_lines_check.isChecked(),
            'show_single_print': self.show_single_print_check.isChecked(),
            'extend_single_print': self.extend_single_print_check.isChecked(),
            'show_poor_high_low': self.show_poor_high_low_check.isChecked(),
            'extend_poor_high_low': self.extend_poor_high_low_check.isChecked(),
            'mark_initial_balance': self.mark_initial_balance_check.isChecked(),
            'mark_open': self.mark_open_check.isChecked(),
            'mark_close': self.mark_close_check.isChecked(),
            'show_100_ib_stdev': self.show_100_ib_stdev_check.isChecked(),
            'show_50_ib_stdev': self.show_50_ib_stdev_check.isChecked(),
            'va_percentage': self.va_percentage_spin.value(),
            'session_start_time': self.session_start_time.time().toString("hh:mm"),
            'session_end_time': self.session_end_time.time().toString("hh:mm"),
            'timezone': self.timezone_combo.currentText(),
            'auto_detect_instrument': self.auto_detect_instrument_check.isChecked(),
            'split_sessions': self.split_sessions_check.isChecked()
        }

    def load_settings(self):
        """Load settings from QSettings."""
        settings = QtCore.QSettings('TPOChartsApp', 'TPOChartSettings')

        # Load colors
        if settings.contains('poc_color'):
            color = settings.value('poc_color', '#FFFFFF')
            self.poc_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

        if settings.contains('va_color'):
            color = settings.value('va_color', '#CCCCCC')
            self.va_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

        if settings.contains('outside_va_color'):
            color = settings.value('outside_va_color', '#666666')
            self.outside_va_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

        if settings.contains('va_line_color'):
            color = settings.value('va_line_color', '#FF6B6B')
            self.va_line_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

        if settings.contains('poc_line_color'):
            color = settings.value('poc_line_color', '#FFD700')
            self.poc_line_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

        if settings.contains('single_print_color'):
            color = settings.value('single_print_color', '#00FF00')
            self.single_print_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

        if settings.contains('poor_high_low_color'):
            color = settings.value('poor_high_low_color', '#FF9900')
            self.poor_high_low_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

        if settings.contains('initial_balance_color'):
            color = settings.value('initial_balance_color', '#00FFFF')
            self.initial_balance_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

        if settings.contains('open_color'):
            color = settings.value('open_color', '#FFFF00')
            self.open_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

        if settings.contains('close_color'):
            color = settings.value('close_color', '#FF00FF')
            self.close_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

        if settings.contains('ib_100_stdev_color'):
            color = settings.value('ib_100_stdev_color', '#9370DB')
            self.ib_100_stdev_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

        if settings.contains('ib_50_stdev_color'):
            color = settings.value('ib_50_stdev_color', '#DDA0DD')
            self.ib_50_stdev_color_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 1px solid #555;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    border: 1px solid #777;
                }}
            """)

        # Load display options
        self.show_va_lines_check.setChecked(settings.value('show_va_lines', True, type=bool))
        self.extend_va_lines_check.setChecked(settings.value('extend_va_lines', False, type=bool))
        self.show_poc_line_check.setChecked(settings.value('show_poc_line', True, type=bool))
        self.extend_poc_lines_check.setChecked(settings.value('extend_poc_lines', False, type=bool))
        self.show_single_print_check.setChecked(settings.value('show_single_print', False, type=bool))
        self.extend_single_print_check.setChecked(settings.value('extend_single_print', False, type=bool))
        self.show_poor_high_low_check.setChecked(settings.value('show_poor_high_low', False, type=bool))
        self.extend_poor_high_low_check.setChecked(settings.value('extend_poor_high_low', False, type=bool))
        self.mark_initial_balance_check.setChecked(settings.value('mark_initial_balance', False, type=bool))
        self.mark_open_check.setChecked(settings.value('mark_open', False, type=bool))
        self.mark_close_check.setChecked(settings.value('mark_close', False, type=bool))
        self.show_100_ib_stdev_check.setChecked(settings.value('show_100_ib_stdev', False, type=bool))
        self.show_50_ib_stdev_check.setChecked(settings.value('show_50_ib_stdev', False, type=bool))
        self.va_percentage_spin.setValue(settings.value('va_percentage', 70, type=int))

        # Load time settings
        session_start_str = settings.value('session_start_time', '18:00')
        session_start_time = QtCore.QTime.fromString(session_start_str, "hh:mm")
        if session_start_time.isValid():
            self.session_start_time.setTime(session_start_time)

        session_end_str = settings.value('session_end_time', '17:00')
        session_end_time = QtCore.QTime.fromString(session_end_str, "hh:mm")
        if session_end_time.isValid():
            self.session_end_time.setTime(session_end_time)

        timezone_text = settings.value('timezone', 'Eastern Time (ET)')
        index = self.timezone_combo.findText(timezone_text)
        if index >= 0:
            self.timezone_combo.setCurrentIndex(index)

        self.auto_detect_instrument_check.setChecked(settings.value('auto_detect_instrument', True, type=bool))
        self.split_sessions_check.setChecked(settings.value('split_sessions', False, type=bool))

    def save_settings(self):
        """Save settings to QSettings."""
        settings = QtCore.QSettings('TPOChartsApp', 'TPOChartSettings')

        # Save colors
        colors = self.get_chart_colors()
        for key, value in colors.items():
            settings.setValue(key, value)

        # Save display options
        settings.setValue('show_va_lines', self.show_va_lines_check.isChecked())
        settings.setValue('extend_va_lines', self.extend_va_lines_check.isChecked())
        settings.setValue('show_poc_line', self.show_poc_line_check.isChecked())
        settings.setValue('extend_poc_lines', self.extend_poc_lines_check.isChecked())
        settings.setValue('show_single_print', self.show_single_print_check.isChecked())
        settings.setValue('extend_single_print', self.extend_single_print_check.isChecked())
        settings.setValue('show_poor_high_low', self.show_poor_high_low_check.isChecked())
        settings.setValue('extend_poor_high_low', self.extend_poor_high_low_check.isChecked())
        settings.setValue('mark_initial_balance', self.mark_initial_balance_check.isChecked())
        settings.setValue('mark_open', self.mark_open_check.isChecked())
        settings.setValue('mark_close', self.mark_close_check.isChecked())
        settings.setValue('show_100_ib_stdev', self.show_100_ib_stdev_check.isChecked())
        settings.setValue('show_50_ib_stdev', self.show_50_ib_stdev_check.isChecked())
        settings.setValue('va_percentage', self.va_percentage_spin.value())

        # Save time settings
        settings.setValue('session_start_time', self.session_start_time.time().toString("hh:mm"))
        settings.setValue('session_end_time', self.session_end_time.time().toString("hh:mm"))
        settings.setValue('timezone', self.timezone_combo.currentText())
        settings.setValue('auto_detect_instrument', self.auto_detect_instrument_check.isChecked())
        settings.setValue('split_sessions', self.split_sessions_check.isChecked())

        # Sync settings to disk
        settings.sync()
